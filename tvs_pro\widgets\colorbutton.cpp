#include "colorbutton.h"

#include <QColorDialog>
#include <QHBoxLayout>
#include <QLabel>
#include <QPainter>

ColorButton::ColorButton(QWidget * parent, QColor color)
    : QPushButton(parent), m_selectedColor(std::move(color))
{
    m_allowSetAlpha = false;
    connect( this, SIGNAL(clicked()), this, SLOT(btnClicked()) );

    m_label = new QLabel();

    QPixmap pix(100, 100);
    pix.fill(color);

    m_label->setScaledContents(true);
    m_label->setPixmap(pix);

    auto btnLayout = new QHBoxLayout;
    btnLayout->addWidget(m_label);
    btnLayout->setContentsMargins(0, 0, 0, 0);
    setLayout(btnLayout);
}

void ColorButton::setColor( const QColor& color )
{
    // After the constructor is called, UIC-generated code calls setLabel, so we overwrite it here
    setText("");

    m_selectedColor = color;

    QPixmap pix(100, 100);
    pix.fill(color);

    m_label->setScaledContents(true);
    m_label->setPixmap(pix);
    // update();

    emit onColorChanged(m_selectedColor);
}

void ColorButton::allowSetAlpha(bool allow)
{
    m_allowSetAlpha = allow;
}

QColor ColorButton::color() const
{
    return m_selectedColor;
}

void ColorButton::btnClicked()
{
    QColor newcolor = QColorDialog::getColor( color(),
                      this,
                      text(),
                      m_allowSetAlpha ?
                      QColorDialog::ShowAlphaChannel :
                      QColorDialog::ColorDialogOptions() );

    if ( newcolor.isValid() )
        setColor( newcolor );
}
