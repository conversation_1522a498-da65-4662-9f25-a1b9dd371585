#ifndef CPPEX_H
#define CPPEX_H
/******************************************************************************
  File Name     : cppex.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 基础工具函数
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include "orderedmap.h"

#include <QSharedPointer>
#include <QUuid>

///
/// \brief operator <<
/// \param stream
/// \param QMap
/// \return QDataStream
///
template <class Key, class T>
QDataStream & operator<<(QDataStream &stream, const QMap<Key, QSharedPointer<T>> & map)
{
    stream << map.size();
    for (auto it = map.constBegin(); it != map.constEnd(); ++it)
    {
        stream << it.key() << *it.value();
    }
    return stream;
}

template <class Key, class T>
QDataStream & operator>>(QDataStream &stream, QMap<Key, QSharedPointer<T>> & map)
{
    qsizetype size;
    stream >> size;
    map.clear();
    for (int i = 0; i < size; ++i)
    {
        Key key;
        QSharedPointer<T> value(new T());
        stream >> key >> *value;
        map.insert(key, value);
    }
    return stream;
}

///
/// \brief operator <<
/// \param stream
/// \param QHash
/// \return QDataStream
///
template <class Key, class T>
QDataStream & operator<<(QDataStream &stream, const QHash<Key, QSharedPointer<T>> & map)
{
    stream << map.size();
    for (auto it = map.constBegin(); it != map.constEnd(); ++it)
    {
        stream << it.key() << *it.value();
    }
    return stream;
}

template <class Key, class T>
QDataStream & operator>>(QDataStream &stream, QHash<Key, QSharedPointer<T>> & map)
{
    qsizetype size;
    stream >> size;
    map.clear();
    for (int i = 0; i < size; ++i)
    {
        Key key;
        QSharedPointer<T> value(new T());
        stream >> key >> *value;
        map.insert(key, value);
    }
    return stream;
}

///
/// \brief operator <<
/// \param stream
/// \param OrderedMap
/// \return QDataStream
///
template <class Key, class T>
QDataStream & operator<<(QDataStream &stream, const OrderedMap<Key, QSharedPointer<T>> & map)
{
    stream << map.size();
    for (auto it = map.begin(); it != map.end(); ++it)
    {
        stream << it.key() << *it.value();
    }
    return stream;
}

template <class Key, class T>
QDataStream & operator>>(QDataStream &stream, OrderedMap<Key, QSharedPointer<T>> & map)
{
    qsizetype size;
    stream >> size;
    map.clear();
    for (int i = 0; i < size; ++i)
    {
        Key key;
        QSharedPointer<T> value(new T());
        stream >> key >> *value;
        map.insert(key, value);
        // map[key] = value;
    }
    return stream;
}


inline QString newUuid()
{
    QString uuidStr = QUuid::createUuid().toString();
    QString str = uuidStr.remove("-").remove("{").remove("}");
    return str;
}


#endif // CPPEX_H
