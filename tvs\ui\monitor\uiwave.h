#ifndef UIWAVE_H
#define UIWAVE_H

/******************************************************************************
  File Name     : uidata.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备查看的波形视图
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
#include <QTimer>
class PlotWidget;

class UiWave : public QWidget
{
    Q_OBJECT
public:
    explicit UiWave(const QString& title, QWidget *parent = nullptr);

private slots:
    void realtimeDataSlot();

protected:
    QTimer dataTimer;
    PlotWidget* customPlot;

signals:
};

#endif // UIWAVE_H
