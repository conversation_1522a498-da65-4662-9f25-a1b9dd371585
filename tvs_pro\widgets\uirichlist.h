#ifndef UIRICHLIST_H
#define UIRICHLIST_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 带图标、标题、描述的列表控件
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QPushButton>
#include <QWidget>

class QVBoxLayout;
class LabelTitle;
class LabelInfo;


class UiRichListItem : public QPushButton
{
    Q_OBJECT
public:
    explicit UiRichListItem(const QString &title, const QString &info, const QString &picFile, QWidget *parent = nullptr);

    QString title() const;
    void setTitle(const QString &newTitle);

    QString info() const;
    void setInfo(const QString &newInfo);

protected:
    LabelTitle *m_lbTitle;
    LabelInfo *m_lbInfo;

signals:
};


class UiRichList : public QWidget
{
    Q_OBJECT
public:
    explicit UiRichList(QWidget *parent = nullptr);

    void addItem( const QString &title, const QString &info, const QString &picFile);
    void clearAll();

signals:
    void item_clicked(QString, QString);

protected:
    QVBoxLayout * m_main_box;

};

class QTreeWidget;

class RecentFilesWidget : public QWidget
{
public:
    RecentFilesWidget(QWidget *parent = nullptr);

    void addFile(const QString& filename, const QString& filepath);

private:
    QTreeWidget *m_treeWidget;
};

#endif // UIRICHLIST_H
