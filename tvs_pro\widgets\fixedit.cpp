#include "fixedit.h"

#include <QHBoxLayout>
#include <QLabel>

FixEdit::FixEdit(const QString &text, bool prefix, QWidget *parent)
    : QLineEdit(parent)
{
    m_label = new QLabel(text);
    addLabel(prefix);
}

FixEdit::FixEdit(const QIcon &icon, bool prefix, QWidget *parent)
    : QLineEdit(parent)
{
    m_label = new QLabel();
    QPixmap pic = icon.pixmap(icon.actualSize(QSize(32, 32)));
    m_label->setPixmap(pic);
    addLabel(prefix);
}

void FixEdit::addLabel(bool prefix)
{
    auto btnLayout = new QHBoxLayout;
    if(prefix)
    {
        btnLayout->addWidget(m_label);
        btnLayout->addStretch();
        btnLayout->setAlignment(Qt::AlignLeft);
        // 设置输入区域的范围
        setTextMargins(m_label->sizeHint().width(), 0, 0, 0);
    }
    else
    {
        btnLayout->addStretch();
        btnLayout->addWidget(m_label);
        btnLayout->setAlignment(Qt::AlignRight);
        // 设置输入区域的范围
        setTextMargins(0, 0, m_label->sizeHint().width(), 0);
    }

    btnLayout->setContentsMargins(0, 0, 0, 0);
    setLayout(btnLayout);
}

