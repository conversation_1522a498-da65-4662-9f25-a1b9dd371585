#include "qsplitter.h"
#include "uimonitorstatus.h"
#include "ui/monitor/uidatatable.h"
#include "ui/monitor/monitor.h"
#include "ui/monitor/uiwave.h"
#include "plot_widget.h"
#include "core/Graph.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>

UiMonitor::UiMonitor(const QString &id, QWidget *parent)
    : QWidget{parent}, m_id(id)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 分割窗口
    m_splitter = new QSplitter(Qt::Vertical, this);
    m_splitter->setContentsMargins(0, 0, 0, 0);

    mainLayout->addWidget(m_splitter);

    m_splitter_top = new QSplitter(Qt::Horizontal, this);
    m_splitter_top->setContentsMargins(0, 0, 0, 0);

    // 角速度
    WaveConfig config = {"扭振角速度", "时间/(s)", "扭振角速度/(rad/s)"};
    m_uiWaveAngularSpeed = new UiWave(config);

    // 频谱
    config = {"频谱", "频率/(Hz)", "幅值/(dB)"};
    m_uiWaveSpectrum = new UiWave(config);
    m_splitter_top->addWidget(m_uiWaveAngularSpeed);
    m_splitter_top->addWidget(m_uiWaveSpectrum);

    // 脉冲
    config = {"脉冲", "时间/(s)", "脉冲幅值/(V)"};
    m_uiWavePulse = new UiWave(config);

    m_splitter->addWidget(m_splitter_top);
    m_splitter->addWidget(m_uiWavePulse);

    int h = m_splitter->size().height();
    m_splitter->setSizes({h / 2, h / 2});

    // 不允许折叠
    m_splitter->setCollapsible(0, false);
    m_splitter->setCollapsible(1, false);

    m_uiMonitorStatus = new UiMonitorStatus();
    mainLayout->addWidget(m_uiMonitorStatus);

    auto lb1 = new QLabel("开始时间: 00:00:00");
    lb1->setAlignment(Qt::AlignCenter);
    m_uiMonitorStatus->m_layout->addWidget(lb1, 1);

    auto line = new QFrame();
    line->setFrameShape(QFrame::VLine);

    m_uiMonitorStatus->m_layout->addWidget(line);

    auto txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/warn.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">设备异常告警:已断开连接</td>
</table>
)";

    m_info = new QLabel(txt);
    m_info->setAlignment(Qt::AlignCenter);
    m_uiMonitorStatus->m_layout->addWidget(m_info);

    connect(MainWindow::instance, &MainWindow::equipmentConnectChanged, [this](QString id, bool bconnect)
            {
        if( m_id == id )
        {
            QString txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/%1.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">%2</td>
</table>
)";

            if(bconnect)
            {
                txt = txt.arg("success", "PA设备已连接");
            }
            else
            {
                txt = txt.arg("warn", "设备异常告警:已断开连接");
            }

            m_info->setText(txt);
        } });

    auto dataSource = new DaqDataSource();
    auto graph = new Graph(dataSource);
    // graph->refreshData(true);
    m_uiWaveAngularSpeed->get_plot_widget()->addGraph(graph);

    connect(&dataTimer, SIGNAL(timeout()), this, SLOT(timeToAddData()));
    dataTimer.start(1000);
}

bool UiMonitor::isUiDataVisiable() const
{
    // return m_uiDataTable->isVisible();
    return true;
}

void UiMonitor::setUiDataVisiable(bool visible)
{
    // m_uiDataTable->setVisible(visible);
}

void UiMonitor::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    m_splitter_top->setMinimumSize(100, event->size().height() / 5);
    m_uiWavePulse->setMinimumSize(100, event->size().height() / 5);
    // m_uiDataTable->setMinimumSize(100, event->size().height()/5);
}


void UiMonitor::timeToAddData()
{
    static QElapsedTimer timer; // 替换 QTime
    static bool firstRun = true;

    if (firstRun)
    {
        timer.start(); // 第一次运行时启动计时器
        firstRun = false;
    }

    double key = timer.elapsed() / 1000.0; // 开始到现在的时间，单位秒
    static double lastPointKey = 0;

    double fsample = 1000;           // 采样率，单位Hz
    double interval = 1.0 / fsample; // 采样间隔，单位秒

    int n = std::ceil((key - lastPointKey) / interval);
    if (n == 0)
        return;

    QVector<double> keys, values;
    for (int i = 0; i < n; ++i)
    {
        keys.append(lastPointKey + i * interval);
        values.append(qSin(lastPointKey + i * 100 * interval) );

        // QRandomGenerator::global()->generateDouble() * 0.01
    }

    m_uiWaveAngularSpeed->get_plot_widget()->getPlot()->graph(0)->addData(keys, values, true, 10000);

    // 记录当前时刻
    lastPointKey = key;

}
