#include "qsplitter.h"
#include "uimonitorstatus.h"
#include "ui/monitor/uidatatable.h"
#include "ui/monitor/monitor.h"
#include "ui/monitor/uiwave.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>

UiMonitor::UiMonitor(const QString& id, QWidget *parent)
    : QWidget{parent}, m_id(id)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0,0,0,0);

    // 分割窗口
    m_splitter = new QSplitter(Qt::Vertical, this);
    m_splitter->setContentsMargins(0,0,0,0);

    mainLayout->addWidget(m_splitter);


    
    m_splitter_top = new QSplitter(Qt::Horizontal, this);
    m_splitter_top->setContentsMargins(0,0,0,0);

    // 角速度
    m_uiWaveAngularSpeed = new UiWave("速度");
    // 频谱
    m_uiWaveSpectrum = new UiWave("频谱");
    m_splitter_top->addWidget(m_uiWaveAngularSpeed);
    m_splitter_top->addWidget(m_uiWaveSpectrum);

    
    // 脉冲
    m_uiWavePulse = new UiWave("脉冲");

    m_splitter->addWidget(m_splitter_top);
    m_splitter->addWidget(m_uiWavePulse);

    int h = m_splitter->size().height();
    m_splitter->setSizes( { h/2, h/2 } );

    // 不允许折叠
    m_splitter->setCollapsible(0, false);
    m_splitter->setCollapsible(1, false);

    m_uiMonitorStatus = new UiMonitorStatus();
    mainLayout->addWidget(m_uiMonitorStatus);

    auto lb1 = new QLabel("开始时间: 00:00:00");
    lb1->setAlignment(Qt::AlignCenter);
    m_uiMonitorStatus->m_layout->addWidget(lb1, 1);

    auto line = new QFrame();
    line->setFrameShape(QFrame::VLine);

    m_uiMonitorStatus->m_layout->addWidget(line);


    auto txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/warn.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">PA设备异常告警:已断开连接</td>
</table>
)";

    m_info = new QLabel(txt);
    m_info->setAlignment(Qt::AlignCenter);
    m_uiMonitorStatus->m_layout->addWidget(m_info);

    connect(MainWindow::instance, &MainWindow::equipmentConnectChanged, [this](QString id, bool bconnect){
        if( m_id == id )
        {
            QString txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/%1.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">%2</td>
</table>
)";

            if(bconnect)
            {
                txt = txt.arg("success", "PA设备已连接");
            }
            else
            {
                txt = txt.arg("warn", "PA设备异常告警:已断开连接");
            }

            m_info->setText(txt);
        }
    });
}

bool UiMonitor::isUiDataVisiable() const
{
    // return m_uiDataTable->isVisible();
    return true;
}

void UiMonitor::setUiDataVisiable(bool visible)
{
    // m_uiDataTable->setVisible(visible);
}

void UiMonitor::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    m_splitter_top->setMinimumSize(100, event->size().height()/5);
    m_uiWavePulse->setMinimumSize(100, event->size().height()/5);
    // m_uiDataTable->setMinimumSize(100, event->size().height()/5);
}
