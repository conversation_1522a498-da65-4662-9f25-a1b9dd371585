#include "wizardpagetest.h"
#include "ui/panelequipment/wizardpageeqpment.h"
#include "widgets/flowlayout.h"
#include "widgets/toolbox.h"

#include <QButtonGroup>
#include <QVBoxLayout>

WizardPageTest::WizardPageTest()
{
    setTitle("选择设备类型");
    // QLabel *label1 = new QLabel("This is Page 1");
    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    // QToolBox * toolBox = new QToolBox();
    ToolBox * toolBox = new ToolBox();

    m_buttonGroup= new QButtonGroup();

    connect(m_buttonGroup, &QButtonGroup::idClicked, [this](int id)
    {
        qDebug() << "单击id为 " << id;
        emit test_type_changed(id);
    });


    registerField("test_type", this, "test_type", SIGNAL(test_type_changed));

    auto test_standard = new EquipmentFlow();
    test_standard->setAttribute(Qt::WA_StyledBackground);
    FlowLayout *flowLayout = new FlowLayout(test_standard, 0, 0, 0);
    flowLayout->setContentsMargins(0,0,0,0);


    auto voltage = new EquipmentItem("电压穿越", "---", "./res/test/voltage.png");
    flowLayout->addWidget(voltage);
    m_buttonGroup->addButton(voltage, 0);
    voltage->setChecked(true);

    toolBox->addItem(test_standard, "新能源行业标准测试");

    auto test_custom = new EquipmentFlow();
    test_custom->setAttribute(Qt::WA_StyledBackground);
    FlowLayout *flowLayout_dianyuan = new FlowLayout(test_custom, 0, 0, 0);
    flowLayout_dianyuan->setContentsMargins(0,0,0,0);

    auto scrtip = new EquipmentItem("脚本扩展", "---", "./res/test/script.png");
    flowLayout_dianyuan->addWidget(scrtip);
    m_buttonGroup->addButton(scrtip, 1);

    toolBox->addItem(test_custom, "自定义测试");
    layout->addWidget(toolBox);
}

int WizardPageTest::get_test_type()
{
    if(m_buttonGroup)
    {
        return m_buttonGroup->checkedId();
    }

    return -1;
}
