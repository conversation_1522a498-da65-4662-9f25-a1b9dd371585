#include "dlgexport.h"
#include "widgets/buttonedit.h"

#include <QComboBox>
#include <QDialogButtonBox>
#include <QFileDialog>
#include <QFormLayout>
#include <QHBoxLayout>
#include <QLabel>
#include <QListWidget>
#include <QTreeWidget>
#include <QVBoxLayout>

DlgExport::DlgExport(QWidget *parent)
    : QDialog(parent)
{
    auto mainLayout = new QVBoxLayout(this);
    auto hlayout = new QHBoxLayout();
    mainLayout->addLayout(hlayout);

    auto flayout = new QFormLayout();
    hlayout->addLayout(flayout);

    auto pathEdit = new ButtonEdit("...");
    connect(pathEdit, &ButtonEdit::buttonClicked,[=]()
    {
        QString fileName = QFileDialog::getOpenFileName(
                               nullptr,
                               tr("选择文件"),
                               "D:/",
                               tr("images(*.png *jpeg *bmp);;video files(*.avi *.mp4 *.wmv);;All files(*.*)"));
        pathEdit->setText(fileName);
    }
           );


    flayout->addRow(new QLabel("导出路径"), pathEdit);

    auto combType = new QComboBox();
    combType->addItems({"fld", "CSV", "tdms", "mat"});
    flayout->addRow(new QLabel("导出种类"), combType);

    auto combSource = new QComboBox();
    combSource->addItems({"主波形", "缩放波形", "光标"});
    flayout->addRow(new QLabel("导出源"), combSource);

    auto combTime = new QComboBox();
    combTime->addItems({"关闭", "5pts", "10pts", "15pts"});
    flayout->addRow(new QLabel("抽样间隔"), combTime);

    auto dialogButtonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    mainLayout->addWidget(dialogButtonBox);

    // QListWidget * list = new QListWidget();
    QTreeWidget * list = new QTreeWidget();

    hlayout->addWidget(list);

    list->setAlternatingRowColors(true);
    list->setHeaderLabel("通道导出");
    QStringList strList =  {"U1", "U2", "U3", "U4", "U5"};
    for (const QString &str : strList)
    {
        QTreeWidgetItem* item = new QTreeWidgetItem(list);
        item->setText(0, str);
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        item->setCheckState(0, Qt::Unchecked);
    }

    connect(dialogButtonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);

    resize(680, 500);
}
