#ifndef UTILS_H
#define UTILS_H

/******************************************************************************
  File Name     : dlgexport.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 功能函数包
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QFile>
#include <QTextStream>
#include <QString>
#include <QMap>

/**
 * @brief convertQss
 * @param templateQssPath
 * @return
 * 解析qss文件变量
 */

inline QString convertQss(const QString &templateQssPath)
{
    QFile qssFile(templateQssPath);
    qssFile.open(QIODevice::ReadOnly);
    QTextStream qssStream(&qssFile);

    // 设置文件的编码格式为UTF-8
    qssStream.setEncoding(QStringConverter::Utf8);

    // 解析变量并记录内容
    QMap<QString, QString> varMap;
    QStringList varList;
    QString content;

    while (!qssStream.atEnd())
    {
        QString line = qssStream.readLine().trimmed();
        if (line.startsWith('$'))
        {
            QStringList parts = line.split('=');
            if (parts.size() == 2)
            {
                QString name = parts[0].trimmed().mid(1); // 去掉$
                QString value = parts[1].trimmed();
                varMap[name] = value;
                varList.append(name);
            }
        }
        else
        {
            content += line + '\n';
        }
    }
    qssFile.close();


    std::sort(varList.begin(), varList.end(), [](const QString &s1, const QString &s2)
    {
        return s1.length() > s2.length();
    });

    for (const QString &key : varList)
    {
        content.replace("$" + key, varMap[key]);
    }


    return content;
}


/**
 * @brief loadQssFile
 * @param widget
 * @param filePath
 * 解析qss文件，并设置到widget
 */

template<typename T>
inline void loadQssFile( T* widget, const QString &filePath)
{
    // QFile qssFile(filePath);
    // qssFile.open(QFile::ReadOnly | QFile::Text);
    // QString qssContent = QLatin1String(qssFile.readAll());
    // widget->setStyleSheet(qssContent);

    QString qss = convertQss(filePath);
    widget->setStyleSheet(qss);


    // 测试-替换变量并写入到输出文件
    // QFile file(filePath+".ss");
    // if( file.open(QFile::WriteOnly | QFile::Truncate) )
    // {
    //     QTextStream out(&file); //IO设备对象的地址对其进行初始化
    //     out.setEncoding(QStringConverter::Utf8);
    //     out.setGenerateByteOrderMark(false);
    //     out << qss;
    //     out.flush();
    //     file.close();
    // }
}


#endif // UTILS_H
