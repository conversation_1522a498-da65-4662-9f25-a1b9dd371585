#ifndef ACTIONMANAGER_H
#define ACTIONMANAGER_H

/******************************************************************************
  File Name     : actionmanager.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 事件管理，用于解耦菜单命令和执行代码
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include "singleton.h"

#include <QMap>
#include <functional>


class ActionManager: public Singleton<ActionManager>
{
public:
    void registerCommand(const QString& cmd, std::function<void(bool)> function);
    void executeCommand(const QString& cmd, bool checked=true);

    QList<QString> allCmds() const;

private:
    ActionManager();
    friend class Singleton<ActionManager>;

private:
    QMap<QString, std::function<void(bool)>> m_commands;
};



#endif // ACTIONMANAGER_H
