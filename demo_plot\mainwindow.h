/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-06-10 23:03:24
 * @LastEditors: zhai
 * @LastEditTime: 2025-07-10 00:11:16
 */
#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include "core/Graph.h"

namespace QCPL
{
    class GraphDataGrid;
}

class PlotWidget;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void graphSelected(PlotWidget *plot, Graph *graph);
    void updateDataGrid();
    void showData(Graph *graph);

private:
    Graph *m_graph;
    QTimer dataTimer;

    QCPL::GraphDataGrid *dataGrid;
    PlotWidget *plot;

private slots:
    void timeToAddData();
};
#endif // MAINWINDOW_H
