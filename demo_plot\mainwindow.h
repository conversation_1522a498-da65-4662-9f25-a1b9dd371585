#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QTimer>
#include "core/Graph.h"

namespace QCPL
{
    class GraphDataGrid;
}

class PlotWidget;

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void graphSelected(Graph *graph);
    void updateDataGrid();
    void showData(Graph *graph);

private:
    Graph *m_graph;
    QTimer dataTimer;

    QCPL::GraphDataGrid *dataGrid;
    PlotWidget *plot;

private slots:
    void timeToAddData();
};
#endif // MAINWINDOW_H
