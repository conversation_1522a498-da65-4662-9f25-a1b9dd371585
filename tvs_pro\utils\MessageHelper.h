#ifndef MESSAGEHELPER_H
#define MESSAGEHELPER_H

#include <QMessageBox>
#include <QWidget>

// Forward declare QMessageBox to use its enums
class QMessageBox;

class MessageHelper
{
public:
    // Information message
    static void information(QWidget *parent, const QString &message);
    
    // Warning message with custom title and buttons
    static QMessageBox::StandardButton warning(QWidget *parent, const QString &message,
                                            QMessageBox::StandardButtons buttons = QMessageBox::Ok,
                                            QMessageBox::StandardButton defaultButton = QMessageBox::NoButton);
    
    // Question dialog
    static QMessageBox::StandardButton question(QWidget *parent, const QString &message,
                                             QMessageBox::StandardButtons buttons = QMessageBox::Yes | QMessageBox::No,
                                             QMessageBox::StandardButton defaultButton = QMessageBox::NoButton);
    
    // Critical error message
    static void critical(QWidget *parent, const QString &message);
    
    // About dialog
    static void about(QWidget *parent, const QString &message);
};

#endif // MESSAGEHELPER_H
