#include "qsplitter.h"
#include "uimonitorstatus.h"
#include "ui/monitor/uidatatable.h"
#include "ui/monitor/monitor.h"
#include "ui/monitor/uiwave.h"
#include "plot_widget.h"
#include "core/Graph.h"
#include "utils/kissfft/kiss_fft.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>
#include <QVector>
#include <QPair>

UiMonitor::UiMonitor(const QString &id, QWidget *parent)
    : QWidget{parent}, m_id(id), m_firstRun(true), m_lastPointKey(0.0)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    // 分割窗口
    auto v_splitter = new QSplitter(Qt::Vertical, this);
    v_splitter->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(v_splitter);

    auto h_splitter_angular = new QSplitter(Qt::Horizontal, this);
    h_splitter_angular->setContentsMargins(0, 0, 0, 0);

    // 角速度
    WaveConfig config = {"扭振角速度", "时间/(s)", "扭振角速度/(rad/s)"};
    m_uiWaveAngularSpeed = new UiWave(config);

    // 频谱
    config = {"频谱", "频率/(Hz)", "幅值/(dB)"};
    m_uiWaveAngularSpectrum = new UiWave(config);
    h_splitter_angular->addWidget(m_uiWaveAngularSpeed);
    h_splitter_angular->addWidget(m_uiWaveAngularSpectrum);


    auto h_splitter = new QSplitter(Qt::Horizontal, this);
    h_splitter->setContentsMargins(0, 0, 0, 0);

    // 速度
    config = {"扭振速度", "时间/(s)", "扭振速度/(rad/s)"};
    m_uiWaveSpeed = new UiWave(config);

    // 频谱
    config = {"频谱", "频率/(Hz)", "幅值/(dB)"};
    m_uiWaveSpectrum = new UiWave(config);
    h_splitter->addWidget(m_uiWaveSpeed);
    h_splitter->addWidget(m_uiWaveSpectrum);


    auto top_tab = new QTabWidget();
    top_tab->setAttribute(Qt::WA_StyledBackground);

    // 将标签位置设置为底部
    top_tab->setTabPosition(QTabWidget::West);

    // 创建标签页-角速度
    top_tab->addTab(h_splitter_angular, QIcon("./res/icon/widget/tab_prop.png"), "扭振角速度");

    // 创建标签页-速度
    top_tab->addTab(h_splitter, QIcon("./res/icon/widget/tab_prop.png"), "扭振速度");
    // top_tab->setTabVisible(1, false);



    // 脉冲
    config = {"脉冲", "时间/(s)", "脉冲幅值/(V)"};
    m_uiWavePulse = new UiWave(config);

    v_splitter->addWidget(top_tab);
    v_splitter->addWidget(m_uiWavePulse);

    int h = v_splitter->size().height();
    v_splitter->setSizes({h / 2, h / 2});

    // 不允许折叠
    v_splitter->setCollapsible(0, false);
    v_splitter->setCollapsible(1, false);

    m_uiMonitorStatus = new UiMonitorStatus();
    mainLayout->addWidget(m_uiMonitorStatus);

    auto lb1 = new QLabel("开始时间: 00:00:00");
    lb1->setAlignment(Qt::AlignCenter);
    m_uiMonitorStatus->m_layout->addWidget(lb1, 1);

    auto line = new QFrame();
    line->setFrameShape(QFrame::VLine);

    m_uiMonitorStatus->m_layout->addWidget(line);

    auto txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/warn.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">设备异常告警:已断开连接</td>
</table>
)";

    m_info = new QLabel(txt);
    m_info->setAlignment(Qt::AlignCenter);
    m_uiMonitorStatus->m_layout->addWidget(m_info);

    connect(MainWindow::instance, &MainWindow::equipmentConnectChanged, [this](QString id, bool bconnect)
    {
        if( m_id == id )
        {
            QString txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/%1.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">%2</td>
</table>
)";

            if(bconnect)
            {
                txt = txt.arg("success", "PA设备已连接");
            }
            else
            {
                txt = txt.arg("warn", "设备异常告警:已断开连接");
            }

            m_info->setText(txt);
        } });

    auto dataSource = new DaqDataSource("角速度");
    m_graph_rad = new Graph(dataSource);
    // graph->refreshData(true);
    m_uiWaveAngularSpeed->get_plot_widget()->addGraph(m_graph_rad);

    dataSource = new DaqDataSource("频谱");
    m_graph_spectrum = new Graph(dataSource);
    m_uiWaveAngularSpectrum->get_plot_widget()->addGraph(m_graph_spectrum);

    dataSource = new DaqDataSource("脉冲");
    m_graph_pulse = new Graph(dataSource);
    m_uiWavePulse->get_plot_widget()->addGraph(m_graph_pulse);

    m_uiWaveAngularSpeed->get_plot_widget()->getPlot()->yAxis->setRange(-1.1, 1.1);
    m_uiWavePulse->get_plot_widget()->getPlot()->yAxis->setRange(-1.1, 1.1);

    connect(&dataTimer, SIGNAL(timeout()), this, SLOT(timeToAddData()));
    dataTimer.start(1000);


    // connect(m_uiWaveAngularSpeed->get_plot_widget(), SIGNAL(graphSelected(PlotWidget*, Graph*)), 
    //         MainWindow::instance, SLOT(graphSelected(PlotWidget*, Graph*)));

    connect(m_uiWaveAngularSpeed->get_plot_widget(), &PlotWidget::graphSelected, MainWindow::instance, &MainWindow::graphSelected);


}

bool UiMonitor::isUiDataVisiable() const
{
    // return m_uiDataTable->isVisible();
    return true;
}

void UiMonitor::setUiDataVisiable(bool visible)
{
    // m_uiDataTable->setVisible(visible);
}

void UiMonitor::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    // m_splitter_top->setMinimumSize(100, event->size().height() / 5);
    // m_uiWavePulse->setMinimumSize(100, event->size().height() / 5);
    // m_uiDataTable->setMinimumSize(100, event->size().height()/5);
}



QPair<QVector<double>, QVector<double>> computeSpectrum(const QVector<double> &signal, double sampleRate) {
    int N = signal.size();
    kiss_fft_cfg cfg = kiss_fft_alloc(N, 0, nullptr, nullptr);
    
    // Use QVector instead of raw arrays
    QVector<kiss_fft_cpx> in(N), out(N);

    // Fill input data
    for (int i = 0; i < N; i++) {
        in[i].r = signal[i]; // Real part
        in[i].i = 0.0;       // Imaginary part
    }

    // Compute FFT
    kiss_fft(cfg, in.data(), out.data());
    free(cfg);

    // Calculate magnitude spectrum
    QVector<double> spectrum(N);
    for (int i = 0; i < N; i++) {
        spectrum[i] = sqrt(out[i].r * out[i].r + out[i].i * out[i].i);
    }

    // Calculate frequency bins (only first half is useful due to Nyquist)
    QVector<double> freqBins(N/2);
    for (int i = 0; i < N/2; i++) {
        freqBins[i] = i * sampleRate / N;
    }

    // Return frequency bins and spectrum (only first half)
    return qMakePair(freqBins, QVector<double>(spectrum.begin(), spectrum.begin() + N/2));
}


void UiMonitor::timeToAddData()
{
    if (m_firstRun)
    {
        m_dataTimer.start(); // 第一次运行时启动计时器
        m_firstRun = false;
    }

    double key = m_dataTimer.elapsed() / 1000.0; // 开始到现在的时间，单位秒

    double fsample = 1000;           // 采样率，单位Hz
    double interval = 1.0 / fsample; // 采样间隔，单位秒

    int n = std::ceil((key - m_lastPointKey) / interval);
    if (n == 0)
        return;

    QVector<double> keys, values;
    for (int i = 0; i < n; ++i)
    {
        keys.append(m_lastPointKey + i * interval);
        values.append(qSin((m_lastPointKey + i * interval) * 30) );

        // QRandomGenerator::global()->generateDouble() * 0.01
    }

    // m_uiWaveAngularSpeed->get_plot_widget()->getPlot()->graph(0)->addData(keys, values, true, 100000);

    m_uiWaveAngularSpeed->get_plot_widget()->addGraphData(m_graph_rad, keys, values, true, 100000);

    // 计算values频谱
    QPair<QVector<double>, QVector<double>> spectrum = computeSpectrum(values, fsample);
    m_uiWaveAngularSpectrum->get_plot_widget()->updateGraphData(m_graph_spectrum, spectrum.first, spectrum.second, true);
    m_uiWaveAngularSpectrum->get_plot_widget()->getPlot()->autolimits();

    m_uiWavePulse->get_plot_widget()->addGraphData(m_graph_pulse, keys, values, true, 100000);

    // 记录当前时刻
    m_lastPointKey = key;

}
