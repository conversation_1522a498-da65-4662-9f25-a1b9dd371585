#ifndef THEMEEDITOR_H
#define THEMEEDITOR_H

/******************************************************************************
  File Name     : themeeditor.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : Qt 主题编辑与预览，开发内部使用
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>

class CodeEditor;

class ThemeEditor : public QWidget
{
    Q_OBJECT
public:
    explicit ThemeEditor(const QString &filePath, std::function<void(QString)> previewCallback, QWidget *parent = nullptr);

signals:

private:
    QString m_filePath;
    std::function<void(QString)> m_previewCallback;
    CodeEditor *m_codeEdit;

private:
    void loadFileContent();
};

#endif // THEMEEDITOR_H
