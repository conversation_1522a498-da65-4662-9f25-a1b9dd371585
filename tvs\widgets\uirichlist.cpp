#include "uirichlist.h"

#include <QLabel>
#include <QVBoxLayout>
#include <QPixmap>
#include <QTreeWidget>

#include "widgets/stdwidget.h"

QString UiRichListItem::title() const
{
    return m_lbTitle->text();
}

void UiRichListItem::setTitle(const QString &newTitle)
{
    m_lbTitle->setText( newTitle);
}

QString UiRichListItem::info() const
{
    return m_lbInfo->text();
}

void UiRichListItem::setInfo(const QString &newInfo)
{
    m_lbInfo->setText( newInfo);
}

UiRichListItem::UiRichListItem(const QString &title, const QString &info, const QString &picFile, QWidget *parent)
    : QPushButton{parent}
{
    auto hlayout = new QHBoxLayout();
    setLayout(hlayout);
    hlayout->setAlignment(Qt::AlignVCenter);
    hlayout->setContentsMargins(0,0,0,0);
    auto pic = new LabelIcon();
    pic->setPixmap(QPixmap(picFile));
    pic->setScaledContents(true);					//设置图片自适应标签大小

    hlayout->addWidget(pic);

    auto vlayout = new QVBoxLayout();
    hlayout->addLayout(vlayout);
    hlayout->addStretch(1);

    vlayout->setAlignment(Qt::AlignVCenter);
    m_lbTitle = new LabelTitle(title);
    m_lbInfo = new LabelInfo(info);

    vlayout->addWidget(m_lbTitle);
    vlayout->addWidget(m_lbInfo);

    setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding); // 设置大小策略为 Preferred
}


UiRichList::UiRichList(QWidget *parent)
    : QWidget{parent}
{
    m_main_box = new QVBoxLayout(this);
}

void UiRichList::addItem(const QString &title, const QString &info, const QString &picFile)
{
    auto item = new UiRichListItem(title, info, picFile);
    m_main_box->addWidget(item);

    connect(item, &QPushButton::clicked, [=]()
    {
        emit item_clicked(title, info);
    });
}

void UiRichList::clearAll()
{
    QLayoutItem* child = nullptr;
    while((child = m_main_box->takeAt(0)) != 0)
    {
        child->widget()->setParent(nullptr);
        delete child;
    }
}


RecentFilesWidget::RecentFilesWidget(QWidget *parent) : QWidget(parent)
{
    setWindowTitle("Recent Files");

    QVBoxLayout *layout = new QVBoxLayout(this);

    // 创建 QTreeWidget
    m_treeWidget = new QTreeWidget(this);
    layout->addWidget(m_treeWidget);

    // 设置列数
    m_treeWidget->setColumnCount(1);

    // 去掉左边的级别线
    m_treeWidget->setStyleSheet("QTreeWidget::item { border-bottom: 1px solid #ccc; }");

    // 设置整行选中
    m_treeWidget->setSelectionBehavior(QAbstractItemView::SelectRows);

    // 添加根节点
    QTreeWidgetItem *root = new QTreeWidgetItem(m_treeWidget);
    root->setText(0, "Recent Files");

    // 添加示例文件
    addFile("Document1", "C:/Documents/Document1.txt");
    addFile("Document2", "C:/Documents/Document2.txt");
    addFile("Document3", "C:/Documents/Document3.txt");
}

void RecentFilesWidget::addFile(const QString &filename, const QString &filepath)
{
    // 创建 QTreeWidgetItem
    QTreeWidgetItem *item = new QTreeWidgetItem(m_treeWidget);

    // 添加文件名和文件路径
    auto *nameLabel = new LabelH1(filename);
    nameLabel->setStyleSheet("font-weight: bold; font-size: 12pt; color: #0078d4;");
    auto *pathLabel = new LabelH2(filepath);
    pathLabel->setStyleSheet("font-size: 10pt; color: #6e6e6e;");

    // 设置 item 的布局
    QVBoxLayout *itemLayout = new QVBoxLayout();
    itemLayout->addWidget(nameLabel);
    itemLayout->addWidget(pathLabel);
    itemLayout->setSpacing(5); // 设置间距
    itemLayout->setContentsMargins(0, 0, 0, 0); // 设置边距

    // 设置 item 的高度
    item->setSizeHint(0, QSize(200, 60));

    // 设置 item 的布局到树节点
    QWidget *widget = new QWidget();
    widget->setLayout(itemLayout);
    m_treeWidget->setItemWidget(item, 0, widget);

    // 添加 item 到根节点
    QTreeWidgetItem *root = m_treeWidget->invisibleRootItem();
    root->addChild(item);
}
