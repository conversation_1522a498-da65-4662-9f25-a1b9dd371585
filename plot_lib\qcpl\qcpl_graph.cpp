#include "qcpl_graph.h"

namespace QCPL
{

const int maxSelectorHandles = 20;

QCPSelectionDecorator* LineGraph::_sharedSelectionDecorator = nullptr;

void LineGraph::setSharedSelectionDecorator(QCPSelectionDecorator* decorator)
{
    if (_sharedSelectionDecorator && _sharedSelectionDecorator != decorator)
    {
        delete _sharedSelectionDecorator;
        _sharedSelectionDecorator = nullptr;
    }
    _sharedSelectionDecorator = decorator;
}

LineGraph::LineGraph(QCPAxis *keyAxis, QCPAxis *valueAxis) : QCPGraph(keyAxis, valueAxis)
{
}



// QCPL 使用点线表示选中状态
void LineGraph::draw(QCPPainter *painter)
{
    if (!mKeyAxis || !mValueAxis)
    {
        qDebug() << Q_FUNC_INFO << "invalid key or value axis";
        return;
    }
    if (mKeyAxis.data()->range().size() <= 0 || mDataContainer->isEmpty()) return;
    if (mLineStyle == lsNone && mScatterStyle.isNone()) return;

    QVector<QPointF> lines, scatters; // line and (if necessary) scatter pixel coordinates will be stored here while iterating over segments

    QCPSelectionDecorator* selectionDecorator = _sharedSelectionDecorator ? _sharedSelectionDecorator : mSelectionDecorator;

    // loop over and draw segments of unselected/selected data:
    QList<QCPDataRange> selectedSegments, unselectedSegments, allSegments;
    getDataSegments(selectedSegments, unselectedSegments);
    allSegments << unselectedSegments << selectedSegments;
    for (int i=0; i<allSegments.size(); ++i)
    {
        bool isSelectedSegment = i >= unselectedSegments.size();
        // get line pixel points appropriate to line style:
        QCPDataRange lineDataRange = isSelectedSegment ? allSegments.at(i) : allSegments.at(i).adjusted(-1, 1); // unselected segments extend lines to bordering selected data point (safe to exceed total data bounds in first/last segment, getLines takes care)
        getLines(&lines, lineDataRange);

        // draw selection:
        if (isSelectedSegment && selectionDecorator && selectionDecorator->pen() != Qt::NoPen)
        {
            QPen selectionPen(selectionDecorator->pen());
            selectionPen.setColor(mPen.color());

            painter->setPen(selectionPen);
            painter->setBrush(Qt::NoBrush);

            drawLinePlot(painter, lines);
        }

        // draw line:
        else if (mLineStyle != lsNone)
        {
            painter->setPen(mPen);
            painter->setBrush(Qt::NoBrush);

            drawLinePlot(painter, lines); // also step plots can be drawn as a line plot
        }

        // draw scatters:
        if (!mScatterStyle.isNone())
        {
            getScatters(&scatters, allSegments.at(i));
            drawScatterPlot(painter, scatters, mScatterStyle);
        }

        // draw selection:
        if (isSelectedSegment && selectionDecorator && !selectionDecorator->scatterStyle().isNone())
        {
            if (scatters.isEmpty())
                getScatters(&scatters, allSegments.at(i));

            if (scatters.size() <= maxSelectorHandles)
            {
                drawScatterPlot(painter, scatters, selectionDecorator->scatterStyle());
            }
            else
            {
                QVector<QPointF> selectorHandles;
                selectorHandles.resize(maxSelectorHandles);
                int step = scatters.size() / maxSelectorHandles;
                for (int si = 0, hi = 0; si < scatters.size() && hi < maxSelectorHandles; si += step, hi++)
                {
                    const QPointF& p = scatters.at(si);
                    selectorHandles[hi].setX(p.x());
                    selectorHandles[hi].setY(p.y());
                }
                drawScatterPlot(painter, selectorHandles, selectionDecorator->scatterStyle());
            }
        }
    }

    // draw other selection decoration that isn't just line/scatter pens and brushes:
    if (selectionDecorator)
        selectionDecorator->drawDecoration(painter, selection());
}


// 基类 + _sharedSelectionDecorator
// void LineGraph::draw(QCPPainter *painter)
// {
//     if (!mKeyAxis || !mValueAxis)
//     {
//         qDebug() << Q_FUNC_INFO << "invalid key or value axis";
//         return;
//     }
//     if (mKeyAxis.data()->range().size() <= 0 || mDataContainer->isEmpty())
//         return;
//     if (mLineStyle == lsNone && mScatterStyle.isNone())
//         return;

//     QVector<QPointF> lines, scatters; // line and (if necessary) scatter pixel coordinates will be stored here while iterating over segments

//     // 优先使用全局Decorator
//     QCPSelectionDecorator* selectionDecorator = _sharedSelectionDecorator ? _sharedSelectionDecorator : mSelectionDecorator;

//     // loop over and draw segments of unselected/selected data:
//     QList<QCPDataRange> selectedSegments, unselectedSegments, allSegments;
//     getDataSegments(selectedSegments, unselectedSegments);
//     allSegments << unselectedSegments << selectedSegments;
//     for (int i = 0; i < allSegments.size(); ++i)
//     {
//         bool isSelectedSegment = i >= unselectedSegments.size();
//         // get line pixel points appropriate to line style:
//         QCPDataRange lineDataRange = isSelectedSegment ? allSegments.at(i) : allSegments.at(i).adjusted(-1, 1); // unselected segments extend lines to bordering selected data point (safe to exceed total data bounds in first/last segment, getLines takes care)
//         getLines(&lines, lineDataRange);

// // check data validity if flag set:
// #ifdef QCUSTOMPLOT_CHECK_DATA
//         QCPGraphDataContainer::const_iterator it;
//         for (it = mDataContainer->constBegin(); it != mDataContainer->constEnd(); ++it)
//         {
//             if (QCP::isInvalidData(it->key, it->value))
//                 qDebug() << Q_FUNC_INFO << "Data point at" << it->key << "invalid." << "Plottable name:" << name();
//         }
// #endif

//         // draw fill of graph:
//         if (isSelectedSegment && selectionDecorator)
//             selectionDecorator->applyBrush(painter);
//         else
//             painter->setBrush(mBrush);

//         painter->setPen(Qt::NoPen);
//         drawFill(painter, &lines);

//         // draw line:
//         if (mLineStyle != lsNone)
//         {

//             // if (mLineStyle != lsImpulse)
//             // {
//             //     // 底层高亮
//             //     QPen baseHighlightPen(Qt::yellow);
//             //     baseHighlightPen.setWidth(1); // 不加粗
//             //     baseHighlightPen.setColor(QColor(255, 255, 0, 128)); // 半透明
//             //     painter->setPen(baseHighlightPen);
//             //     painter->drawPolyline(lines);
//             // }


//             if (isSelectedSegment && selectionDecorator)
//             {

//                 QPen selectionPen(selectionDecorator->pen());
//                 selectionPen.setColor(mPen.color());

//                 painter->setPen(selectionPen);
//                 painter->setBrush(Qt::NoBrush);
//                 // selectionDecorator->applyPen(painter);
//             }
//             else
//                 painter->setPen(mPen);
//             painter->setBrush(Qt::NoBrush);
//             if (mLineStyle == lsImpulse)
//                 drawImpulsePlot(painter, lines);
//             else
//                 drawLinePlot(painter, lines); // also step plots can be drawn as a line plot
//         }

//         // draw scatters:
//         QCPScatterStyle finalScatterStyle = mScatterStyle;
//         if (isSelectedSegment && selectionDecorator)
//             finalScatterStyle = selectionDecorator->getFinalScatterStyle(mScatterStyle);
//         if (!finalScatterStyle.isNone())
//         {
//             getScatters(&scatters, allSegments.at(i));
//             drawScatterPlot(painter, scatters, finalScatterStyle);
//         }
//     }

//     // draw other selection decoration that isn't just line/scatter pens and brushes:
//     if (selectionDecorator)
//         selectionDecorator->drawDecoration(painter, selection());
// }



// // QCPL 原始代码
// void LineGraph::draw(QCPPainter *painter)
// {
//     if (!mKeyAxis || !mValueAxis)
//     {
//         qDebug() << Q_FUNC_INFO << "invalid key or value axis";
//         return;
//     }
//     if (mKeyAxis.data()->range().size() <= 0 || mDataContainer->isEmpty()) return;
//     if (mLineStyle == lsNone && mScatterStyle.isNone()) return;

//     QVector<QPointF> lines, scatters; // line and (if necessary) scatter pixel coordinates will be stored here while iterating over segments

//     QCPSelectionDecorator* selectionDecorator = _sharedSelectionDecorator ? _sharedSelectionDecorator : mSelectionDecorator;

//     // loop over and draw segments of unselected/selected data:
//     QList<QCPDataRange> selectedSegments, unselectedSegments, allSegments;
//     getDataSegments(selectedSegments, unselectedSegments);
//     allSegments << unselectedSegments << selectedSegments;
//     for (int i=0; i<allSegments.size(); ++i)
//     {
//         bool isSelectedSegment = i >= unselectedSegments.size();
//         // get line pixel points appropriate to line style:
//         QCPDataRange lineDataRange = isSelectedSegment ? allSegments.at(i) : allSegments.at(i).adjusted(-1, 1); // unselected segments extend lines to bordering selected data point (safe to exceed total data bounds in first/last segment, getLines takes care)
//         getLines(&lines, lineDataRange);

//         // draw selection:
//         if (isSelectedSegment && selectionDecorator && selectionDecorator->pen() != Qt::NoPen)
//         {
//             QPen selectionPen(selectionDecorator->pen());
//             if (mLineStyle != lsNone)
//             {
//                 selectionPen.setWidth(mPen.width() + 2*selectionDecorator->pen().width());
//                 selectionPen.setCapStyle(Qt::RoundCap);
//             }

//             painter->setPen(selectionPen);
//             painter->setBrush(Qt::NoBrush);

//             drawLinePlot(painter, lines);
//         }

//         // draw line:
//         if (mLineStyle != lsNone)
//         {
//             painter->setPen(mPen);
//             painter->setBrush(Qt::NoBrush);

//             drawLinePlot(painter, lines); // also step plots can be drawn as a line plot
//         }

//         // draw scatters:
//         if (!mScatterStyle.isNone())
//         {
//             getScatters(&scatters, allSegments.at(i));
//             drawScatterPlot(painter, scatters, mScatterStyle);
//         }

//         // draw selection:
//         if (isSelectedSegment && selectionDecorator && !selectionDecorator->scatterStyle().isNone())
//         {
//             if (scatters.isEmpty())
//                 getScatters(&scatters, allSegments.at(i));

//             if (scatters.size() <= maxSelectorHandles)
//             {
//                 drawScatterPlot(painter, scatters, selectionDecorator->scatterStyle());
//             }
//             else
//             {
//                 QVector<QPointF> selectorHandles;
//                 selectorHandles.resize(maxSelectorHandles);
//                 int step = scatters.size() / maxSelectorHandles;
//                 for (int si = 0, hi = 0; si < scatters.size() && hi < maxSelectorHandles; si += step, hi++)
//                 {
//                     const QPointF& p = scatters.at(si);
//                     selectorHandles[hi].setX(p.x());
//                     selectorHandles[hi].setY(p.y());
//                 }
//                 drawScatterPlot(painter, selectorHandles, selectionDecorator->scatterStyle());
//             }
//         }
//     }

//     // draw other selection decoration that isn't just line/scatter pens and brushes:
//     if (selectionDecorator)
//         selectionDecorator->drawDecoration(painter, selection());
// }

// 测试性能版
// void LineGraph::draw(QCPPainter *painter)
// {
//     QElapsedTimer timer_base;
//     timer_base.start();

//     QCPGraph::draw(painter);

//     qint64 t_base = timer_base.elapsed();

//     QElapsedTimer timer;
//     timer.start();

//     if (!mKeyAxis || !mValueAxis)
//     {
//         qDebug() << Q_FUNC_INFO << "invalid key or value axis";
//         return;
//     }
//     if (mKeyAxis.data()->range().size() <= 0 || mDataContainer->isEmpty()) return;
//     if (mLineStyle == lsNone && mScatterStyle.isNone()) return;

//     QVector<QPointF> lines, scatters; // line and (if necessary) scatter pixel coordinates will be stored here while iterating over segments

//     QCPSelectionDecorator* selectionDecorator = _sharedSelectionDecorator ? _sharedSelectionDecorator : mSelectionDecorator;

//     // loop over and draw segments of unselected/selected data:
//     QList<QCPDataRange> selectedSegments, unselectedSegments, allSegments;
//     getDataSegments(selectedSegments, unselectedSegments);
//     allSegments << unselectedSegments << selectedSegments;

//     qint64 t_segment = timer.elapsed();

//     qint64 t_getlines_total = 0;
//     qint64 t_draw_total = 0;

//     qint64 t_draw_sel_line_total = 0;
//     qint64 t_draw_line_total = 0;
//     qint64 t_draw_scatter_total = 0;
//     qint64 t_draw_sel_scatter_total = 0;

//     for (int i=0; i<allSegments.size(); ++i)
//     {
//         bool isSelectedSegment = i >= unselectedSegments.size();

//         // 测量getLines耗时
//         QElapsedTimer timer_draw;
//         timer_draw.start();

//         // get line pixel points appropriate to line style:
//         QCPDataRange lineDataRange = isSelectedSegment ? allSegments.at(i) : allSegments.at(i).adjusted(-1, 1); // unselected segments extend lines to bordering selected data point (safe to exceed total data bounds in first/last segment, getLines takes care)
//         getLines(&lines, lineDataRange);

//         qint64 t_getlines = timer_draw.elapsed();
//         t_getlines_total += t_getlines;


//         qint64 t_1 = timer_draw.elapsed();
//         // draw selection:
//         if (isSelectedSegment && selectionDecorator && selectionDecorator->pen() != Qt::NoPen)
//         {
//             QPen selectionPen(selectionDecorator->pen());
//             if (mLineStyle != lsNone)
//             {
//                 // 降低线宽
//                 int baseWidth = mPen.width();
//                 int highlightWidth = qMin(baseWidth + 2 * selectionDecorator->pen().width(), 1); // 限制上限
//                 selectionPen.setWidth(highlightWidth);
//                 selectionPen.setCapStyle(Qt::FlatCap);
//             }

//             painter->setPen(selectionPen);
//             painter->setBrush(Qt::NoBrush);
//             painter->setRenderHint(QPainter::Antialiasing, false);

//             drawLinePlot(painter, lines);
//         }

//         qint64 t_2 = timer_draw.elapsed();
//         t_draw_sel_line_total += (t_2-t_1);

//         // draw line:
//         if (mLineStyle != lsNone)
//         {
//             painter->setPen(mPen);
//             painter->setBrush(Qt::NoBrush);

//             drawLinePlot(painter, lines); // also step plots can be drawn as a line plot
//         }

//         qint64 t_3 = timer_draw.elapsed();
//         t_draw_line_total += (t_3-t_2);

//         // draw scatters:
//         if (!mScatterStyle.isNone())
//         {
//             getScatters(&scatters, allSegments.at(i));
//             drawScatterPlot(painter, scatters, mScatterStyle);
//         }

//         qint64 t_4 = timer_draw.elapsed();
//         t_draw_scatter_total += (t_4-t_3);

//         // draw selection:
//         if (isSelectedSegment && selectionDecorator && !selectionDecorator->scatterStyle().isNone())
//         {
//             if (scatters.isEmpty())
//                 getScatters(&scatters, allSegments.at(i));

//             if (scatters.size() <= maxSelectorHandles)
//             {
//                 drawScatterPlot(painter, scatters, selectionDecorator->scatterStyle());
//             }
//             else
//             {
//                 QVector<QPointF> selectorHandles;
//                 selectorHandles.resize(maxSelectorHandles);
//                 int step = scatters.size() / maxSelectorHandles;
//                 for (int si = 0, hi = 0; si < scatters.size() && hi < maxSelectorHandles; si += step, hi++)
//                 {
//                     const QPointF& p = scatters.at(si);
//                     selectorHandles[hi].setX(p.x());
//                     selectorHandles[hi].setY(p.y());
//                 }
//                 drawScatterPlot(painter, selectorHandles, selectionDecorator->scatterStyle());
//             }
//         }

//         qint64 t_5 = timer_draw.elapsed();
//         t_draw_sel_scatter_total += (t_5-t_4);
//         t_draw_total += (t_5-t_getlines);
//     }

//     qint64 t_draw = timer.elapsed() - t_segment;

//     // draw other selection decoration that isn't just line/scatter pens and brushes:
//     if (selectionDecorator)
//         selectionDecorator->drawDecoration(painter, selection());


//     qint64 t_total = timer.elapsed();

//     qDebug().nospace()
//             << "\n[LineGraph Performance]"
//             << "\n  Base Total time: " << t_base << "ms"
//             << "\n  LineGraph Total time: " << t_total-t_base << "ms"
//             << "\n  Segments: " << allSegments.size()
//             << "\n  Data points: " << mDataContainer->size()
//             << "\n    Segmentation: " << t_segment - t_segment << "ms"
//             << "\n    Draw loop(" << allSegments.size() << "segments): " << t_draw << "ms"
//             << "\n        getLines: " << t_getlines_total << "ms"
//             << "\n        draw: " << t_draw_total << "ms"
//             << "\n            sel_line: " << t_draw_sel_line_total << "ms"
//             << "\n            sel_scatter: " << t_draw_sel_scatter_total << "ms"
//             << "\n            line: " << t_draw_line_total << "ms"
//             << "\n            scatter: " << t_draw_scatter_total << "ms"
//             << "\n    Other " << t_total - (t_draw + t_segment) << "ms";


// }

} // namespace QCPL
