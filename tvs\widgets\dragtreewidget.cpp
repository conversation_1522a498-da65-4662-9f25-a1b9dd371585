#include "dragtreewidget.h"
#include "qevent.h"

#include <QApplication>
#include <QDrag>
#include <QLabel>
#include <QMimeData>

DragTreeWidget::DragTreeWidget(QWidget *parent)
    :QTreeWidget(parent)
{
    setAlternatingRowColors(true);

    m_label = new QLabel(this);
    m_label->setObjectName("Drag");
    m_label->hide();
}


void DragTreeWidget::mousePressEvent(QMouseEvent *event)
{
    if (event->button() & Qt::LeftButton)
    {
        m_item = itemAt(event->pos());
        if(m_item)
        {
            m_pos = event->pos();
        }
    }

    QTreeWidget::mousePressEvent(event);
}

void DragTreeWidget::mouseMoveEvent(QMouseEvent *event)
{
    // 判断拖动
    if (m_item && (event->buttons() & Qt::LeftButton) && (event->pos() - m_pos).manhattanLength() >= QApplication::startDragDistance())
    {
        QByteArray dataItem;
        QDataStream dataStream(&dataItem, QIODevice::WriteOnly);
        dataStream << m_item->text(0);
        m_label->setText(m_item->text(0));
        m_label->adjustSize();

        // 设置自定义数据
        QMimeData *mimeData = new QMimeData;
        mimeData->setData("Data/Source", dataItem);

        // 拖动设置
        QPixmap pixmap = m_label->grab();
        QDrag *drag = new QDrag(this);
        drag->setPixmap(pixmap);
        drag->setMimeData(mimeData);

        drag->setHotSpot(QPoint(pixmap.width(), pixmap.height()));
        drag->exec(Qt::MoveAction | Qt::CopyAction, Qt::CopyAction);

        //       Qt::DropAction dropAction =
        //       drag->exec(Qt::MoveAction|Qt::CopyAction,Qt::CopyAction);
        //        if(dropAction == Qt::MoveAction)
        //        {
        //            //当成功拖动时清除原节点
        //            delete item;
        //            qDebug()<<"成功";
        //        }
    }

    QTreeWidget::mouseMoveEvent(event);
}
