#ifndef NAVIGATIONWIDGET_H
#define NAVIGATIONWIDGET_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 左侧导航控件
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#pragma once

#include <QWidget>
#include <QListWidget>
#include <QStackedWidget>
#include <QVBoxLayout>
#include <QString>

class QNavigationWidget : public QWidget
{
    Q_OBJECT
public:
    explicit QNavigationWidget(QWidget *parent = nullptr);

    void addTab(QWidget* page, const QString& item, const QString& toolTip = QString(), const QString &iconFile= QString());
    int insertTab(const int index, QWidget* page, const QString& item, const QString& toolTip = QString(), const QString &iconFile= QString());
    void setTabToolTip(const int index, const QString& toolTip);
    void setTabText(const int index, const QString& text);
    void deleteTab(const int index);

    int getCurrentIndex() const;
    void setCurrentIndex(const int index);

    QWidget* getCurrentWidget() const;

signals:
    void currentIndexChanged(const int index);

private slots:
    void changeTab(const int index);

private:
    QListWidget *m_navigationMenu;
    QStackedWidget *m_stackedWidget;
};

#endif
