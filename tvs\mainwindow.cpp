#include <QApplication>
#include "mainwindow.h"
#include "qsettings.h"
#include "ui/demo/themeeditor.h"
#include "ui/paneltest/paneltest.h"
#include "app/AppConfig.h"
#include "ui/proptest/propdata.h"
#include "ui/propequipment/propequipment.h"
#include "ui/equipment/uiequipment.h"
#include "ui/panelequipment/panelequipment.h"
#include "ui/project/uiproject.h"
#include "ui/proptest/propchannel.h"
#include "ui/test/uitest.h"
#include "ui/uiwelcome.h"
#include "ui/utils.h"
#include "ui/monitor/monitor_tv.h"
#include "ui/monitor/monitor_elec.h"
#include "SAFramelessHelper.h"
#include "SARibbonBar.h"
#include "DockAreaWidget.h"
#include "SARibbonMainWindow.h"
#include "widgets/buttonedit.h"
#include "widgets/codeeditor.h"
#include "widgets/qnavigationwidget.h"
#include "widgets/slidingstackedwidget.h"
#include <QDialog>
#include <QTimer>
#include <QVBoxLayout>
#include <QFile>
#include <QDir>
#include <QMessageBox>
#include <SARibbonMenu.h>
#include <SARibbonApplicationButton.h>
#include <QCheckBox>
#include <QRadioButton>
#include <QJsonParseError>
#include <QJsonObject>
#include <QJsonArray>
#include <QListWidget>
#include <SARibbonActionsManager.h>
#include <SARibbonButtonGroupWidget.h>
#include <SARibbonTabBar.h>
#include <QFileSystemWatcher>

#include "DockSplitter.h"

#include "AutoHideDockContainer.h"
#include "utils/access_private.h"

using namespace ads;

#include <QString>
#include <QDebug>
#include <QFormLayout>
#include <QFileDialog>
#include <QMessageBox>
#include <QStatusBar>

#include <app/settings.h>

#include <ui/demo/widgetgallery.h>

#include <utils/actionmanager.h>
#include <utils/countcode.h>

#include <ui/panelequipment/wizardeqpment.h>

#include <ui/paneltest/wizarduitest.h>
#include "qcpl/qcpl_graph_grid.h"
#include "core/Graph.h"
#include "plot_widget.h"

MainWindow *MainWindow::instance = nullptr;

class SARibbonBarFake : public SARibbonBar
{
    friend class MainWindow;
};

MainWindow::MainWindow(QWidget *parent)
    : SARibbonMainWindow(parent)
{
    m_recent_files = YamlConfigPj::instance().getArray<QString>("general.recentfiles");

    m_cur_equipment = nullptr;
    m_right_tab = nullptr;

    MainWindow::instance = this;

    registerCommand();

    initRibbonBar();
    initDockWidget();

    initQssWatcher();
}

void MainWindow::registerCommand()
{
    ActionManager::instance().registerCommand("show_yibiao", [this](bool)
                                              { showContextCategory("仪表", true); });

    ActionManager::instance().registerCommand("hide_yibiao", [this](bool)
                                              { showContextCategory("仪表", false); });

    ActionManager::instance().registerCommand("CountCode", [this](bool)
                                              {
        auto  count = new CountCode();
        // 模态
        // count->setWindowModality(Qt::ApplicationModal);
        count->show(); });

    ActionManager::instance().registerCommand("vie_layout_default", [this](bool)
                                              { setDockStack(); });

    ActionManager::instance().registerCommand("vie_layout_tile", [this](bool)
                                              { setDockTile(); });

    ActionManager::instance().registerCommand("vie_layout_stacking", [this](bool)
                                              { setDockStack(); });

    ActionManager::instance().registerCommand("equ_display_numericalview", [this](bool checked)
                                              {
        if(m_cur_equipment)
        {
            m_cur_equipment->setUiDataVisiable(checked);
        } });

    ActionManager::instance().registerCommand("show_porp_equip", [this](bool checked)
                                              {
        m_right_tab->setTabVisible(0, true);
        m_right_tab->setTabVisible(1, false);
        m_right_tab->setCurrentIndex(0); });

    ActionManager::instance().registerCommand("show_porp_channel", [this](bool checked)
                                              {
        m_right_tab->setTabVisible(0, false);
        m_right_tab->setTabVisible(1, true);
        m_right_tab->setCurrentIndex(1); });

    // 连接设备（活动视图）
    ActionManager::instance().registerCommand("connect_all_equip", [this](bool)
                                              {
        QList<ads::CDockWidget*> docks = getAllOpenDocks();

        for (auto it = m_map_id_equipmentview.begin(); it != m_map_id_equipmentview.end(); ++it)
        {
            if(docks.contains(it.value()))
            {
                connectEquipment(it.key());
            }
        } });

    // 断开设备(活动视图)
    ActionManager::instance().registerCommand("deconnect_all_equip", [this](bool)
                                              {
        QList<ads::CDockWidget*> docks = getAllOpenDocks();

        for (auto it = m_map_id_equipmentview.begin(); it != m_map_id_equipmentview.end(); ++it)
        {
            if(docks.contains(it.value()))
            {
                deconnectEquipment(it.key());
            }
        } });
}

MainWindow::~MainWindow()
{
}

void MainWindow::showDlg()
{
    QDialog *dialog = new QDialog;

    dialog->setAttribute(Qt::WA_DeleteOnClose);
    dialog->setWindowTitle(tr("gallery"));

    // 创建一个垂直布局
    QVBoxLayout *layout = new QVBoxLayout(dialog);

    auto gallery = new WidgetGallery();
    layout->addWidget(gallery);

    // 设置布局为对话框的布局
    dialog->setLayout(layout);

    // 显示对话框
    dialog->exec();
}

QAction *MainWindow::createAction(const QString &text, const QString &iconurl)
{
    QAction *act = new QAction(this);
    act->setText(text);
    act->setIcon(QIcon(iconurl));
    act->setObjectName(text);
    return act;
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    SARibbonMainWindow::resizeEvent(event);
}

void MainWindow::closeEvent(QCloseEvent *event)
{
    SARibbonMainWindow::closeEvent(event);
}

void MainWindow::changeEvent(QEvent *event)
{
    QWindowStateChangeEvent *stateEvent = dynamic_cast<QWindowStateChangeEvent *>(event);
    if (Q_NULLPTR != stateEvent)
    {
        if (this->isMinimized())
        {
            qDebug() << "最小化";
        }
        else if (this->isMaximized())
        {
            qDebug() << "最大化";
        }
        else if (this->windowState() == Qt::WindowNoState)
        {
            qDebug() << "正常";
        }
    }

    SARibbonMainWindow::changeEvent(event);
}

void MainWindow::initRibbonByJson()
{
    QFile jsfile("./config/ribbon.json");
    if (jsfile.open(QIODevice::ReadOnly))
    {
        QJsonParseError err;
        QJsonDocument jsdoc = QJsonDocument::fromJson(jsfile.readAll(), &err);

        if (err.error != QJsonParseError::NoError)
        {
            qDebug() << "json 格式错误！";
            return;
        }
        else
        {
            SARibbonBar *ribbon = ribbonBar();

            QJsonObject rootObj = jsdoc.object();
            auto jribbon = rootObj.value("ribbon").toObject();

            auto japp_btn = jribbon.value("app_button").toObject();
            auto app_btn_show = japp_btn.value("show").toBool(true);

            ribbon->applicationButton()->setVisible(app_btn_show);

            auto jcategorys = jribbon.value("categorys").toArray();

            for (const QJsonValue &jcat : jcategorys)
            {
                auto jcategory = jcat.toObject();
                auto category_title = jcategory.value("title").toString();
                auto category_visible = jcategory.value("visible").toBool(true);

                SARibbonCategory *category = new SARibbonCategory();
                category->setCategoryName(category_title);
                ribbon->addCategoryPage(category);

                auto jpannels = jcategory.value("pannels").toArray();
                for (const QJsonValue &jpan : jpannels)
                {
                    auto jpannel = jpan.toObject();
                    auto pannel_title = jpannel.value("title").toString();

                    SARibbonPannel *pannel = category->addPannel(pannel_title);
                    //    SARibbonPannel* pannel = new SARibbonPannel("pannel1", category);
                    //    category->addPannel(pannel);

                    auto jactions = jpannel.value("actions").toArray();
                    for (const QJsonValue &jact : jactions)
                    {
                        auto jaction = jact.toObject();
                        auto action_title = jaction.value("title").toString();
                        auto action_icon = jaction.value("icon").toString();
                        auto action_cmd = jaction.value("cmd").toString();
                        auto checkable = jaction.value("checkable").toBool();
                        auto disable = jaction.value("disable").toBool();
                        auto visible = jaction.value("visible").toBool(true);

                        QAction *action = createAction(action_title, action_icon);
                        action->setCheckable(checkable);
                        action->setVisible(visible);
                        action->setDisabled(disable);
                        pannel->addLargeAction(action);

                        connect(action, &QAction::triggered, [action_cmd](bool checked)
                                {
                            qDebug() << "on ribbon cmd: " << action_cmd;
                            ActionManager::instance().executeCommand(action_cmd, checked); });
                    }
                }

                if (!category_visible)
                {
                    ribbon->hideCategory(category);
                }
            }

            auto jcontext_category = jribbon.value("context_category").toArray();

            for (const QJsonValue &jcat : jcontext_category)
            {
                auto jcategory = jcat.toObject();
                auto category_title = jcategory.value("title").toString();
                auto category_id = jcategory.value("id").toInt();
                auto category_color = jcategory.value("color").toString();

                auto context_category = ribbon->addContextCategory(category_title, QColor(category_color), category_id);

                auto jpages = jcategory.value("pages").toArray();
                for (const QJsonValue &jp : jpages)
                {
                    auto jpage = jp.toObject();
                    auto page_title = jpage.value("title").toString();
                    SARibbonCategory *page = context_category->addCategoryPage(page_title);

                    auto jpannels = jpage.value("pannels").toArray();
                    for (const QJsonValue &jpan : jpannels)
                    {
                        auto jpannel = jpan.toObject();
                        auto pannel_title = jpannel.value("title").toString();

                        SARibbonPannel *pannel = page->addPannel(pannel_title);

                        auto jactions = jpannel.value("actions").toArray();
                        for (const QJsonValue &jact : jactions)
                        {
                            auto jaction = jact.toObject();
                            auto action_title = jaction.value("title").toString();
                            auto action_icon = jaction.value("icon").toString();
                            auto action_cmd = jaction.value("cmd").toString();
                            auto checkable = jaction.value("checkable").toBool();
                            auto disable = jaction.value("disable").toBool();
                            auto visible = jaction.value("visible").toBool(true);

                            QAction *action = createAction(action_title, action_icon);
                            action->setCheckable(checkable);
                            action->setVisible(visible);
                            action->setDisabled(disable);
                            pannel->addLargeAction(action);

                            connect(action, &QAction::triggered, [action_cmd]()
                                    {
                                qDebug() << "on ribbon cmd: " << action_cmd;
                                ActionManager::instance().executeCommand(action_cmd); });
                        }
                    }
                }

                ribbon->showContextCategory(context_category);
            }
        }

        jsfile.close();
    }
}

ACCESS_PRIVATE_FUN(SARibbonBar, void(), resizeAll)

void MainWindow::showCategory(const QString &title, bool on)
{
    // 获取当前显示的所有的SARibbonCategory，包含未显示的SARibbonContextCategory的SARibbonCategory 也一并返回
    QList<SARibbonCategory *> cat_list = ribbonBar()->categoryPages();

    // categoryByObjectName
    auto itr = std::find_if(cat_list.begin(), cat_list.end(), [title](const SARibbonCategory *cat)
                            { return cat->categoryName() == title; });

    if (itr != cat_list.end())
    {
        if (on)
        {
            ribbonBar()->showCategory(*itr);

            int index = ribbonBar()->categoryIndex(*itr);
            ribbonBar()->setCurrentIndex(index);

            call_private::resizeAll(*ribbonBar());
            qDebug() << "显示:" << title;
        }
        else
        {
            ribbonBar()->hideCategory(*itr);
            qDebug() << "隐藏:" << title;
        }
    }
    else
    {
        qDebug() << "未找到标题为: [" << title << "] 的RibbonContextCategory";
        // showContextCategory(title, on);
    }
}

void MainWindow::showContextCategory(const QString &title, bool on)
{
    // 获取所有的上下文标签
    QList<SARibbonContextCategory *> con_list = ribbonBar()->contextCategoryList();

    auto itr = std::find_if(con_list.begin(), con_list.end(), [title](const SARibbonContextCategory *cat)
                            { return cat->contextTitle() == title; });

    if (itr != con_list.end())
    {
        if (on)
        {
            ribbonBar()->showContextCategory(*itr);
            qDebug() << "显示:" << title;
        }
        else
        {
            ribbonBar()->hideContextCategory(*itr);
            qDebug() << "隐藏:" << title;
        }
    }
    else
    {
        qDebug() << "未找到标题为: [" << title << "] 的RibbonContextCategory";
    }
}

void MainWindow::setActionChecked(const QString &title, bool checked)
{
    QList<QAction *> acts = m_ActionsManager->allActions();
    auto itr = std::find_if(acts.begin(), acts.end(), [title](const QAction *act)
                            { return act->text() == title; });

    if (itr != acts.end())
    {
        (*itr)->setChecked(checked);
    }
}

void MainWindow::onActionVisibleAllTriggered(bool on)
{
    QList<QAction *> acts = m_ActionsManager->allActions();
    for (QAction *a : acts)
    {
        if (a != m_ActionVisibleAll)
        {
            a->setVisible(on);
        }
    }
    ribbonBar()->updateRibbonGeometry();
}

void MainWindow::initRibbonBar()
{
    SAFramelessHelper *helper = framelessHelper();
    helper->setRubberBandOnResize(false);

    // Initialize application configuration
    // The AppConfig constructor already attempts to load the config
    // We'll just log if it failed
    if (APP_CONFIG.windowTitle().isEmpty())
    {
        qWarning() << "Using default configuration values";
    }
    else
    {
        qDebug() << "Application configuration loaded successfully";
    }

    // Set window title from configuration
    setWindowTitle(APP_CONFIG.windowTitle());

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // Status Bar
    setStatusBar(new QStatusBar());

    // Add application title to status bar
    auto lbQZ = new QLabel(APP_CONFIG.windowTitle(), this);
    lbQZ->setAlignment(Qt::AlignCenter);
    statusBar()->addPermanentWidget(lbQZ, 1);

    QPushButton *btnDisk = new QPushButton(QIcon("./res/icon/Open.png"), "", this);
    statusBar()->addPermanentWidget(btnDisk);

    connect(btnDisk, &QPushButton::clicked, []()
            {
        QDialog *dlg = new QDialog();

        dlg->setAttribute(Qt::WA_DeleteOnClose);
        dlg->setWindowTitle("系统设置----软件正常退出后生效");

        QFormLayout *layout = new QFormLayout(dlg);

        auto btnEdit = new ButtonEdit(QIcon("./res/icon/Open.png"));
        connect(btnEdit, &ButtonEdit::buttonClicked,[=]()
        {
            QString fileName = QFileDialog::getExistingDirectory(nullptr, tr("选择文件夹"), "", QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
            btnEdit->setText(fileName);
        }
               );
        layout->addRow(new QLabel("文件路径"), btnEdit);
        dlg->resize(500, 200);
        dlg->exec(); });

    auto lbDisk = new QLabel("20.88GB/320.66GB", this);
    statusBar()->addPermanentWidget(lbDisk);

    auto line = new QFrame();
    line->setFrameShape(QFrame::VLine);
    statusBar()->addPermanentWidget(line);

    auto lbVersion = new QLabel("V1.0.0", this);
    statusBar()->addPermanentWidget(lbVersion);

    SARibbonBar *ribbon = ribbonBar();
    // ribbon->setRibbonStyle(SARibbonBar::RibbonStyleCompactTwoRow);
    ribbon->setRibbonStyle(SARibbonBar::RibbonStyleCompactThreeRow);
    ribbon->setEnableWordWrap(false);

    ribbon->setFont(QFont("微软雅黑", 10));
    ribbon->applicationButton()->setVisible(true);
    ribbon->applicationButton()->setText(("  工程  "));

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // applicationButton

    QAbstractButton *btn = ribbon->applicationButton();
    SARibbonApplicationButton *appBtn = qobject_cast<SARibbonApplicationButton *>(btn);
    if (appBtn)
    {
        // 连接按钮的 clicked 信号和槽函数
        connect(appBtn, &QToolButton::clicked, [=]()
                {
                    // m_mainStack->slideInNext();
                    if (this->m_mainStack->currentIndex() == 0)
                    {
                        ribbon->setMinimumMode(false);
                        this->m_mainStack->slideInIdx(1, SlidingStackedWidget::RIGHT2LEFT);
                    }
                    else
                    {
                        ribbon->setCurrentIndex(0);
                        ribbon->setMinimumMode(true);

                        this->m_mainStack->slideInIdx(0, SlidingStackedWidget::LEFT2RIGHT);
                    }

                    // QTimer::singleShot(100, [=]()
                    // {
                    //     ribbon->ribbonTabBar()->setExpanding(true);
                    //     ribbon->updateRibbonGeometry();
                    // });
                });
    }

    ribbon->showMinimumModeButton(true);

    initRibbonByJson();

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // 自动注册所有action
    m_ActionsManager = new SARibbonActionsManager(ribbonBar());

    QTimer::singleShot(0, this, [this]()
                       {
        // sa_set_ribbon_theme(ribbon, SARibbonMainWindow::RibbonThemeOffice2013);
        this->setRibbonTheme(SARibbonMainWindow::RibbonThemeDark2);
        loadQssFile(ribbonBar(), "./theme/ribbon.qss");
        ribbonBar()->updateRibbonGeometry(); });

    SARibbonTabBar *ribbonTabBar = ribbon->ribbonTabBar();

    // 重新connect，以控制执行顺序
    disconnect(ribbonTabBar, &QTabBar::tabBarClicked, nullptr, nullptr);

    connect(ribbonTabBar, &QTabBar::tabBarClicked, [=](int)
            {
        // QMessageBox::information(this,  "Title",  "tabBarClicked");
        if(m_mainStack->currentIndex() == 0)
        {
            ribbonBar()->setMinimumMode(false);
            m_mainStack->slideInIdx(1, SlidingStackedWidget::RIGHT2LEFT);
        } });

    connect(ribbonTabBar, &QTabBar::tabBarClicked, ribbon, &SARibbonBarFake::onCurrentRibbonTabClicked);

    // connect( ribbon, &SARibbonBar::currentRibbonTabChanged, [=](int index)
    // {
    //     QMessageBox::information(this,  "Title",  "currentRibbonTabChanged");
    //     m_mainStack->setCurrentIndex(1);
    //     ribbon->setMinimumMode(false);
    // } );

    // connect(ribbonBar(), &SARibbonBar::ribbonModeChanged, [&](SARibbonBar::RibbonMode nowNode)
    // {
    //     if(nowNode == SARibbonBar::NormalRibbonMode)
    //     {
    //         m_mainStack->setCurrentIndex(1);
    //     }
    // });

    // connect( ribbon, &SARibbonBar::titleBarHeightChanged, [=](int, int)
    // {

    //     QMessageBox::information(this,  "Title",  "titleBarHeightChanged");
    //     m_mainStack->setCurrentIndex(1);
    //     ribbon->setMinimumMode(false);
    // } );
}

void MainWindow::initLeftDockWidget()
{
    m_dock_left = new CDockWidget("设备列表");

    // 不能移动,不能关闭,不能悬浮
    m_dock_left->setFeature(CDockWidget::DockWidgetMovable, false);
    m_dock_left->setFeature(CDockWidget::DockWidgetClosable, false);
    m_dock_left->setFeature(CDockWidget::DockWidgetFloatable, false);
    m_dock_left->setFeature(CDockWidget::DockWidgetFocusable, false);

    m_panel_tab = new QTabWidget();
    m_panel_tab->setAttribute(Qt::WA_StyledBackground);

    // 将标签位置设置为左侧
    m_panel_tab->setTabPosition(QTabWidget::West);

    // 创建标签页 - 设备
    m_panel_equip = new PanelEquipment();
    m_panel_tab->addTab(m_panel_equip, QIcon("./res/icon/widget/tab_device.png"), "设备");

    // 创建标签页 - 测试
    m_panel_test = new PanelTest();
    m_panel_tab->addTab(m_panel_test, QIcon("./res/icon/widget/tab_test.png"), "测试");

    m_dock_left->setWidget(m_panel_tab);

    auto lwidth = YamlConfigUI::instance().getValue<int>("ui_main.lwidth", 360);
    // auto rwidth = YamlConfigUI::instance().getValue<int>("ui_main.rwidth", 360);

    m_dock_left->setMinimumSizeHintMode(CDockWidget::MinimumSizeHintFromDockWidget);
    m_dock_left->setMinimumSize(lwidth, 300);

    // 方案1
    CAutoHideDockContainer *autoHideContainer = m_dock_manager->addAutoHideDockWidget(SideBarLocation::SideBarLeft, m_dock_left);
    // autoHideContainer->setSize(360);
    autoHideContainer->dockAreaWidget()->setAllowedAreas(DockWidgetArea::NoDockWidgetArea);
    m_dock_left->dockAreaWidget()->setAllowedAreas(DockWidgetArea::NoDockWidgetArea);
    m_dock_left->setAutoHide(false);

    // 方案2
    // // CDockAreaWidget* leftDockArea = m_dock_manager->addDockWidget(DockWidgetArea::LeftAutoHideArea, m_dock_left);
    // CDockAreaWidget* leftDockArea = m_dock_manager->addDockWidget(DockWidgetArea::LeftDockWidgetArea, m_dock_left);
    // leftDockArea->setAllowedAreas(DockWidgetArea::NoDockWidgetArea);
    // m_dock_left->setAutoHide(false);
}

void MainWindow::graphSelected(PlotWidget *plot, Graph *graph)
{
    if (graph == nullptr)
    {
        m_dataGrid->setData(QVector<double>(), QVector<double>());
    }
    else
    {
        QVector<double> keys, values;
        if (!plot->getGraphData(graph, keys, values))
        {
            m_dataGrid->setData(QVector<double>(), QVector<double>());
            return;
        }

        m_dataGrid->setData(keys, values);
    }
}

void MainWindow::initRightDockWidget()
{
    m_dock_right = new CDockWidget("数据");

    // 不能移动,不能关闭,不能悬浮
    m_dock_right->setFeature(CDockWidget::DockWidgetMovable, false);
    m_dock_right->setFeature(CDockWidget::DockWidgetClosable, false);
    m_dock_right->setFeature(CDockWidget::DockWidgetFloatable, false);
    m_dock_right->setFeature(CDockWidget::DockWidgetFocusable, false);

    // m_right_tab = new QTabWidget();
    // m_right_tab->setAttribute(Qt::WA_StyledBackground);

    // // 将标签位置设置为左侧
    // m_right_tab->setTabPosition(QTabWidget::West);

    // // 创建标签页-设备属性
    // PropEquipment *label1 = new PropEquipment();
    // m_right_tab->addTab(label1, QIcon("./res/icon/widget/tab_prop.png"), "属性");

    // // 创建标签页-测试通道数据
    // PropChannel *propChannel = new PropChannel();
    // m_right_tab->addTab(propChannel, QIcon("./res/icon/widget/tab_prop.png"), "通道");
    // m_right_tab->setTabVisible(1, false);

    // // 创建标签页-设备数据
    // PropData *propData = new PropData();
    // m_right_tab->addTab(propData, QIcon("./res/icon/widget/tab_data.png"), "数据");

    m_dataGrid = new QCPL::GraphDataGrid;

    m_dock_right->setWidget(m_dataGrid);

    auto rwidth = YamlConfigUI::instance().getValue<int>("ui_main.rwidth", 360);
    m_dock_right->setMinimumSizeHintMode(CDockWidget::MinimumSizeHintFromDockWidget);
    m_dock_right->setMinimumSize(rwidth, 300);

    // 方案1
    CAutoHideDockContainer *autoHideContainer = m_dock_manager->addAutoHideDockWidget(SideBarLocation::SideBarRight, m_dock_right);
    autoHideContainer->dockAreaWidget()->setAllowedAreas(DockWidgetArea::NoDockWidgetArea);
    m_dock_right->dockAreaWidget()->setAllowedAreas(DockWidgetArea::NoDockWidgetArea);
    m_dock_right->setAutoHide(false);

    // 方案2
    // // CDockAreaWidget* leftDockArea = m_dock_manager->addDockWidget(DockWidgetArea::RightAutoHideArea, m_dock_right);
    // CDockAreaWidget* leftDockArea = m_dock_manager->addDockWidget(DockWidgetArea::RightDockWidgetArea, m_dock_right);
    // // leftDockArea->setAllowedAreas(DockWidgetArea::RightDockWidgetArea);
    // leftDockArea->setAllowedAreas(DockWidgetArea::NoDockWidgetArea);
    // m_dock_right->setAutoHide(false);
}

void MainWindow::initDockWidget()
{
    CDockManager::setConfigFlag(CDockManager::OpaqueSplitterResize, true);
    CDockManager::setConfigFlag(CDockManager::XmlCompressionEnabled, false);
    CDockManager::setConfigFlag(CDockManager::FocusHighlighting, true);
    CDockManager::setAutoHideConfigFlags(CDockManager::DefaultAutoHideConfig);

    CDockManager::setConfigFlag(CDockManager::AllTabsHaveCloseButton, true);
    CDockManager::setConfigFlag(CDockManager::DockAreaHideDisabledButtons, true);
    CDockManager::setConfigFlag(CDockManager::DisableTabTextEliding, true);
    CDockManager::setConfigFlag(CDockManager::DockAreaHasTabsMenuButton, false);
    CDockManager::setConfigFlag(CDockManager::DockAreaHasUndockButton, false);

    m_mainStack = new SlidingStackedWidget(this);
    m_mainStack->setWrap(true);
    setCentralWidget(m_mainStack);

    // 创建主窗口
    m_project_widget = new UiProject();
    m_mainStack->addWidget(m_project_widget);

    // 方法 1
    //  QMainWindow *mainWnd = new QMainWindow();
    //  mainWnd->setObjectName("MainWnd");
    //  m_dock_manager = new CDockManager(mainWnd);

    // 方法 2
    QWidget *mainWnd = new QWidget();
    mainWnd->setAttribute(Qt::WA_StyledBackground);
    mainWnd->setObjectName("MainWnd");
    QVBoxLayout *layout = new QVBoxLayout(mainWnd);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_main_wnd.margins");
    if (margins.size() == 4)
    {
        layout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    m_dock_manager = new ads::CDockManager(mainWnd);
    layout->addWidget(m_dock_manager);

    m_dock_manager->setDockWidgetToolBarStyle(Qt::ToolButtonIconOnly, ads::CDockWidget::StateFloating);
    m_mainStack->addWidget(mainWnd);

    m_mainStack->setCurrentIndex(1);

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // Central DockManager
    QWidget *centralWnd = new QWidget();
    centralWnd->setObjectName("CenWnd");
    QVBoxLayout *cenLayout = new QVBoxLayout(centralWnd);
    cenLayout->setContentsMargins(0, 0, 0, 0);
    m_cen_dock_manager = new ads::CDockManager(centralWnd);
    cenLayout->addWidget(m_cen_dock_manager);

    connect(m_cen_dock_manager, &CDockManager::focusedDockWidgetChanged, [this](CDockWidget *old, CDockWidget *now)
            {
        if(now)
        {
            QWidget *widget = now->widget();

            // 判断是否是设备视图
            UiEquipment* uiEquip = qobject_cast<UiEquipment*>(widget);
            if(uiEquip)
            {
                setActionChecked("数值视图", uiEquip->isUiDataVisiable());
                m_cur_equipment = uiEquip;
            }
            else
            {
                m_cur_equipment = nullptr;
                // 判断是否是测试视图
                UiTest* uiTest = qobject_cast<UiTest*>(widget);
                if(uiTest)
                {
                }
            }
        } });

    auto dock_central = new CDockWidget("central");
    dock_central->setWidget(centralWnd);
    dock_central->setFeature(CDockWidget::NoTab, true);
    CDockAreaWidget *central_dock_area = m_dock_manager->setCentralWidget(dock_central);
    central_dock_area->setAllowedAreas(DockWidgetArea::InvalidDockWidgetArea);

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // CentralWidget - 欢迎
    auto ui_welcome = new uiWelcome();

    m_dock_cen = new CDockWidget("欢迎");
    m_dock_cen->setWidget(ui_welcome);
    m_dock_cen->setFeature(CDockWidget::NoTab, true);

    m_dock_cen->setMinimumSize(100, 200);
    m_dock_cen->setMinimumSizeHintMode(CDockWidget::MinimumSizeHintFromDockWidget);

    auto cen_dock_area = m_cen_dock_manager->setCentralWidget(m_dock_cen);
    cen_dock_area->setAllowedAreas(DockWidgetArea::AllDockAreas);

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // 标准组件demo
    auto gallery = new WidgetGallery();
    auto galleryView = new CDockWidget("标准组件");
    galleryView->setWidget(gallery);
    m_cen_dock_manager->addDockWidgetTabToArea(galleryView, m_dock_cen->dockAreaWidget());

    auto uiWaveTV = new UiMonitorTV("1");
    auto waveViewTV = new CDockWidget("扭振相关实时波形");
    waveViewTV->setFeature(CDockWidget::DockWidgetFloatable, false);
    waveViewTV->setFeature(CDockWidget::DockWidgetClosable, false);
    waveViewTV->setWidget(uiWaveTV);
    m_cen_dock_manager->addDockWidgetTabToArea(waveViewTV, m_dock_cen->dockAreaWidget());

    auto uiWaveElec = new UiMonitorElec("2");
    auto waveViewElec = new CDockWidget("电器相关实时波形");
    waveViewElec->setFeature(CDockWidget::DockWidgetFloatable, false);
    waveViewElec->setFeature(CDockWidget::DockWidgetClosable, false);
    waveViewElec->setWidget(uiWaveElec);
    m_cen_dock_manager->addDockWidgetTabToArea(waveViewElec, m_dock_cen->dockAreaWidget());

    auto uiEquipWave3 = new UiMonitorTV("3");
    auto equipWaveView3 = new CDockWidget("蒸汽参数实时数据");
    equipWaveView3->setFeature(CDockWidget::DockWidgetFloatable, false);
    equipWaveView3->setFeature(CDockWidget::DockWidgetClosable, false);
    equipWaveView3->setWidget(uiEquipWave3);
    m_cen_dock_manager->addDockWidgetTabToArea(equipWaveView3, m_dock_cen->dockAreaWidget());

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // ThemeEditor
    if (false)
    {
        initThemeEditor();
    }

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // 左侧面板
    // initLeftDockWidget();

    ///////////////////////////////////////////////////////////////////////////////////////////////
    // 右侧面板
    initRightDockWidget();

    loadQssFile(m_dock_manager, "./theme/dock.qss");
    loadQssFile(m_cen_dock_manager, "./theme/dock.qss");
}

void MainWindow::ActivateMainUI()
{
    SARibbonBar *ribbon = ribbonBar();
    ribbon->setMinimumMode(false);
    m_mainStack->slideInIdx(1, SlidingStackedWidget::RIGHT2LEFT);
}

void MainWindow::ActivateProjectUI()
{
    SARibbonBar *ribbon = ribbonBar();
    ribbon->setCurrentIndex(0);
    ribbon->setMinimumMode(true);

    m_mainStack->slideInIdx(0, SlidingStackedWidget::LEFT2RIGHT);
}

void MainWindow::ActivateEquipmentView(const QString &id, const QString &title)
{
    if (m_map_id_equipmentview.find(id) != m_map_id_equipmentview.end())
    {
        m_map_id_equipmentview[id]->toggleView();
        m_map_id_equipmentview[id]->setAsCurrentTab();
        return;
    }

    auto uiEquip = new UiEquipment(id);
    auto equipView = new CDockWidget(title);

    equipView->setWidget(uiEquip);
    equipView->setFeature(CDockWidget::DockWidgetFloatable, false);
    m_cen_dock_manager->addDockWidgetTabToArea(equipView, m_dock_cen->dockAreaWidget());
    m_map_id_equipmentview[id] = equipView;

    m_cur_equipment = uiEquip;

    MainWindow::instance->setActionChecked("数值视图", true);

    QTimer::singleShot(10, [this]()
                       { showCategory("设备", true); });

    connect(equipView, &CDockWidget::closed, [this, id]() {

    });

    // connect(equipView, &CDockWidget::visibilityChanged, [=](bool visible)
    // {
    //     showCategory("设备", visible);

    //     if(visible)
    //     {
    //         setActionChecked("数值视图", uiEquip->isUiDataVisiable());
    //         m_cur_equipment = uiEquip;
    //     }
    //     else
    //     {
    //         m_cur_equipment = nullptr;
    //     }
    // });
}

void MainWindow::ActivateTestView(const QString &id, const QString &title)
{
    if (m_map_id_testview.find(id) != m_map_id_testview.end())
    {
        m_map_id_testview[id]->toggleView();
        m_map_id_testview[id]->setAsCurrentTab();
        return;
    }

    auto uiTest = new UiTest();
    auto testView = new CDockWidget(title);
    testView->setWidget(uiTest);
    testView->setFeature(CDockWidget::DockWidgetFloatable, false);
    m_cen_dock_manager->addDockWidgetTabToArea(testView, m_dock_cen->dockAreaWidget());

    m_map_id_testview[id] = testView;

    QTimer::singleShot(10, [this]()
                       { showCategory("测试", true); });

    connect(testView, &CDockWidget::closed, [this, id]() {

    });

    connect(testView, &CDockWidget::visibilityChanged, [this](bool visible)
            { showCategory("测试", visible); });
}

void MainWindow::DeactivateTestView(const QString &id, bool bDelete)
{
    if (m_map_id_testview.contains(id))
    {
        if (bDelete)
        {
            m_map_id_testview[id]->deleteDockWidget();
            m_map_id_testview.remove(id);
        }
        else
        {
            m_map_id_testview[id]->closeDockWidget();
        }
    }
}

void MainWindow::DeactivateEquipmentView(const QString &id, bool bDelete)
{
    if (m_map_id_equipmentview.contains(id))
    {
        if (bDelete)
        {
            m_map_id_equipmentview[id]->deleteDockWidget();
            m_map_id_equipmentview.remove(id);
        }
        else
        {
            m_map_id_equipmentview[id]->closeDockWidget();
        }
    }
}

void MainWindow::DeleteEquipment(const QString &id)
{
    DeactivateEquipmentView(id, true);
    m_doc.removeEquipment(id);
}

void MainWindow::DeleteTest(const QString &id)
{
    DeactivateTestView(id, true);
    m_doc.removeTest(id);
}

QList<CDockWidget *> MainWindow::getAllOpenDocks()
{
    QMap<QString, CDockWidget *> dockMap = m_cen_dock_manager->dockWidgetsMap();

    QList<CDockWidget *> docks = dockMap.values();

    docks.erase(std::remove_if(docks.begin(), docks.end(), [](CDockWidget *dock)
                               {
        // 返回true以删除对象，返回false以保留对象
        return dock->isClosed(); }),
                docks.end());

    return docks;
}

void MainWindow::connectEquipment(const QString &id)
{
    emit equipmentConnectChanged(id, true);
}

void MainWindow::deconnectEquipment(const QString &id)
{
    emit equipmentConnectChanged(id, false);
}

void MainWindow::setDockStack()
{
    CDockWidget *central_dock = m_cen_dock_manager->centralWidget();
    CDockAreaWidget *central_dock_area = central_dock->dockAreaWidget();
    QMap<QString, CDockWidget *> dockMap = m_cen_dock_manager->dockWidgetsMap();

    QList<CDockWidget *> docks = dockMap.values();
    for (auto dock : docks)
    {
        if (central_dock == dock)
        {
            continue;
        }

        m_cen_dock_manager->removeDockWidget(dock);
        m_cen_dock_manager->addDockWidgetTabToArea(dock, central_dock_area);
    }
}

void MainWindow::setDockTile()
{
    CDockWidget *central_dock = m_cen_dock_manager->centralWidget();
    CDockAreaWidget *central_dock_area = central_dock->dockAreaWidget();
    QMap<QString, CDockWidget *> dockMap = m_cen_dock_manager->dockWidgetsMap();

    QList<CDockWidget *> docks = dockMap.values();
    CDockAreaWidget *baseArea = nullptr;
    int count = 0;
    for (auto dock : docks)
    {
        if (central_dock == dock)
        {
            continue;
        }

        m_cen_dock_manager->removeDockWidget(dock);
        if (nullptr == baseArea)
        {
            baseArea = m_cen_dock_manager->addDockWidget(DockWidgetArea::CenterDockWidgetArea, dock, central_dock_area);
        }
        else
        {
            if (count % 3 == 0)
            {
                baseArea = m_cen_dock_manager->addDockWidgetToContainer(DockWidgetArea::BottomDockWidgetArea, dock, central_dock->dockContainer());
            }
            else
            {
                m_cen_dock_manager->addDockWidget(DockWidgetArea::RightDockWidgetArea, dock, baseArea);
            }
        }

        ++count;
    }
}

void MainWindow::openProject()
{
    QString fileName = QFileDialog::getOpenFileName(nullptr, tr("打开工程"), "", "青智工程文件(*.qz)");
    if (fileName != "")
    {
        loadDoc(fileName);
    }
}

void MainWindow::saveProject()
{
    saveDoc();
}

void MainWindow::saveProjectAs()
{
    QString fileName = QFileDialog::getSaveFileName(nullptr, tr("文件另存为"), "", "青智工程文件(*.qz)");
    if (fileName != "")
    {
        saveDoc(fileName);
    }
}

bool MainWindow::saveDoc(const QString &file)
{
    // 保存布局
    m_doc.m_win_state = saveState();
    m_doc.m_dock_state = m_dock_manager->saveState();
    m_doc.m_cen_dock_state = m_cen_dock_manager->saveState();

    m_doc.saveDoc(file);

    if (m_doc.m_file != "")
    {
        addRecentFile(m_doc.m_file);
    }

    return true;
}

bool MainWindow::loadDoc(const QString &file)
{
    // 加载文件
    m_doc.loadDoc(file);

    if (m_doc.m_file != "")
    {
        addRecentFile(m_doc.m_file);
    }

    // 清理UI
    m_panel_equip->clearAll();
    m_panel_test->clearAll();

    foreach (auto dock, m_map_id_equipmentview)
    {
        dock->deleteDockWidget();
    }
    m_map_id_equipmentview.clear();

    foreach (auto dock, m_map_id_testview)
    {
        dock->deleteDockWidget();
    }
    m_map_id_testview.clear();

    // 添加文件内容至UI
    foreach (EquipmentDataPtr equip, m_doc.m_equipments)
    {
        m_panel_equip->addEquipment(equip->m_id, equip->m_type, equip->m_name, equip->m_ip);
        ActivateEquipmentView(equip->m_id, equip->m_name + "-" + equip->m_ip);
    }

    foreach (TestDataPtr test, m_doc.m_tests)
    {
        m_panel_test->addTest(test->m_id, test->m_type, test->m_name);
        ActivateTestView(test->m_id, test->m_name);
    }

    ribbonBar()->setCurrentIndex(0);
    ribbonBar()->setMinimumMode(false);
    m_mainStack->slideInIdx(1, SlidingStackedWidget::RIGHT2LEFT);

    // 恢复布局
    restoreState(m_doc.m_win_state);
    m_dock_manager->restoreState(m_doc.m_dock_state);
    m_cen_dock_manager->restoreState(m_doc.m_cen_dock_state);
    return true;
}

void MainWindow::newEquipment()
{
    WizardUiEquipment wizard;

    // connect(&wizard, &QWizard::accepted, [](){
    // });

    if (QDialog::Accepted == wizard.exec())
    {
        auto equipment = wizard.field("equipment").toInt();
        auto name = wizard.field("name").toString();
        auto type = wizard.field("type").toString();
        auto communicate = wizard.field("communicate").toString();
        auto port = wizard.field("port").toString();
        auto ip = wizard.field("ip").toString();

        // 添加
        addNewEquipment(equipment, name, ip, port);

        ActivateMainUI();
        m_panel_tab->setCurrentIndex(0);
    }
}

void MainWindow::newTest()
{
    WizardUiTest wizard;

    // 显示对话框
    if (QDialog::Accepted == wizard.exec())
    {
        auto test_type = wizard.field("test_type").toInt();
        auto type = wizard.field("type").toString();
        auto equipment = wizard.field("equipment").toString();
        auto unit = wizard.field("unit").toString();
        auto ip = wizard.field("ip").toString();

        QStringList testNames = {"电压跨越", "脚本扩展"};

        QString name = testNames[test_type];

        // 自动命名
        int n = 0;
        while (m_doc.hasTestName(name))
        {
            name = testNames[test_type] + QString("(%1)").arg(++n);
        }

        // 添加
        addNewTest(test_type, name);

        ActivateMainUI();
        m_panel_tab->setCurrentIndex(1);
    }
}

void MainWindow::addRecentFile(const QString &file)
{
    m_recent_files.removeAll(file);
    m_recent_files.prepend(file);
    while (m_recent_files.size() > MaxRecentFiles)
        m_recent_files.removeLast();

    YamlConfigPj::instance().setArray("general.recentfiles", m_recent_files);

    emit recentFilesChanged(m_recent_files);
    // updateRecentFilesMenu();
}

void MainWindow::addNewEquipment(int type, const QString &name, const QString &ip, const QString &port)
{
    auto id = newUuid();
    m_doc.addEquipment(id, type, name, ip, port);
    m_panel_equip->addEquipment(id, type, name, ip);
    ActivateEquipmentView(id, name + "-" + ip);
}

void MainWindow::addNewTest(int type, const QString &name)
{
    QString id = newUuid();
    m_doc.addTest(id, type, name);
    m_panel_test->addTest(id, type, name);
    ActivateTestView(id, name);
}

void MainWindow::initQssWatcher()
{
    // 文件检测
    QString qss_widget = "./theme/darkstyle.qss";
    QString qss_dock = "./theme/dock.qss";
    QString qss_ribbon = "./theme/ribbon.qss";

    m_pFileWatcher = new QFileSystemWatcher();
    m_pFileWatcher->addPath(qss_widget);
    m_pFileWatcher->addPath(qss_dock);
    m_pFileWatcher->addPath(qss_ribbon);

    connect(m_pFileWatcher, &QFileSystemWatcher::fileChanged, this, &MainWindow::onQssChanged);
}

// 监测qss变化，自动应用
void MainWindow::onQssChanged(const QString &path)
{
    qDebug() << path;

    int lastIndex = path.lastIndexOf("/");
    QString fileName = path.mid(lastIndex + 1);
    if (fileName == "darkstyle.qss")
    {
        loadQssFile(qApp, path);
    }
    else if (fileName == "dock.qss")
    {
        loadQssFile(m_dock_manager, path);
        loadQssFile(m_cen_dock_manager, "./theme/dock.qss");
    }
    else if (fileName == "ribbon.qss")
    {
        loadQssFile(ribbonBar(), path);
        ribbonBar()->updateRibbonGeometry();
    }
}

// 控制宽度
void MainWindow::setPanelWidth()
{
    auto lwidth = YamlConfigUI::instance().getValue<int>("ui_main.lwidth", 260);
    auto rwidth = YamlConfigUI::instance().getValue<int>("ui_main.rwidth", 260);

    CDockSplitter *lsplitter = internal::findParent<CDockSplitter *>(m_dock_left->dockAreaWidget());
    // CDockSplitter* rsplitter = internal::findParent<CDockSplitter*>(m_dock_right->dockAreaWidget());

    int l_w = lsplitter->size().width();
    qDebug() << "splitter width: " << l_w;

    // lsplitter->setStretchFactor(0, 1);
    // lsplitter->setStretchFactor(1, 1);
    // lsplitter->setStretchFactor(2, 1);

    // lsplitter->setStretchFactor(0, 0);
    // lsplitter->setStretchFactor(1, 1);
    // lsplitter->setStretchFactor(2, 0);

    lsplitter->setSizes({lwidth, l_w - lwidth - rwidth, rwidth});
}

// theme editor
void MainWindow::initThemeEditor()
{
    auto dock_code_widget = new CDockWidget("widget");
    auto previewWidgetCallback = [&](QString str)
    {
        // qDebug() << "预览按钮被点击了！";
        // QMessageBox::information(this,  "Title",  "Content");

        qApp->setStyleSheet(str);
    };

    m_editor_widget = new ThemeEditor("./theme/darkstyle.qss", previewWidgetCallback);
    dock_code_widget->setWidget(m_editor_widget);
    dock_code_widget->resize(100, 150);
    dock_code_widget->setMinimumSize(100, 150);
    // m_cen_dock_manager->addDockWidget(DockWidgetArea::LeftDockWidgetArea, dock_code_widget);
    // m_cen_dock_manager->addDockWidgetTab(DockWidgetArea::LeftDockWidgetArea, dock_code_widget);
    m_cen_dock_manager->addDockWidget(DockWidgetArea::RightDockWidgetArea, dock_code_widget);

    auto dock_code_dock = new CDockWidget("dock");
    auto previewDockCallback = [&](QString str)
    {
        // qDebug() << "预览按钮被点击了！";
        m_dock_manager->setStyleSheet(str);
    };
    m_editor_dock = new ThemeEditor("./theme/dock.qss", previewDockCallback);
    dock_code_dock->setWidget(m_editor_dock);
    dock_code_dock->resize(250, 150);
    dock_code_dock->setMinimumSize(200, 150);
    m_cen_dock_manager->addDockWidgetTabToArea(dock_code_dock, dock_code_widget->dockAreaWidget());

    auto dock_code_rb = new CDockWidget("ribbon");
    auto previewRbCallback = [&](QString str)
    {
        // qDebug() << "预览按钮被点击了！";
        ribbonBar()->setStyleSheet(str);
        ribbonBar()->updateRibbonGeometry();
    };

    m_editor_rb = new ThemeEditor("./theme/ribbon.qss", previewRbCallback);
    dock_code_rb->setWidget(m_editor_rb);
    dock_code_rb->resize(250, 150);
    dock_code_rb->setMinimumSize(200, 150);
    m_cen_dock_manager->addDockWidgetTabToArea(dock_code_rb, dock_code_widget->dockAreaWidget());
}
