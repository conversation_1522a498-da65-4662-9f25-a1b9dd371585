#ifndef CONSTANTS_H
#define CONSTANTS_H

#include <QColor>

namespace Constants {
// Theme color constants (Using QColor for direct use in Qt)
const QColor DARK_BG_COLOR = QColor("#1c1c1c");
const QColor DARK_AXIS_COLOR = QColor("black");
const QColor DARK_TICK_COLOR = QColor("lightgrey");
const QColor DARK_GRID_COLOR = QColor("#555555");
const QColor DARK_TEXT_COLOR = QColor("white");
const QColor DARK_ANNOTATION_BG = QColor("#333333");

const QColor LIGHT_BG_COLOR = QColor("#FFFFFF");
const QColor LIGHT_AXIS_COLOR = QColor("white");
const QColor LIGHT_TICK_COLOR = QColor("black");
const QColor LIGHT_GRID_COLOR = QColor("darkgrey");
const QColor LIGHT_TEXT_COLOR = QColor("black");
const QColor LIGHT_ANNOTATION_BG = QColor("white");

// UI palette colors
const QColor DARK_PALETTE_WINDOW = QColor(53, 53, 53);
const QColor DARK_PALETTE_WINDOW_TEXT = QColor(255, 255, 255);
const QColor DARK_PALETTE_BASE = QColor(25, 25, 25);
const QColor DARK_PALETTE_ALT_BASE = QColor(53, 53, 53);
const QColor DARK_PALETTE_TOOLTIP_BASE = QColor(0, 0, 0);
const QColor DARK_PALETTE_TOOLTIP_TEXT = QColor(255, 255, 255);
const QColor DARK_PALETTE_TEXT = QColor(255, 255, 255);
const QColor DARK_PALETTE_BUTTON = QColor(53, 53, 53);
const QColor DARK_PALETTE_BUTTON_TEXT = QColor(255, 255, 255);
const QColor DARK_PALETTE_BRIGHT_TEXT = QColor(0, 128, 255); // Note: Might not map perfectly in palette
const QColor DARK_PALETTE_LINK = QColor(42, 130, 218);
const QColor DARK_PALETTE_HIGHLIGHT = QColor(42, 130, 218);
const QColor DARK_PALETTE_HIGHLIGHT_TEXT = QColor(0, 0, 0);

// Default plot line colors
const QColor DEFAULT_MEASURED_COLOR_LIGHT_1 = QColor("#17a2a2"); // Base color for first trace
const QColor DEFAULT_REFERENCE_COLOR_LIGHT_1 = QColor("lightgrey"); // Fill color for first trace
const QColor DEFAULT_SPOT_NOISE_COLOR_LIGHT = QColor("red"); // Spot noise color remains consistent

const QColor DEFAULT_MEASURED_COLOR_DARK_1 = QColor("cyan"); // Base color for first trace
const QColor DEFAULT_REFERENCE_COLOR_DARK_1 = QColor("yellow"); // Line color for first trace
const QColor DEFAULT_SPOT_NOISE_COLOR_DARK = QColor("orange"); // Spot noise color remains consistent

} // namespace Constants

#endif // CONSTANTS_H
