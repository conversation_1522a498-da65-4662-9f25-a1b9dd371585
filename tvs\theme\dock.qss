$ThemeColor=rgb(43, 43, 43)
/* 主题色 */
$ThemeColorLighter=rgb(64, 64, 64)
/* 主题色亮 */
$ThemeColorDarkness=rgb(20, 99, 95)
/* 主题色暗 */


$AccentColor=rgb(64, 64, 64)
/* 辅助色 */
$AccentColorLighter=rgb(78, 93, 112)
/* 辅助色亮 */
$AccentColorLighterHover=rgb(90, 108, 130)
/* 辅助色亮-悬停 */
$AccentColorLighterClick=rgb(60, 74, 91)
/* 辅助色亮-点击 */
$AccentColorDarkness=rgb(25, 35, 45)
/* 辅助是暗 */
$AccentColorDarknessPlan=rgba(0, 0, 0, 0.15)
/* 辅助是区域暗 */
$AccentColorDarknessPlanborder=rgba(0, 0, 0, 0.3)
/* 辅助是区域暗边 */
$AccentColorDblock=rgba(0, 0, 0, 0.5)
/* 辅助是区域黑色 */




/* * Visual Studio like light theme */
/***************************************************************************** * CDockManager *****************************************************************************/
ads--CDockManager {
    background: palette(window);

}

/*****************************************************************************   * CDockContainerWidget   *****************************************************************************/

ads--CDockContainerWidget {
    /* 整体外框 */
    background:black;
    padding: 0px 0px;

}

/*****************************************************************************   * CDockAreaWidget   *****************************************************************************/
ads--CDockAreaWidget {
    /* 单个窗口外框 */
    /* background: palette(window);
    selection-background-color: red;
    border: 1px solid palette(dark); */

}

ads--CDockAreaTitleBar {
    /* 标题背景 */
    background: $AccentColor;
    border-bottom: 1px solid #000000;
}

ads--CDockAreaTitleBar  QToolButton{
    height: 29px;
}
ads--CDockAreaTitleBar::tear {
    color: red;
    
}
ads--CTitleBarButton {
    /* 更多还原关闭 */
    padding: 0px 5px;
    border: none;
    background: $AccentColor;
    color: #DFE1E2;
}

ads--CTitleBarButton:hover {
    /* 更多还原关闭-鼠标悬停 */
    background: rgba(0, 0, 0, 24);
}

ads--CTitleBarButton:pressed {
    /* 更多还原关闭-鼠标按下 */
    background: rgba(0, 0, 0, 48);
}

QScrollArea#dockWidgetScrollArea {
    /* 单个窗口内容 */
    padding: 0px;
}

#tabsMenuButton::menu-indicator {
    /* 更多还原关闭-更多按钮右下图标 */
    background-color: aqua;
    image: none;
}

#dockAreaCloseButton {
    /* 更多还原关闭-关闭图标 */
    qproperty-icon: url(:/ads/images/close-button-focused.svg), url(:/ads/images/close-button-disabled.svg) disabled;
    qproperty-iconSize: 16px;
}

#detachGroupButton {
    /* 更多还原关闭-还原图标 */
    qproperty-icon: url(:/ads/images/detach-button.svg), url(:/ads/images/detach-button-disabled.svg) disabled;
    qproperty-iconSize: 16px;
}

ads--CDockAreaWidget[focused="true"] ads--CDockAreaTitleBar {
    /* 标题栏背景-获取焦点 */ 
    /* border-bottom: 1px solid black; */
}

/*****************************************************************************   * CDockWidgetTab   *****************************************************************************/
/* dock面板tab栏 */
ads--CDockWidgetTab {
    /* tab */
    background: $ThemeColorLighter;
    border:1px solid black; 
    border-left:0 ; 
    border-bottom: 0;
    border-top: 0; 
    padding: 5;
    border-radius: 0px;
    qproperty-iconSize: 16px 16px;
    /* this is optional in case you would like to change icon size*/
}


ads--CDockWidgetTab  QPushButton{
    /* tab 基础按钮*/
    /* height: 16; */
    padding:0;
    margin: 0;
}
ads--CDockWidgetTab:hover  {
    /* tab 鼠标悬停*/
    background: #343434;
}

ads--CDockWidgetTab[activeTab="true"] {
    /* tab选中颜色 */
    background: $ThemeColor;

}
ads--CDockWidgetTab QLabel {
    /* tab文字颜色 */
    color:white;
    background:none;
}

ads--CDockWidgetTab[activeTab="true"] QLabel {
    /* tab选中文字颜色 */
    color: white;
}
#tabCloseButton {
    /* tab关闭 */
    margin-top: 2px;
    background: none;
    border: none;
    padding: 0;
    min-height:20px;
    qproperty-icon: url(:/ads/images/close-button-focused.svg), url(:/ads/images/close-button-disabled.svg) disabled;
    qproperty-iconSize: 16px;
}

#tabCloseButton:hover {
    /* tab关闭-鼠标悬停 */
    background: rgba(0, 0, 0, 24);
}

#tabCloseButton:pressed {
    /* tab关闭-鼠标按下 */
    background: rgba(0, 0, 0, 48);
}

/* Focus related styling  窗口获取焦点*/

ads--CDockWidgetTab[focused="true"] {
    /* tab-窗口获取焦点  */
    background: palette(highlight);
    border-color: palette(highlight);
}

ads--CDockWidgetTab[focused="true"]>#tabCloseButton {
    /* tab关闭-窗口获取焦点  */
    qproperty-icon: url(:/ads/images/close-button-focused.svg)
}
ads--CDockWidgetTab>#tabCloseButton:hover {
    /* tab关闭-窗口获取焦点-悬停  */
    background:rgba(0,0,0,0.25);
}

ads--CDockWidgetTab[focused="true"]>#tabCloseButton:pressed {
   /* tab关闭-窗口获取焦点-按下  */
    background: rgba(255, 255, 255, 92);
}

ads--CDockWidgetTab QLabel {
    /* color: palette(light); */
    padding-left:6px;
}

/*****************************************************************************   * CDockWidget   *****************************************************************************/
ads--CDockWidget {
    /* 窗口内容 */
    /* background: palette(light); */
    /* border: 1px solid rgb(204, 204, 204); */
    /* border-top: none; */
}
/*****************************************************************************   *   * Styling of auto hide functionality   *   *****************************************************************************/
/*****************************************************************************   * CAutoHideTab   *****************************************************************************/
ads--CAutoHideTab {
    /* 窗口收起后的按钮 */
    qproperty-iconSize: 16px 16px;
    qproperty-icon: url(:/ads/images/vs-pin-button-pinned-focused.svg);
    /* this is optional in case you would like to change icon size*/
    background: $AccentColor;
    padding-left: 20px;
    padding-right: 20px;
    text-align: left;
    height: 20px;
    width:100%;
    border-radius: 0;
    padding-bottom: 2px;
}

ads--CAutoHideTab:hover {
    /* 窗口收起后的按钮-鼠标悬停 */
    background: #343434;
}


ads--CAutoHideTab[iconOnly="false"][sideBarLocation="0"],
ads--CAutoHideTab[iconOnly="false"][sideBarLocation="2"] {
    border-top: 0px solid rgba(0, 0, 0, 48);
}

ads--CAutoHideTab[iconOnly="false"][sideBarLocation="1"],
ads--CAutoHideTab[iconOnly="false"][sideBarLocation="3"] {
    border-bottom: 0px solid rgba(0, 0, 0, 48);
}

ads--CAutoHideTab:hover[iconOnly="false"][sideBarLocation="0"],
ads--CAutoHideTab:hover[iconOnly="false"][sideBarLocation="2"],
ads--CAutoHideTab[iconOnly="false"][sideBarLocation="0"][activeTab="true"],
ads--CAutoHideTab[iconOnly="false"][sideBarLocation="2"][activeTab="true"] {
    border-top: 0px solid palette(highlight);
}

ads--CAutoHideTab:hover[iconOnly="false"][sideBarLocation="1"],
ads--CAutoHideTab:hover[iconOnly="false"][sideBarLocation="3"],
ads--CAutoHideTab[iconOnly="false"][sideBarLocation="1"][activeTab="true"],
ads--CAutoHideTab[iconOnly="false"][sideBarLocation="3"][activeTab="true"] {
    border-bottom: 0px solid palette(highlight);
}

/**   * Auto hide tabs with icon only   */
ads--CAutoHideTab[iconOnly="true"][sideBarLocation="0"] {
    border-top: 6px solid rgba(0, 0, 0, 48);
}

ads--CAutoHideTab[iconOnly="true"][sideBarLocation="1"] {
    border-left: 6px solid rgba(0, 0, 0, 48);
}

ads--CAutoHideTab[iconOnly="true"][sideBarLocation="2"] {
    border-right: 6px solid rgba(0, 0, 0, 48);
}

ads--CAutoHideTab[iconOnly="true"][sideBarLocation="3"] {
    border-bottom: 6px solid rgba(0, 0, 0, 48);
}

/**   * Auto hide tabs with icon only hover   */
ads--CAutoHideTab:hover[iconOnly="true"][sideBarLocation="0"],
ads--CAutoHideTab[iconOnly="true"][sideBarLocation="0"][activeTab="true"] {
    border-top: 6px solid palette(highlight);
}

ads--CAutoHideTab:hover[iconOnly="true"][sideBarLocation="1"],
ads--CAutoHideTab[iconOnly="true"][sideBarLocation="1"][activeTab="true"] {
    border-left: 6px solid palette(highlight);
}

ads--CAutoHideTab:hover[iconOnly="true"][sideBarLocation="2"],
ads--CAutoHideTab[iconOnly="true"][sideBarLocation="2"][activeTab="true"] {
    border-right: 6px solid palette(highlight);
}

ads--CAutoHideTab:hover[iconOnly="true"][sideBarLocation="3"],
ads--CAutoHideTab[iconOnly="true"][sideBarLocation="3"][activeTab="true"] {
    border-bottom: 6px solid palette(highlight);
}

/*****************************************************************************   * CAutoHideSideBar   *****************************************************************************/
ads--CAutoHideSideBar {
    /* 窗口收起后的背景条 */
    background: $AccentColor;
    border-radius: 0;
    qproperty-spacing: 12;
}
#sideTabsContainerWidget {
    background: transparent;
}

ads--CAutoHideSideBar[sideBarLocation="0"] {
    border-bottom: 1px solid black;
}

ads--CAutoHideSideBar[sideBarLocation="1"] {
    border-right: 1px solid black;
}

ads--CAutoHideSideBar[sideBarLocation="2"] {
    border-left: 1px solid black;
}

ads--CAutoHideSideBar[sideBarLocation="3"] {
    border-top: 1px solid black;
}

/*****************************************************************************   * CAutoHideDockContainer   *****************************************************************************/
ads--CAutoHideDockContainer {
    background: #000;
}

ads--CAutoHideDockContainer ads--CDockAreaTitleBar {
    padding: 0px;
    background: $AccentColor;
    border-bottom: 1px solid black;
}

/*   * This is required because the ads--CDockAreaWidget[focused="true"] will   * overwrite the ads--CAutoHideDockContainer ads--CDockAreaTitleBar rule   */
ads--CAutoHideDockContainer ads--CDockAreaWidget[focused="true"] ads--CDockAreaTitleBar {
    background: $AccentColor;
    padding: 0px;
}

#autoHideTitleLabel {
    padding:0 6px;
}
ads--CAutoHideDockContainer ads--CDockAreaTitleBar QLabel{
    background-color: #404040;
}

/** 左右两侧面板 */
QWidget#MainWnd ads--CDockWidgetTab[activeTab="true"],
QWidget#MainWnd ads--CDockWidgetTab[focused="true"] {
    background:#404040;
    border-right-width:0;
}

/** 主面板 */
QWidget#CenWnd ads--CDockWidgetTab[activeTab="true"],
QWidget#CenWnd ads--CDockWidgetTab[focused="true"] {
    background:$ThemeColor;
    border-right:1px solid #000;
}
QWidget#CenWnd ads--CDockWidgetTab{
    padding: 4px 5px;
}
QWidget#CenWnd ads--CDockWidgetTab:hover{
    border-top: 2px solid #0072a3;
}
QWidget#CenWnd ads--CDockWidgetTab[activeTab="true"]{
    border-top: 2px solid #0A8FE2;
}
/*****************************************************************************   * CAutoHideDockContainer titlebar buttons   *****************************************************************************/
#dockAreaAutoHideButton {
    qproperty-icon: url("./res/icon/dock/dockAreaAutoHideButton.png");
    
    qproperty-iconSize: 16px;
}

ads--CAutoHideDockContainer #dockAreaAutoHideButton {
    qproperty-icon: url(:/ads/images/vs-pin-button-pinned-focused.svg);
    qproperty-iconSize: 16px;
}

ads--CAutoHideDockContainer #dockAreaCloseButton {
    qproperty-icon: url(:/ads/images/close-button-focused.svg);
}
ads--CAutoHideDockContainer ads--CTitleBarButton {
    qproperty-icon: url("./res/icon/dock/CTitleBarButton.png");
}
ads--CAutoHideDockContainer ads--CTitleBarButton:hover {
    background: #343434;
}

ads--CAutoHideDockContainer ads--CTitleBarButton:pressed {
    background: $ThemeColor;
}

/*****************************************************************************   * CAutoHideDockContainer Titlebar and Buttons   *****************************************************************************/
/*****************************************************************************   * CResizeHandle   *****************************************************************************/
ads--CResizeHandle {
    background: #000;
    min-width:3px;
    max-width:3px;
}
ads--CAutoHideDockContainer[sideBarLocation="0"] ads--CResizeHandle,
ads--CAutoHideDockContainer[sideBarLocation="1"] ads--CResizeHandle,
ads--CAutoHideDockContainer[sideBarLocation="2"] ads--CResizeHandle,
ads--CAutoHideDockContainer[sideBarLocation="3"] ads--CResizeHandle{
    border-width:0;
}
