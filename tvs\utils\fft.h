#pragma once

#include "kissfft/kiss_fft.h"
#include <QVector>
#include <QPair>

inline QPair<QVector<double>, QVector<double>> computeSpectrum(const QVector<double> &signal, double sampleRate) {
    int N = signal.size();
    kiss_fft_cfg cfg = kiss_fft_alloc(N, 0, nullptr, nullptr);
    
    // Use QVector instead of raw arrays
    QVector<kiss_fft_cpx> in(N), out(N);

    // Fill input data
    for (int i = 0; i < N; i++) {
        in[i].r = signal[i]; // Real part
        in[i].i = 0.0;       // Imaginary part
    }

    // Compute FFT
    kiss_fft(cfg, in.data(), out.data());
    free(cfg);

    // Calculate magnitude spectrum
    QVector<double> spectrum(N);
    for (int i = 0; i < N; i++) {
        spectrum[i] = sqrt(out[i].r * out[i].r + out[i].i * out[i].i);
    }

    // Calculate frequency bins (only first half is useful due to Nyquist)
    QVector<double> freqBins(N/2);
    for (int i = 0; i < N/2; i++) {
        freqBins[i] = i * sampleRate / N;
    }

    // Return frequency bins and spectrum (only first half)
    return qMakePair(freqBins, QVector<double>(spectrum.begin(), spectrum.begin() + N/2));
}
