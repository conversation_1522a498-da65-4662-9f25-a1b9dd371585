#include "DocFile.h"
#include "settings.h"

#include <QFileDialog>
#include <QMessageBox>

DocFile::DocFile(QObject *parent)
    : QObject{parent}
{}

bool DocFile::saveDoc(const QString &file)
{
    QString fileName = file;
    if(fileName == "")
    {
        fileName = m_file;
    }

    if(fileName == "")
    {
        fileName = QFileDialog::getSaveFileName( nullptr, tr("保存文件"), "", "青智工程文件(*.qz)");
        if(fileName == "")
        {
            return false;
        }
    }

    try
    {
        QFile f(fileName);
        if(f.open(QFile::WriteOnly))
        {
            QDataStream ds(&f);

            int rightcode = 1;
            ds << rightcode;
            ds << m_equipments << m_tests;
            ds << m_win_state << m_dock_state << m_cen_dock_state;

            f.close();

            m_file = file;
        }
        else
        {
            throw std::runtime_error("file open failed");
        }
    }
    catch (...)
    {
        QMessageBox::critical(nullptr, tr("青智仪器"), "文档保存失败");
        return false;
    }

    QMessageBox::information(nullptr, tr("青智仪器"),
                             tr("文档已保存")
                            );
    return true;
}

bool DocFile::loadDoc(const QString &file)
{
    try
    {
        QFile f(file);
        if(f.open(QFile::ReadOnly))
        {
            QDataStream ds(&f);

            int rightcode = 0;
            ds >> rightcode;
            ds >> m_equipments >> m_tests;
            ds >> m_win_state >> m_dock_state >> m_cen_dock_state;

            f.close();

            m_file = file;
        }
        else
        {
            throw std::runtime_error("file open failed");
        }
    }
    catch (...)
    {
        QMessageBox::critical(nullptr, tr("青智仪器"), "文档加载失败");
        return false;
    }

    return true;
}

bool DocFile::hasEquipmentIp(const QString &ip) const
{
    // return m_equipments.contains(ip);
    auto list = m_equipments.values();
    for(const EquipmentDataPtr& value: list)
    {
        if(value->m_ip == ip)
            return true;
    }

    return false;
}

EquipmentDataPtr DocFile::addEquipment(const QString &id, int type, const QString &name, const QString &ip, const QString &port)
{
    auto data = EquipmentDataPtr(new EquipmentData(id, type, name, ip, port));
    // EquipmentDataPtr::create(id, type, name, ip)
    m_equipments[id] = data;
    return data;
}

bool DocFile::removeEquipment(const QString &id)
{
    m_equipments.remove(id);
    return true;
}

TestDataPtr DocFile::addTest(const QString &id, int type, const QString &name)
{
    auto data = TestDataPtr(new TestData(id, type, name));
    m_tests[id] = data;
    return data;
}

bool DocFile::removeTest(const QString &id)
{
    m_tests.remove(id);
    return true;
}

bool DocFile::hasTestName(const QString &name) const
{
    // return m_tests.contains(name);
    auto list = m_tests.values();
    for(const TestDataPtr& value: list)
    {
        if(value->m_name == name)
            return true;
    }

    return false;
}

EquipmentData::EquipmentData(const QString &id, int type, const QString &name, const QString &ip, const QString &port)
{
    m_id = id;
    m_type = type;
    m_name = name;
    m_ip = ip;
    m_port = port;
}

TestData::TestData(const QString &id, int type, const QString &name)
{
    m_id = id;
    m_type = type;
    m_name = name;
}

