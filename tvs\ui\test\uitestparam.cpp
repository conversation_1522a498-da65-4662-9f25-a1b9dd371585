#include "uitestparam.h"
#include "qcombobox.h"
#include "qlabel.h"
#include "widgets/buttonedit.h"
#include "widgets/fixedit.h"
#include "widgets/switchbutton.h"
#include "widgets/toolbox.h"

#include <QVBoxLayout>

UiTestParam::UiTestParam(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0,0,0,0);

    ToolBox * toolBox = new ToolBox();
    mainLayout->addWidget(toolBox);


    auto w1 = new QWidget();
    toolBox->addItem(w1, "1: 请选择您需要执行的测试类型");

    auto grid1 = new QGridLayout(w1);
    grid1->addWidget(new QLabel("测试类型"), 0, 0);

    auto testType = new QComboBox();
    testType->addItem("低电压穿越测试");
    grid1->addWidget(testType, 0, 1);



    auto w2 = new QWidget();
    toolBox->addItem(w2, "2: 请选择您测试所使用的PA设备");

    auto grid2 = new QGridLayout(w2);
    grid2->addWidget(new QLabel("测试设备"), 0, 0);
    grid2->addWidget(new QLabel("IP地址"), 0, 2);
    grid2->addWidget(new QLabel("测试单元"), 1, 0);
    grid2->addWidget(new QLabel("采样率"), 1, 2);

    auto testEquip = new QComboBox();
    testType->addItem("PAH");
    grid2->addWidget(testEquip, 0, 1);

    auto ipEdit = new ButtonEdit("连接");
    ipEdit->setText("127.0.0.1");
    ipEdit->setValidator(new QRegularExpressionValidator(QRegularExpression("\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b")));
    ipEdit->setInputMask("***************; ");
    grid2->addWidget(ipEdit, 0, 3);

    auto testUnit = new QComboBox();
    testType->addItem("-");
    grid2->addWidget(testUnit, 1, 1);

    auto testSamplingRate = new QComboBox();
    testType->addItem("20K");
    grid2->addWidget(testSamplingRate, 1, 3);



    auto w3 = new QWidget();
    toolBox->addItem(w3, "3: 请设置测试相关的参数");

    auto grid3 = new QGridLayout(w3);
    grid3->addWidget(new QLabel("无功电流注入值(A)"), 0, 0);
    grid3->addWidget(new QLabel("电压跌落/升高至(V)"), 0, 2);
    grid3->addWidget(new QLabel("无功电流注入值(A)"), 1, 0);


    auto testDL = new FixEdit("A", false);
    grid3->addWidget(testDL, 0, 1);

    auto testDY = new FixEdit("V", false);
    grid3->addWidget(testDY, 0, 3);


    auto btnStorage = new SwitchButton();
    grid3->addWidget(btnStorage, 1, 1);
}
