#ifndef PROPDATA_H
#define PROPDATA_H

/******************************************************************************
  File Name     : propchannel.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 测试属性-属性面板
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
class QTreeWidget;
class QTreeWidgetItem;

class PropData : public QWidget
{
    Q_OBJECT
public:
    explicit PropData(QWidget *parent = nullptr);

    void loadTreeFromJson(QTreeWidget *treeWidget, const QJsonObject &jsonObject);
signals:

protected:
    void initTreeByJson(QTreeWidget *tree, QTreeWidgetItem *parentItem, const QJsonValue &value);
};

#endif // PROPDATA_H
