#include "qcpl_plot.h"

#include "qcpl_axis.h"
#include "qcpl_axis_factor.h"
#include "qcpl_colors.h"
#include "qcpl_graph.h"
#include "qcpl_format.h"
#include "qcpl_io_json.h"
#include "cpp_ex/StateGuard.h"
#include "constants.h"

/// Returns true when range is corrected, false when it's unchanged.
static bool correctZeroRange(QCPRange &range, double safeMargin)
{
    auto epsilon = std::numeric_limits<double>::epsilon();
    if (range.size() > epsilon)
        return false;

    // constant line at zero level
    if (qAbs(range.lower) <= epsilon)
    {
        range.lower = -1;
        range.upper = 1;
        return true;
    }

    // constant line at any level
    double delta = qAbs(range.lower) * safeMargin;
    range.lower -= delta;
    range.upper += delta;
    return true;
}

inline QFont defaultTitleFont()
{
    return QFont("sans",
#ifdef Q_OS_MAC
                 16,
#else
                 14,
#endif
                 QFont::Bold);
}

namespace QCPL
{

Plot::Plot(const PlotOptions &opts, QWidget *parent) : QCustomPlot(parent),
    // TODO: make configurable
    _safeMarginsX(1.0 / 100.0),
    _safeMarginsY(5.0 / 100.0),
    _zoomStepX(1.0 / 100.0),
    _zoomStepY(1.0 / 100.0),
    _numberPrecision(10)
{
    foreach (auto axis, defaultAxes())
    {
        initDefault(axis);
        if (opts.replaceDefaultAxes)
        {
            auto fmt = writeAxis(axis);
            auto axisType = axis->axisType();
            axisRect()->removeAxis(axis);
            readAxis(fmt, addAxis(axisType));
        }
    }

    if (!LineGraph::sharedSelectionDecorator())
    {
        // TODO: make selector customizable: line color/width/visibility, points count/color/size/visibility
        auto decorator = new QCPSelectionDecorator;
        decorator->setPen(QPen(QBrush(QColor(0, 0, 255, 120)), 1, Qt::DashLine));

        // decorator->setScatterStyle(QCPScatterStyle(QCPScatterStyle::ssSquare, Qt::black, Qt::black, 6));
        // decorator->setUsedScatterProperties(QCPScatterStyle::spAll);
        LineGraph::setSharedSelectionDecorator(decorator);
    }

    _title = new QCPTextElement(this);
    _title->setMargins({10, 10, 10, 10});
    _title->setSelectable(true);
    _title->setVisible(false);
    plotLayout()->insertRow(0);
    plotLayout()->setRowSpacing(0),
               plotLayout()->setRowStretchFactor(0, 0.01);
    connect(_title, &QCPTextElement::doubleClicked, this, [this]()
    {
        titleTextDlg();
    });

    _backupLayout = new QCPLayoutGrid;
    _backupLayout->setVisible(false);
    _backupLayout->addElement(0, 0, _title);

    legend->setVisible(true);

    setInteractions(QCP::iRangeDrag | QCP::iRangeZoom | QCP::iSelectPlottables |
                    QCP::iSelectAxes | QCP::iSelectItems | QCP::iSelectLegend | QCP::iSelectOther);

    connect(this, SIGNAL(selectionChangedByUser()), this, SLOT(plotSelectionChanged()));
    connect(this, SIGNAL(plottableClick(QCPAbstractPlottable *, int, QMouseEvent *)),
            this, SLOT(rawGraphClicked(QCPAbstractPlottable *)));
    connect(this, SIGNAL(axisDoubleClick(QCPAxis *, QCPAxis::SelectablePart, QMouseEvent *)),
            this, SLOT(axisDoubleClicked(QCPAxis *, QCPAxis::SelectablePart)));

    connect(this, &QCustomPlot::legendClick, this, &Plot::onLegendItemClicked);

    auto titleFont = _title->font();
#ifdef Q_OS_MAC
    titleFont.setPointSize(16);
#else
    titleFont.setPointSize(14);
#endif
    _title->setFont(titleFont);
    _title->setSelectedFont(titleFont);

    initZoomBar();

    m_tracer_line = new QCPItemStraightLine(this);
    m_tracer_line->setSelectable(false);
    m_tracer_line->setLayer(QLatin1String("background"));
    m_tracer_line->setClipToAxisRect(true);
    m_tracer_line->setAntialiased(false);
    m_tracer_line->point1->setCoords(0, 0);
    m_tracer_line->point2->setCoords(0, 0);

    m_tracer_label = new QCPItemText(this);
    m_tracer_label->setPositionAlignment(Qt::AlignTop | Qt::AlignHCenter);

    applyTheme();
}

Plot::~Plot()
{
    delete _backupLayout;
    auto it = _formatters.constBegin();
    while (it != _formatters.constEnd())
    {
        delete it.value();
        it++;
    }
}

QCPAxis *Plot::selectedAxis() const
{
    foreach (auto axis, axisRect()->axes())
        if (axis->selectedParts().testFlag(QCPAxis::spAxis))
            return axis;
    return nullptr;
}

void Plot::mouseDoubleClickEvent(QMouseEvent *event)
{
    QCustomPlot::mouseDoubleClickEvent(event);

    QCPLayerable *selectedLayerable = layerableAt(event->pos(), true);
    if (!selectedLayerable)
    {
        emit emptySpaceDoubleClicked(event);

        if (autolimitsOnEmptySpaceDoubleClick)
        {
            autolimits();
        }
    }
}

QMenu *Plot::findContextMenu(const QPointF &pos)
{
    if (menuTitle && _title->selectTest(pos, false) >= 0)
        return menuTitle;
    if (menuLegend && legend->selectTest(pos, false) >= 0)
        return menuLegend;
    if (menuAxis)
    {
        foreach (auto axis, axisRect()->axes())
        {
            if (axis->selectTest(pos, false) >= 0)
            {
                axisUnderMenu = axis;
                return menuAxis;
            }
        }
    }
    if (menuAxisX && xAxis->selectTest(pos, false) >= 0)
    {
        axisUnderMenu = xAxis;
        return menuAxisX;
    }
    if (menuAxisY && yAxis->selectTest(pos, false) >= 0)
    {
        axisUnderMenu = yAxis;
        return menuAxisY;
    }
    if (menuGraph)
    {
        foreach (auto g, selectedGraphs())
            if (!isService(g))
                return menuGraph;
    }
    auto it = menus.constBegin();
    while (it != menus.constEnd())
    {
        if (it.key()->selectTest(pos, false) > 0)
            return it.value();
        it++;
    }
    return menuPlot;
}

void Plot::contextMenuEvent(QContextMenuEvent *event)
{
    auto menu = findContextMenu(event->pos());
    if (menu)
        menu->popup(event->globalPos());
}

void Plot::resizeEvent(QResizeEvent *event)
{
    QCustomPlot::resizeEvent(event);
    emit resized(event->oldSize(), event->size());
}

void Plot::plotSelectionChanged()
{
    auto allAxes = axisRect()->axes();
    int countX = 0, countY = 0;
    bool axisSelected = false;
    for (auto axis : allAxes)
    {
        if (!axis->visible())
            continue;

        if (axis->orientation() == Qt::Horizontal)
            countX++;
        else
            countY++;

        if (highlightAxesOfSelectedGraphs)
            if (auto a = dynamic_cast<Axis *>(axis); a)
                a->setHightlight(false);

        if (axis->selectedParts().testFlag(QCPAxis::spAxis) ||
                axis->selectedParts().testFlag(QCPAxis::spTickLabels))
        {
            axis->setSelectedParts(QCPAxis::spAxis | QCPAxis::spTickLabels);
            axisRect()->setRangeDragAxes({axis});
            axisRect()->setRangeZoomAxes({axis});
            axisSelected = true;
        }
    }
    if (axisSelected)
        return;

    QList<QCPAxis *> graphAxes;
    for (auto graph : qAsConst(mGraphs))
        if (graph->selected())
            graphAxes << graph->keyAxis() << graph->valueAxis();

    if (graphAxes.empty())
    {
        axisRect()->setRangeDragAxes(allAxes);
        axisRect()->setRangeZoomAxes(allAxes);
        return;
    }

    if (highlightAxesOfSelectedGraphs)
        for (auto axis : qAsConst(graphAxes))
            if (auto a = dynamic_cast<Axis *>(axis); a)
                if ((a->isX() and countX > 1) or (a->isY() and countY > 1))
                    a->setHightlight(true);

    axisRect()->setRangeDragAxes(graphAxes);
    axisRect()->setRangeZoomAxes(graphAxes);
}

void Plot::rawGraphClicked(QCPAbstractPlottable *plottable)
{
    auto g = dynamic_cast<QCPGraph *>(plottable);
    if (_serviceGraphs.contains(g))
        g = nullptr;
    emit graphClicked(g);
}

void Plot::axisDoubleClicked(QCPAxis *axis, QCPAxis::SelectablePart part)
{
    if (autolimitsOnAxisDoubleClick)
    {
        if (axis->orientation() == Qt::Horizontal)
            autolimitsX();
        else
            autolimitsY();

        return;
    }

    if (part == QCPAxis::spAxisLabel)
        axisTextDlg(axis);
    else
        limitsDlg(axis);
}

void Plot::autolimits(QCPAxis *axis, bool replot)
{
    QCPRange totalRange;
    bool isTotalValid = false;
    bool isX = axis->orientation() == Qt::Horizontal;
    for (int i = 0; i < graphCount(); i++)
    {
        auto g = graph(i);

        if (!g->visible())
            continue;

        if (excludeServiceGraphsFromAutolimiting)
            if (_serviceGraphs.contains(g))
                continue;

        if (isX)
        {
            if (g->keyAxis() != axis)
                continue;
        }
        else if (g->valueAxis() != axis)
            continue;

        bool hasRange = false;
        auto range = isX
                     ? g->getKeyRange(hasRange, QCP::sdBoth)
                     : g->getValueRange(hasRange, QCP::sdBoth, QCPRange());
        if (!hasRange)
            continue;

        if (!isTotalValid)
        {
            totalRange = range;
            isTotalValid = true;
        }
        else
            totalRange.expand(range);
    }

    if (!isTotalValid)
        return;

    bool corrected = correctZeroRange(totalRange, safeMargins(axis));
    {
        StateGuard2(m_setting_range, true);
        axis->setRange(totalRange);

        if (!corrected && useSafeMargins)
            extendLimits(axis, safeMargins(axis), false);
    }
    if (replot)
        this->replot();
}

void Plot::extendLimits(QCPAxis *axis, double factor, bool replot)
{
    auto range = axis->range();
    auto delta = range.size() * factor;
    range.upper += delta;
    range.lower -= delta;
    setAxisRange(axis, range);
    if (replot)
        this->replot();
}

void Plot::setLimits(QCPAxis *axis, double min, double max, bool replot)
{
    QCPRange range(min, max);
    range.normalize();
    setAxisRange(axis, range);
    if (replot)
        this->replot();
}

AxisLimits Plot::limits(QCPAxis *axis) const
{
    auto range = axis->range();
    return AxisLimits(range.lower, range.upper);
}

double Plot::safeMargins(QCPAxis *axis)
{
    return axis->orientation() == Qt::Horizontal ? _safeMarginsX : _safeMarginsY;
}

void Plot::setAxisRange(QCPAxis *axis, const QCPRange &range)
{
    QCPRange r = range;
    correctZeroRange(r, safeMargins(axis));
    axis->setRange(r);
}

bool Plot::limitsDlg(QCPAxis *axis)
{
    auto range = axis->range();
    AxisLimitsDlgProps props;
    props.title = tr("Limits of %1").arg(axisIdent(axis));
    props.precision = _numberPrecision;
    props.unit = getAxisUnitString ? getAxisUnitString(axis) : QString();
    if (axisLimitsDlg(range, props))
    {
        setAxisRange(axis, range);
        replot();
        return true;
    }
    return false;
}

bool Plot::limitsDlgXY()
{
    auto range = ((selectedAxis() == yAxis) ? yAxis : xAxis)->range();
    AxisLimitsDlgProps props;
    props.title = tr("Limits for X and Y");
    props.precision = _numberPrecision;
    if (axisLimitsDlg(range, props))
    {
        setAxisRange(xAxis, range);
        setAxisRange(yAxis, range);
        replot();
        return true;
    }
    return false;
}

bool Plot::axisFactorDlg(QCPAxis *axis)
{
    AxisFactorDlgProps props;
    props.title = tr("Factor of %1").arg(axisIdent(axis));
    props.plot = this;
    if (QCPL::axisFactorDlg(axis, props))
    {
        emit modified("Plot::axisFactorDlg");
        return true;
    }
    return false;
}

bool Plot::axisTextDlg(QCPAxis *axis)
{
    AxisFormatDlgProps props;
    props.title = tr("Title of %1").arg(axisIdent(axis));
    props.formatter = formatter(axis);
    props.defaultText = defaultText(axis);
    if (QCPL::axisTextDlg(axis, props))
    {
        emit modified("Plot::axisTextDlg");
        return true;
    }
    return false;
}

bool Plot::axisFormatDlg(QCPAxis *axis)
{
    AxisFormatDlgProps props;
    props.title = tr("Format of %1").arg(axisIdent(axis));
    props.formatter = formatter(axis);
    props.defaultText = defaultText(axis);
    if (formatSaver)
        props.onSaveDefault = [this, axis]()
    {
        formatSaver->saveAxis(axis);
    };
    if (QCPL::axisFormatDlg(axis, props))
    {
        emit modified("Plot::axisFormatDlg");
        return true;
    }
    return false;
}

bool Plot::colorScaleFormatDlg(QCPColorScale *scale)
{
    AxisFormatDlgProps props;
    props.title = tr("%1 Format").arg(axisIdent(scale->axis()));
    props.formatter = formatter(scale->axis());
    props.defaultText = defaultText(scale->axis());
    if (formatSaver)
        props.onSaveDefault = [this, scale]()
    {
        formatSaver->saveColorScale(scale);
    };
    if (QCPL::colorScaleFormatDlg(scale, props))
    {
        emit modified("Plot::colorScaleFormatDlg");
        return true;
    }
    return false;
}

bool Plot::titleTextDlg()
{
    TitleFormatDlgProps props;
    props.title = tr("Title Text");
    props.formatter = formatter(_title);
    props.defaultText = defaultText(_title);
    if (QCPL::titleTextDlg(_title, props))
    {
        emit modified("Plot::titleTextDlg");
        return true;
    }
    return false;
}

bool Plot::titleFormatDlg()
{
    TitleFormatDlgProps props;
    props.title = tr("Title Format");
    props.formatter = formatter(_title);
    props.defaultText = defaultText(_title);
    if (formatSaver)
        props.onSaveDefault = [this]()
    {
        formatSaver->saveTitle(_title);
    };
    if (QCPL::titleFormatDlg(_title, props))
    {
        emit modified("Plot::titleFormatDlg");
        return true;
    }
    return false;
}

bool Plot::legendFormatDlg()
{
    LegendFormatDlgProps props;
    props.title = tr("Legend Format");
    if (formatSaver)
        props.onSaveDefault = [this]()
    {
        formatSaver->saveLegend(legend);
    };
    if (QCPL::legendFormatDlg(legend, props))
    {
        emit modified("Plot::legendFormatDlg");
        return true;
    }
    return false;
}

QString Plot::axisTypeStr(QCPAxis::AxisType type) const
{
    switch (type)
    {
    case QCPAxis::atLeft:
        return tr("Left Axis");
    case QCPAxis::atRight:
        return tr("Right Axis");
    case QCPAxis::atTop:
        return tr("Top Axis");
    case QCPAxis::atBottom:
        return tr("Bottom Axis");
    }
    return "Axis";
}

QString Plot::axisIdent(QCPAxis *axis) const
{
    if (axisIdents.contains(axis))
        return axisIdents[axis];
    auto type = axis->axisType();
    QString typeStr = axisTypeStr(type);
    if (axis == xAxis || axis == xAxis2 || axis == yAxis || axis == yAxis2)
        return typeStr;
    auto axes = axis->axisRect()->axes(type);
    for (int i = 0; i < axes.size(); i++)
        if (axes.at(i) == axis)
            return QString("%1 %2").arg(typeStr).arg(i);
    if (!axis->label().isEmpty())
        return axis->label();
    return tr("Axis");
}

QCPGraph *Plot::makeNewGraph(const QString &title)
{
    auto g = new LineGraph(xAxis, yAxis);
    auto g_zoom = new LineGraph(m_zoom_axis_x, m_zoom_axis_y);
    g_zoom->setData(g->data());
    g_zoom->removeFromLegend();
    g_zoom->setSelectable(QCP::stNone);
    m_zoom_graphs.append(g_zoom);

    auto tracer = new QCPItemTracer(this);
    tracer->setGraph(g);
    tracer->setStyle(QCPItemTracer::tsCircle);
    tracer->setInterpolating(false);
    tracer->setBrush(Qt::NoBrush);
    tracer->setLayer(QLatin1String("overlay"));

    if (graphAutoColors)
    {
        auto clr = nextGraphColor();
        g->setPen(clr);
        g_zoom->setPen(clr);
        tracer->setPen(clr);
    }

    g->setName(title);
    g->setSelectable(_selectionType);

    m_graph_atts[g] = {g_zoom, tracer, nullptr};
    return g;
}

QCPGraph *Plot::makeNewGraph(const QString &title, const GraphData &data, bool replot)
{
    auto g = makeNewGraph(title);
    g->setData(data.x, data.y);
    if (replot)
        this->replot();
    return g;
}

void Plot::updateGraph(QCPGraph *graph, const GraphData &data, bool replot)
{
    graph->setData(data.x, data.y);
    if (replot)
        this->replot();
}

QColor Plot::nextGraphColor()
{
    // if (_nextColorIndex == defaultColorSet().size())
    //     _nextColorIndex = 0;
    // return defaultColorSet().at(_nextColorIndex++);

    return generateColor(_nextColorIndex++, isDarkTheme());
}

QCPGraph *Plot::selectedGraph() const
{
    auto graphs = selectedGraphs();
    return graphs.isEmpty() ? nullptr : graphs.first();
}

void Plot::copyPlotImage()
{
    QImage image(width(), height(), QImage::Format_RGB32);
    QCPPainter painter(&image);
    toPainter(&painter);
    qApp->clipboard()->setImage(image);
}

void Plot::addFormatter(void *target, TextFormatterBase *formatter)
{
    if (_formatters.contains(target))
        qWarning() << "Formatter is already registerd for this target, it will be lost (possible memory leak)";
    _formatters[target] = formatter;
}

void Plot::addTextVar(void *target, const QString &name, const QString &descr, TextVarGetter getter)
{
    if (!_formatters.contains(target))
    {
        bool targetAdded = false;
        foreach (auto axis, axisRect()->axes())
        {
            if (axis == target)
            {
                _formatters[target] = new AxisTextFormatter(axis);
                targetAdded = true;
                break;
            }
        }
        if (!targetAdded and target == _title)
        {
            _formatters[target] = new TitleTextFormatter(_title);
            targetAdded = true;
        }
        if (!targetAdded)
            return;
    }
    _formatters[target]->addVar(name, descr, getter);
}

void Plot::updateTexts()
{
    auto it = _formatters.constBegin();
    while (it != _formatters.constEnd())
    {
        it.value()->format();
        it++;
    }
}

void Plot::updateText(void *target)
{
    auto fmt = formatter(target);
    if (fmt)
        fmt->format();
}

void Plot::setFormatterText(void *target, const QString &text)
{
    auto fmt = formatter(target);
    if (fmt)
        fmt->setText(text);
}

QString Plot::formatterText(void *target) const
{
    auto fmt = formatter(target);
    return fmt ? fmt->text() : QString();
}

void Plot::updateTitleVisibility()
{
    // We can't just hide the title, because the layout will still respect it's size.
    // We can't just extract the title from layout because it will be deleted.
    // So we have to move it into another layout instead.
    QString text;
    if (auto f = formatter(_title); f)
        text = f->text();
    else
        text = _title->text();
    if (!_title->visible() or text.isEmpty())
    {
        // it's ok to get element without checking, but a console warning is printed then
        if (_backupLayout->hasElement(0, 0) and _backupLayout->element(0, 0) == _title)
            return;
        _backupLayout->addElement(0, 0, _title);
    }
    else
    {
        auto mainLayout = plotLayout();
        auto p = titleRC();
        if (mainLayout->hasElement(p.row, p.col) and mainLayout->element(p.row, p.col) == _title)
            return;
        mainLayout->addElement(p.row, p.col, _title);
    }
}

int Plot::graphsCount(GraphCountFlags flags) const
{
    int count = 0;
    for (int i = 0; i < graphCount(); i++)
    {
        auto g = graph(i);
        if (!g->visible() and (flags & COUNT_ONLY_VISIBLE))
            continue;
        if (isService(g) and !(flags & COUNT_SERVICE))
            continue;
        count++;
    }
    return count;
}

AxisFactor Plot::axisFactor(QCPAxis *axis) const
{
    auto factorTicker = dynamic_cast<FactorAxisTicker *>(axis->ticker().data());
    return factorTicker ? factorTicker->factor : AxisFactor();
}

void Plot::setAxisFactor(QCPAxis *axis, const AxisFactor &factor)
{
    auto factorTicker = dynamic_cast<FactorAxisTicker *>(axis->ticker().data());
    if (factorTicker)
    {
        if (isAxisFactorSet(factor))
            factorTicker->factor = factor;
        else
            axis->setTicker(factorTicker->prevTicker);
    }
    else if (isAxisFactorSet(factor))
    {
        auto factorTicker = new FactorAxisTicker(axis->ticker());
        factorTicker->factor = factor;
        axis->setTicker(QSharedPointer<QCPAxisTicker>(factorTicker));
    }
    if (formatAxisTitleAfterFactorSet)
        updateText(axis);
    replot();
}

void Plot::initDefault(QCPAxis *axis)
{
    auto labelsFont = font();
#ifdef Q_OS_MAC
    labelsFont.setPointSize(14);
#else
    labelsFont.setPointSize(10);
#endif
    axis->setLabelFont(labelsFont);
    axis->setSelectedLabelFont(labelsFont);
    axis->setNumberPrecision(_numberPrecision);
}

QCPAxis *Plot::addAxis(QCPAxis::AxisType axisType)
{
    if (axisType == QCPAxis::atBottom && xAxis && !xAxis->visible())
    {
        xAxis->setVisible(true);
        return xAxis;
    }
    if (axisType == QCPAxis::atLeft && yAxis && !yAxis->visible())
    {
        yAxis->setVisible(true);
        return yAxis;
    }
    if (axisType == QCPAxis::atTop && xAxis2 && !xAxis2->visible())
    {
        xAxis2->setVisible(true);
        return xAxis2;
    }
    if (axisType == QCPAxis::atRight && yAxis2 && !yAxis2->visible())
    {
        yAxis2->setVisible(true);
        return yAxis2;
    }
    auto axis = axisRect()->addAxis(axisType, new Axis(axisRect(), axisType));
    axis->setLayer(QLatin1String("axes"));
    axis->grid()->setLayer(QLatin1String("grid"));
    initDefault(axis);
    return axis;
}

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Zoom Bar

QColor Plot::generateColor(int index, bool darkTheme)
{
    static const QList<QColor> baseColorsLight =
    {
        QColor("#17a2a2"), // Teal (Original Measured)
        QColor("#ff7f0e"), // Orange
        QColor("#2ca02c"), // Green
        QColor("#d62728"), // Red
        QColor("#9467bd"), // Purple
        QColor("#8c564b"), // Brown
        QColor("#e377c2"), // Pink
        QColor("#7f7f7f"), // Gray
        QColor("#bcbd22"), // Olive
        QColor("#1f77b4")  // Blue
    };

    static const QList<QColor> baseColorsDark =
    {
        QColor("cyan"), // Original Measured
        QColor("orange"),
        QColor("lightgreen"),
        QColor("red"),
        QColor("magenta"),
        QColor("yellow"),
        QColor("pink"),
        QColor("lightgray"),
        QColor("#dbdb8d"), // Dark Olive
        QColor("#aec7e8")  // Light Blue
    };

    const QList<QColor> &colors = darkTheme ? baseColorsDark : baseColorsLight;
    return colors[index % colors.size()];
}

QCPRange Plot::extendRange(const QCPRange &range, double factor)
{
    auto delta = range.size() * factor;
    return QCPRange(range.lower - delta, range.upper + delta);
}

void Plot::removeGraphAndAtts(QCPGraph *graph)
{
    if (auto it = m_graph_atts.find(graph); it != m_graph_atts.end())
    {
        if (auto tracer = it->second.tracer)
            removeItem(tracer);
        if (auto label = it->second.label)
            removeItem(label);
        if (auto g_zoom = it->second.graph)
        {
            m_zoom_graphs.removeOne(g_zoom);
            removeGraph(g_zoom);
        }
        m_graph_atts.erase(it);
    }
    removeGraph(graph);
}

void Plot::initZoomBar()
{
    m_zoom_rect = new QCPAxisRect(this);
    m_zoom_rect->setupFullAxesBox(true);
    m_zoom_rect->setMaximumSize(QSize(QWIDGETSIZE_MAX, 30));
    m_zoom_rect->axis(QCPAxis::atLeft)->setVisible(false);
    m_zoom_rect->axis(QCPAxis::atRight)->setVisible(false);
    m_zoom_rect->setRangeDrag({});
    m_zoom_rect->setRangeZoom({});
    plotLayout()->addElement(2, 0, m_zoom_rect);

    m_zoom_axis_x = m_zoom_rect->axis(QCPAxis::atBottom);
    m_zoom_axis_y = m_zoom_rect->axis(QCPAxis::atLeft);

    // 选区填充
    m_range_rect = new QCPItemRect(this);
    m_range_rect->setSelectable(false);
    m_range_rect->setLayer(QLatin1String("overlay"));

    m_range_rect->topLeft->setAxes(m_zoom_axis_x, m_zoom_axis_y);
    m_range_rect->bottomRight->setAxes(m_zoom_axis_x, m_zoom_axis_y);
    m_range_rect->setBrush(QColor(0, 0, 255, 100));
    m_range_rect->setPen(Qt::NoPen);
    m_range_rect->setClipToAxisRect(false);

    // m_range_rect->topLeft->setType(QCPItemPosition::ptAxisRectRatio);
    // m_range_rect->bottomRight->setType(QCPItemPosition::ptAxisRectRatio);
    auto x0 = m_zoom_axis_x->range().lower;
    auto x1 = m_zoom_axis_x->range().upper;
    auto y0 = m_zoom_axis_y->range().lower;
    auto y1 = m_zoom_axis_y->range().upper;
    m_range_rect->topLeft->setCoords(x0, y0);
    m_range_rect->bottomRight->setCoords(x1, y1);

    // 标签
    m_range_label = new QCPItemText(this);
    m_range_label->setSelectable(false);
    m_range_label->position->setAxes(m_zoom_axis_x, m_zoom_axis_y);
    m_range_label->setPositionAlignment(Qt::AlignTop | Qt::AlignHCenter);
    m_range_label->setClipToAxisRect(false);
    m_range_label->setFont(QFont(font().family(), 9));
    m_range_label->setColor(Qt::black);
    // 确保标签在最上层显示
    m_range_label->setLayer(QLatin1String("overlay"));

    connect(xAxis, QOverload<const QCPRange &>::of(&QCPAxis::rangeChanged), this, [=](const QCPRange &newRange)
    {
        // 防止进入死循环：只有主动交互（如缩放）才更新 m_range_rect
        if (!m_setting_range && m_zoom_mode == ZoomMode::None)
        {
            setRange(newRange.lower, newRange.upper);
        }
    });

    m_zoom_mode = ZoomMode::None;
}

void Plot::applyTheme(bool dark)
{
    m_dark_theme = dark;
    // --- Apply Theme Colors & Base Plot Setup ---
    QColor bgColor, axisColor, tickColor, gridColor, labelColor, textColor, lineColor;
    if (dark)
    {
        bgColor = Constants::DARK_BG_COLOR;
        axisColor = Constants::DARK_AXIS_COLOR;
        tickColor = Constants::DARK_TICK_COLOR;
        gridColor = Constants::DARK_GRID_COLOR;
        labelColor = Constants::DARK_TEXT_COLOR;
        textColor = Constants::DARK_TEXT_COLOR;
        lineColor = QColor(200, 200, 255);
    }
    else
    {
        bgColor = Constants::LIGHT_BG_COLOR;
        axisColor = Constants::LIGHT_AXIS_COLOR;
        tickColor = Constants::LIGHT_TICK_COLOR;
        gridColor = Constants::LIGHT_GRID_COLOR;
        labelColor = Constants::LIGHT_TEXT_COLOR;
        textColor = Constants::LIGHT_TEXT_COLOR;
        lineColor = QColor(50, 50, 150);
    }

    setBackground(bgColor);
    axisRect()->setBackground(bgColor);

    if (m_tracer_line)
    {
        m_tracer_line->setPen(QPen(lineColor, 1, Qt::DashLine));
        m_tracer_label->setColor(labelColor);
        m_tracer_label->setBrush(QColor(bgColor.red(), bgColor.green(), bgColor.blue(), 180));
        m_tracer_label->setPadding(QMargins(4, 2, 4, 2));
    }

    // --- Update Axes Appearance ---
    xAxis->setLabelColor(labelColor);
    xAxis->setBasePen(QPen(tickColor));
    xAxis->setTickPen(QPen(tickColor));
    xAxis->setSubTickPen(QPen(tickColor));
    xAxis->setTickLabelColor(tickColor);
    xAxis->grid()->setPen(QPen(gridColor, 0.5));
    xAxis->grid()->setSubGridPen(QPen(gridColor, 0.3, Qt::DotLine));

    yAxis->setLabelColor(labelColor);
    yAxis->setBasePen(QPen(tickColor));
    yAxis->setTickPen(QPen(tickColor));
    yAxis->setSubTickPen(QPen(tickColor));
    yAxis->setTickLabelColor(tickColor);
    yAxis->grid()->setPen(QPen(gridColor, 0.5));
    yAxis->grid()->setSubGridPen(QPen(gridColor, 0.3, Qt::DotLine));

    yAxis2->setLabelColor(labelColor);
    yAxis2->setBasePen(QPen(tickColor));
    yAxis2->setTickPen(QPen(tickColor));
    yAxis2->setSubTickPen(QPen(tickColor));
    yAxis2->setTickLabelColor(tickColor);

    auto zoom_axis_x1 = m_zoom_rect->axis(QCPAxis::atTop);
    QList<QCPAxis *> zoom_axis_list = {m_zoom_axis_x, zoom_axis_x1};
    for (auto axis : zoom_axis_list)
    {
        axis->setBasePen(QPen(tickColor));
        axis->setTickPen(QPen(tickColor));
        axis->setSubTickPen(QPen(tickColor));
        axis->setTickLabelColor(tickColor);
        axis->grid()->setPen(QPen(gridColor, 0.5));
        axis->grid()->setSubGridPen(QPen(gridColor, 0.3, Qt::DotLine));
    }

    // --- Update Zoom Bar Appearance ---
    m_zoom_rect->setBackground(bgColor);
    // m_range_rect->setBrush(QColor(0, 0, 255, 60));
    // m_range_label->setColor(textColor);

    // 在黑色背景下使用更明显的颜色和透明度
    if (dark)
    {
        // 深色主题下使用更亮的蓝色，增加透明度以便更明显
        m_range_rect->setBrush(QColor(80, 120, 255, 100));
        // 为选择区域添加边框以增强可见性
        m_range_rect->setPen(QPen(QColor(120, 160, 255), 1, Qt::SolidLine));
    }
    else
    {
        // 浅色主题下使用深蓝色
        m_range_rect->setBrush(QColor(0, 0, 255, 60));
        m_range_rect->setPen(Qt::NoPen);
    }

    // 设置标签颜色并确保在最上层显示
    m_range_label->setColor(textColor);
    // 增加标签背景以提高可读性
    m_range_label->setBrush(QColor(bgColor.red(), bgColor.green(), bgColor.blue(), 180));
    m_range_label->setPadding(QMargins(4, 2, 4, 2));
}

// Handle clicks on legend items to toggle visibility using strikethrough
void Plot::onLegendItemClicked(QCPLegend *legend, QCPAbstractLegendItem *item, QMouseEvent *event)
{
    Q_UNUSED(legend)
    Q_UNUSED(event)
    if (!item || !this->legend)
        return; // Safety checks

    QCPPlottableLegendItem *plItem = qobject_cast<QCPPlottableLegendItem *>(item);
    if (!plItem)
        return; // Only handle plottable items for now

    // 切换显示/隐藏
    auto g = plItem->plottable();
    g->setVisible(!g->visible());
    if (auto it = m_graph_atts.find(qobject_cast<QCPGraph *>(g)); it != m_graph_atts.end())
    {
        auto &att = it->second; // 引用，避免复制
        if (auto g_zoom = att.graph)
            g_zoom->setVisible(g->visible());
        if (auto tracer = att.tracer)
            tracer->setVisible(g->visible());
        if (auto label = att.label)
            label->setVisible(g->visible());
    }

    QFont itemFont = plItem->font();
    itemFont.setStrikeOut(!g->visible());
    plItem->setFont(itemFont);

    QFont selectedFont = plItem->selectedFont();
    selectedFont.setStrikeOut(!g->visible());   
    plItem->setSelectedFont(selectedFont);

    replot(); // Redraw the plot (will show/hide graphs based on isVisible and update legend appearance)
}

QPair<double, double> Plot::currentRange() const
{
    return {m_range_rect->topLeft->key(), m_range_rect->bottomRight->key()};
}

double Plot::zoom_pixe_to_coord(int xPixel) const
{
    return m_zoom_axis_x->pixelToCoord(xPixel);
}

int Plot::zoom_coord_to_pixel(double xCoord) const
{
    return m_zoom_axis_x->coordToPixel(xCoord);
}

void Plot::setRange(double x1, double x2)
{
    double minX = m_zoom_axis_x->range().lower;
    double maxX = m_zoom_axis_x->range().upper;
    double minY = m_zoom_axis_y->range().lower;
    double maxY = m_zoom_axis_y->range().upper;

    x1 = qBound(minX, x1, maxX);
    x2 = qBound(minX, x2, maxX);

    if (x1 > x2)
        std::swap(x1, x2);

    m_range_rect->topLeft->setCoords(x1, minY);
    m_range_rect->bottomRight->setCoords(x2, maxY);

    // ✅ 同步更新主图的显示范围
    {
        StateGuard2(m_setting_range, true);
        xAxis->setRange(x1, x2);
    }

    update_label();
    replot();
}

void Plot::update_label()
{
    QPointF p1 = m_range_rect->topLeft->coords();
    QPointF p2 = m_range_rect->bottomRight->coords();
    m_range_label->position->setCoords((p1.x() + p2.x()) / 2, p2.y());
    double x1 = m_range_rect->topLeft->key();
    double x2 = m_range_rect->bottomRight->key();
    m_range_label->setText(QString("[%1 ~ %2]").arg(x1, 0, 'f', 2).arg(x2, 0, 'f', 2));
}

const int tolerance = 5;

void Plot::mousePressEvent(QMouseEvent *event)
{
    // const double tolerance = (axis->range().size()) / axis->axisRect()->width() * 5; // 拟合屏幕5像素范围
    QRect rect = m_zoom_rect->rect();
    int y = event->pos().y();
    if (y < rect.top() || y > rect.bottom())
    {
        m_zoom_mode = ZoomMode::None;
        QCustomPlot::mousePressEvent(event);
        return;
    }

    double x1 = m_range_rect->left->pixelPosition().x();
    double x2 = m_range_rect->right->pixelPosition().x();
    int x = event->pos().x();

    // 优先靠近哪个边就拖哪个
    double distToLeft = qAbs(x - x1);
    double distToRight = qAbs(x - x2);

    // 记录拖动开始时的像素和坐标
    m_drag_x = x;

    if (distToLeft < tolerance && distToLeft <= distToRight)
        m_zoom_mode = ZoomMode::Left;
    else if (distToRight < tolerance && distToRight < distToLeft)
        m_zoom_mode = ZoomMode::Right;
    else if (x > x1 && x < x2)
        m_zoom_mode = ZoomMode::Move;
    else
        m_zoom_mode = ZoomMode::None;

    QCustomPlot::mousePressEvent(event);
}

double getNearestY(QCPGraph *graph, double x)
{
    if (!graph || graph->data()->isEmpty())
        return qQNaN();

    auto data = graph->data();
    auto it = data->findBegin(x); // 第一个 key >= x 的点

    if (it == data->constBegin())
        return it->value;
    if (it == data->constEnd())
        return (it - 1)->value;

    auto itNext = it;
    auto itPrev = it - 1;

    // 比较前后两个点哪个更接近 x
    if (std::abs(itNext->key - x) < std::abs(itPrev->key - x))
        return itNext->value;
    else
        return itPrev->value;
}

void Plot::setShowTracers(bool show)
{
    m_show_tracers = show;
    if (!m_show_tracers)
    {
        m_tracer_line->setVisible(false);
        for (auto &it : m_graph_atts)
        {
            if (auto tracer = it.second.tracer)
                tracer->setVisible(false);
        }
    }
}

void Plot::mouseMoveEvent(QMouseEvent *event)
{
    int x = event->pos().x();
    int y = event->pos().y();

    if (m_zoom_mode == ZoomMode::None)
    {
        QRect rect = m_zoom_rect->rect();

        if (y >= rect.top() && y <= rect.bottom())
        {
            int rangeLeftPixel = m_range_rect->left->pixelPosition().x();
            int rangeRightPixel = m_range_rect->right->pixelPosition().x();
            // 更新鼠标样式
            if (qAbs(x - rangeLeftPixel) <= tolerance || qAbs(x - rangeRightPixel) <= tolerance)
            {
                setCursor(Qt::SizeHorCursor); // 左右拖动
            }
            else if (x > rangeLeftPixel && x < rangeRightPixel)
            {
                setCursor(Qt::OpenHandCursor); // 拖动整个区域
            }
            else
            {
                unsetCursor(); // 恢复默认
            }
        }
        else
        {
            unsetCursor(); // 恢复默认

            if (m_show_tracers && m_tracer_line)
            {
                QRect plot_rect = axisRect()->rect();
                if (plot_rect.contains(event->pos()))
                {
                    double coord_x = xAxis->pixelToCoord(x);

                    m_tracer_line->setVisible(true);

                    QString labelText = QString("x: %1").arg(coord_x, 0, 'f', 2);

                    for (auto &it : m_graph_atts)
                    {
                        if (auto graph = it.first; graph->visible())
                        {
                            if (auto tracer = it.second.tracer)
                            {
                                tracer->setGraphKey(coord_x);
                                tracer->updatePosition();
                                labelText += QString("\n%1: %2").arg(graph->name()).arg(tracer->position->value(), 0, 'f', 3);
                            }
                        }
                    }

                    m_tracer_line->point1->setCoords(coord_x, yAxis->range().lower);
                    m_tracer_line->point2->setCoords(coord_x, yAxis->range().upper);

                    m_tracer_label->setText(labelText);
                    m_tracer_label->position->setCoords(coord_x, yAxis->range().upper);

                    replot();
                }
                else
                {
                    // m_line_v->setVisible(false);
                    // m_tracer->setVisible(false);
                }
            }
        }
    }
    else
    {
        double min_x = m_zoom_axis_x->range().lower;
        double max_x = m_zoom_axis_x->range().upper;
        double min_len = (max_x - min_x) / 1000.0;
        const double epsilon = 1e-8;

        double dx = zoom_pixe_to_coord(x) - zoom_pixe_to_coord(m_drag_x);
        m_drag_x = x;

        double old_x1 = m_range_rect->topLeft->key();
        double old_x2 = m_range_rect->bottomRight->key();

        // min_len 会变化（当range变化时），所以要修正 x1, x2，避免 qBound Q_ASSERT 异常
        if (old_x2 - old_x1 < min_len)
        {
            if ((old_x1 + old_x2) > (min_x + max_x))
                old_x1 = old_x2 - min_len;
            else
                old_x2 = old_x1 + min_len;
        }

        double new_x1 = old_x1;
        double new_x2 = old_x2;

        if (m_zoom_mode == ZoomMode::Left)
        {
            new_x1 = qBound(min_x, old_x1 + dx, new_x2 - min_len);
        }
        else if (m_zoom_mode == ZoomMode::Right)
        {
            new_x2 = qBound(new_x1 + min_len, old_x2 + dx, max_x);
        }
        else if (m_zoom_mode == ZoomMode::Move)
        {
            double width = old_x2 - old_x1;

            // 长度固定
            // new_x1 = qBound(min_x, old_x1 + dx, max_x - width);
            // new_x2 = new_x1 + width;

            // 长度可变
            if (dx > 0)
            {
                new_x2 = qBound(min_x, old_x2 + dx, max_x);
                new_x1 = qBound(min_x, old_x1 + dx, new_x2 - min_len);
            }
            else
            {
                new_x1 = qBound(min_x, old_x1 + dx, max_x);
                new_x2 = qBound(new_x1 + min_len, old_x2 + dx, max_x);
            }
        }

        setRange(new_x1, new_x2);
    }

    QCustomPlot::mouseMoveEvent(event);
}

void Plot::mouseReleaseEvent(QMouseEvent *event)
{
    m_zoom_mode = ZoomMode::None;
    QCustomPlot::mouseReleaseEvent(event);
}

void Plot::wheelEvent(QWheelEvent *event)
{
    if (!m_zoom_rect->rect().contains(event->position().toPoint()))
        return QCustomPlot::wheelEvent(event); // 保持默认行为（在其他区域）

    const double zoomFactor = 0.85; // <1 缩小，>1 放大
    double factor = (event->angleDelta().y() > 0) ? zoomFactor : (1.0 / zoomFactor);

    double x_mouse = zoom_pixe_to_coord(event->position().x());
    double x_min = m_range_rect->topLeft->key();
    double x_max = m_range_rect->bottomRight->key();
    double length = x_max - x_min;

    double ratio = (x_mouse - x_min) / length;
    double newLength = length * factor;

    double new_x_min = x_mouse - ratio * newLength;
    double new_x_max = x_mouse + (1 - ratio) * newLength;

    double minX = m_zoom_axis_x->range().lower;
    double maxX = m_zoom_axis_x->range().upper;
    new_x_min = qBound(minX, new_x_min, maxX);
    new_x_max = qBound(minX, new_x_max, maxX);

    m_zoom_mode = ZoomMode::Wheel;
    setRange(new_x_min, new_x_max);
    m_zoom_mode = ZoomMode::None;

    event->accept();
}

void Plot::autoZoomLimits()
{
    QCPRange total_range_x, total_range_y;
    bool isTotalValid = false;
    for (int i = 0; i < graphCount(); i++)
    {
        auto g = graph(i);

        if (!g->visible())
            continue;

        if (m_zoom_graphs.contains(g))
            continue;

        bool hasRange = false;
        auto range_x = g->getKeyRange(hasRange, QCP::sdBoth);
        if (!hasRange)
            continue;

        auto range_y = g->getValueRange(hasRange, QCP::sdBoth, QCPRange());

        if (!isTotalValid)
        {
            total_range_x = range_x;
            total_range_y = range_y;
            isTotalValid = true;
        }
        else
        {
            total_range_x.expand(range_x);
            total_range_y.expand(range_y);
        }
    }

    if (!isTotalValid)
        return;

    QCPRange old_range_x = m_zoom_axis_x->range();
    QCPRange new_range_x = extendRange(total_range_x, _safeMarginsX);
    m_zoom_axis_x->setRange(new_range_x);

    QCPRange old_range_y = m_zoom_axis_y->range();
    QCPRange new_range_y = extendRange(total_range_y, _safeMarginsY);
    m_zoom_axis_y->setRange(new_range_y);

    // 获取当前选择区域的范围
    double currentMin = m_range_rect->topLeft->key();
    double currentMax = m_range_rect->bottomRight->key();

    // 计算当前选择区域在旧范围中的相对位置（0.0到1.0之间）
    double relativeMin = 0.0;
    double relativeMax = 0.0;
    if (old_range_x.size() > 0)
    {
        relativeMin = (currentMin - old_range_x.lower) / old_range_x.size();
        relativeMax = (currentMax - old_range_x.lower) / old_range_x.size();

        // 根据相对位置计算新的选择区域范围
        double newMin = new_range_x.lower + relativeMin * new_range_x.size();
        double newMax = new_range_x.lower + relativeMax * new_range_x.size();

        setRange(newMin, newMax);
    }
}

} // namespace QCPL
