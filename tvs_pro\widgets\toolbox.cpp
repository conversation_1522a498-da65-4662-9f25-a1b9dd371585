#include "toolbox.h"
#include "widgets/toolpage.h"
#include <QScrollArea>
#include <QVBoxLayout>

ToolBox::ToolBox(QWidget *parent) :
    QWidget(parent)
{
    setAttribute(Qt::WA_StyledBackground);
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);

    QScrollArea *scroll = new QScrollArea();
    mainLayout->addWidget(scroll);

    QWidget *widget = new QWidget();
    scroll->setWidget(widget);

    QVBoxLayout *vBoxLayout = new QVBoxLayout(widget);
    vBoxLayout->setContentsMargins(0, 0, 0, 0);

    m_layout = new QVBoxLayout();
    m_layout->setContentsMargins(0, 0, 0, 0);
    m_layout->setSpacing(0);

    vBoxLayout->addLayout(m_layout);
    vBoxLayout->addStretch(1);

    scroll->setWidgetResizable(true);
}

void ToolBox::addItem(QWidget *widget, const QString &title)
{
    ToolPage *page = new ToolPage();
    page->addWidget(title, widget);

    m_layout->addWidget(page);
}
