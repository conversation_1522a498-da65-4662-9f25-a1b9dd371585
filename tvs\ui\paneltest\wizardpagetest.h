#ifndef WIZARDPAGETEST_H
#define WIZARDPAGETEST_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        : 
  Created       : 2024-04-16
  Last Modified :
  Description   : 测试新建向导-选择类型
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWizardPage>
class QButtonGroup;

class WizardPageTest : public QWizardPage
{
    Q_OBJECT
public:
    WizardPageTest();

    Q_PROPERTY(int test_type READ get_test_type NOTIFY test_type_changed)

    int get_test_type();

protected:
    QButtonGroup *m_buttonGroup;

signals:
    void test_type_changed(int);
};

#endif // WIZARDPAGETEST_H
