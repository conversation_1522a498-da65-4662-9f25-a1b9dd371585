#pragma once
#include "anonymous_var.h"


template <class T>
class StateGuardT
{
	T&  m_val;        // Variable
	T   m_oldValue;   // Old value
public:
	/** Arguments:
	variable (I) Variable to save.
	*/
	StateGuardT( T& variable )
		: m_val( variable )
	{
		m_oldValue = m_val;
	}
	/** Arguments:
	newValue (I) New value for variable.
	*/
	StateGuardT( T& variable, const T& newValue )
		: m_val( variable )
	{
		m_oldValue = m_val;
		m_val = newValue;
	}
	StateGuardT( StateGuardT<T> &&val ) 
		: m_oldValue(std::move(val.m_oldValue))
		, m_val(std::move(val.m_val))
	{
	}
	~StateGuardT()
	{
		m_val = m_oldValue;
	}

	StateGuardT<T>& operator=( StateGuardT<T> &&val )
	{
		if (this == &val)
			return *this;

		m_oldValue	= std::move(val.m_oldValue);
		m_val		= std::move(val.m_val);
		return *this;
	}
	/** Description:
	Returns the saved value of the variable.
	*/
	operator const T&() const
	{
		return m_oldValue;
	}
};

template<typename T>
inline StateGuardT<T>* MakeStateGuardPtr(T& variable, const T& newValue)
{
	return new StateGuardT<T>(variable, newValue);
}

template<typename T>
inline StateGuardT<T> MakeStateGuard(T& variable, const T& newValue)
{
	return StateGuardT<T>(variable, newValue);
}

template<typename T>
inline StateGuardT<T> MakeStateGuard(T& variable)
{
	return StateGuardT<T>(variable);
}

#define StateGuard(variable)			auto A_VAR(state_guard_) = MakeStateGuard(variable)
#define StateGuard2(variable, newValue)	auto A_VAR(state_guard_) = MakeStateGuard(variable, newValue)

