#include "uiproject.h"
#include "qlabel.h"
#include "ui/project/uipjinfo.h"
#include "ui/project/uipjnew.h"
#include "ui/project/uipjopen.h"
#include "ui/project/uipjoption.h"

#include <widgets/qnavigationwidget.h>

#include <MainWindow.h>
#include <QApplication>
#include <QFileDialog>
#include <QMessageBox>

UiProject::UiProject(QWidget *parent)
    : QWidget{parent}
{
    // 创建主窗口
    auto layout = new QVBoxLayout(this);
    layout->setContentsMargins(0,0,0,0);

    m_project_widget = new QNavigationWidget();
    layout->addWidget(m_project_widget);

    // 创建页面
    QWidget *page1 = new UiPjInfo();

    QWidget *page2 = new UiPjNew();

    QWidget *pj_open = new UiPjOpen();


    m_project_widget->addTab(page1, "信息", "信息", "./res/icon/project/info_normal.png");
    m_project_widget->addTab(page2, "新建", "新建", "./res/icon/project/create_normal.png");
    m_project_widget->addTab(pj_open, "打开", "打开", "./res/icon/project/open_normal.png");
    m_project_widget->addTab(new QLabel(), "保存", "保存", "./res/icon/project/save_normal.png");
    m_project_widget->addTab(new QLabel(), "另存为...", "另存为...", "./res/icon/project/saveto_normal.png");
    m_project_widget->addTab(new QLabel(), "关闭", "关闭", "./res/icon/project/close_nromal.png");


    QWidget *pj_option = new UiPjOption();
    m_project_widget->addTab(pj_option, "选项", "选项", "./res/icon/project/option_normal.png");

    m_project_widget->setCurrentIndex(0);

    connect(m_project_widget, &QNavigationWidget::currentIndexChanged, [this](int index)
    {
        switch (index)
        {
        case 3:
        {
            MainWindow::instance->saveProject();
        }
        break;
        case 4:
        {
            MainWindow::instance->saveProjectAs();
        }

        break;
        case 5:
        {
            int ret = QMessageBox::warning(this, tr("青智仪器"),
                                           tr("文档已修改\n"
                                              "是否保存更改？"),
                                           QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel,
                                           QMessageBox::Save
                                          );

            if(ret == QMessageBox::Save)
            {
                MainWindow::instance->saveProject();
            }

            if(ret != QMessageBox::Cancel)
            {
                QApplication::quit();
            }
        }
        break;
        default:
            break;
        }
    });




}
