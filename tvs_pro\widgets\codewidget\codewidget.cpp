#include "codewidget.h"
#include <Qsci/qscilexercpp.h>
#include <Qsci/qsciscintilla.h>
#include <Qsci/qscilexerpython.h>
#include <Qsci/qscilexerd.h>
#include <Qsci/qscistyledtext.h>
#include <Qsci/qsciapis.h>

#include <QString>
#include <QKeyEvent>
#include <QTextCursor>
#include <QScrollBar>
#include <QVBoxLayout>
#include <QMessageBox>
#include <QRegularExpression>

// 在自定义词法分析器中添加额外逻辑
class SmartPythonLexer : public QsciLexerPython
{
public:
    using QsciLexerPython::QsciLexerPython;

    QStringList userClasses = {"await", "async", "split"}; // 存储用户定义的类名、关键字

    const char *keywords(int set) const override
    {
        if (set == 2)
        {
            // >>> dir(__builtins__)
            static QString keywords = "ArithmeticError AssertionError AttributeError BaseException BlockingIOError BrokenPipeError BufferError BytesWarning ChildProcessError ConnectionAbortedError ConnectionError ConnectionRefusedError ConnectionResetError DeprecationWarning EOFError Ellipsis EncodingWarning EnvironmentError Exception False FileExistsError FileNotFoundError FloatingPointError FutureWarning GeneratorExit IOError ImportError ImportWarning IndentationError IndexError InterruptedError IsADirectoryError KeyError KeyboardInterrupt LookupError MemoryError ModuleNotFoundError NameError None NotADirectoryError NotImplemented NotImplementedError OSError OverflowError PendingDeprecationWarning PermissionError ProcessLookupError RecursionError ReferenceError ResourceWarning RuntimeError RuntimeWarning StopAsyncIteration StopIteration SyntaxError SyntaxWarning SystemError SystemExit TabError TimeoutError True TypeError UnboundLocalError UnicodeDecodeError UnicodeEncodeError UnicodeError UnicodeTranslateError UnicodeWarning UserWarning ValueError Warning WindowsError ZeroDivisionError __build_class__ __debug__ __doc__ __import__ __loader__ __name__ __package__ __spec__ abs aiter all anext any ascii bin bool breakpoint bytearray bytes callable chr classmethod compile complex copyright credits delattr dict dir divmod enumerate eval exec exit filter float format frozenset getattr globals hasattr hash help hex id input int isinstance issubclass iter len license list locals map max memoryview min next object oct open ord pow print property quit range repr reversed round set setattr slice sorted staticmethod str sum super tuple type vars zip";

            QString keywordsStr = keywords + " " + userClasses.join(" ");
            static QByteArray keywordsData = keywordsStr.toLatin1(); // static QByteArray
            return keywordsData.constData();                         // 返回持久化的指针
        }
        return QsciLexerPython::keywords(set);
    }
};

PythonFunctionCallHighlighter::PythonFunctionCallHighlighter(QsciScintilla *editor, QObject *parent)
    : QObject(parent), m_editor(editor)
{
    initIndicator();
    // 无效
    // connect(m_editor, &QsciScintilla::textChanged, this, &PythonFunctionCallHighlighter::scheduleHighlight);
  
    // 有效
    connect(m_editor, SIGNAL(textChanged()), this, SLOT(scheduleHighlight()));

    scheduleHighlight();
}

void PythonFunctionCallHighlighter::setEnabled(bool enabled)
{
    m_enabled = enabled;
    if (enabled)
        scheduleHighlight();
    else
        m_editor->SendScintilla(QsciScintilla::SCI_INDICATORCLEARRANGE, 0, m_editor->text().toUtf8().length());
}

void PythonFunctionCallHighlighter::initIndicator()
{
    QColor color(220, 220, 170);
    m_editor->SendScintilla(QsciScintilla::SCI_INDICSETSTYLE, m_indicatorIndex, QsciScintilla::INDIC_TEXTFORE);
    m_editor->SendScintilla(QsciScintilla::SCI_INDICSETFORE, m_indicatorIndex, color);
    m_editor->SendScintilla(QsciScintilla::SCI_INDICSETUNDER, m_indicatorIndex, true);
}

void PythonFunctionCallHighlighter::scheduleHighlight()
{
    if (!m_enabled)
        return;

    QTimer::singleShot(200, this, [this]()
                       { highlightCalls(); });
}

int PythonFunctionCallHighlighter::charPosToByteOffset(const QString &text, int charPos)
{
    return text.left(charPos).toUtf8().size();

}

void PythonFunctionCallHighlighter::highlightCalls()
{
    QString text = m_editor->text();
    QByteArray utf8 = text.toUtf8();

    m_editor->SendScintilla(QsciScintilla::SCI_SETINDICATORCURRENT, m_indicatorIndex);
    m_editor->SendScintilla(QsciScintilla::SCI_INDICATORCLEARRANGE, 0, utf8.size());

    QRegularExpression re(R"(\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\()");
    auto it = re.globalMatch(text);

    while (it.hasNext())
    {
        auto match = it.next();
        int charStart = match.capturedStart(1);
        int charLen = match.capturedLength(1);

        int byteStart = charPosToByteOffset(text, charStart);
        int byteLen = charPosToByteOffset(text, charStart + charLen) - byteStart;

        m_editor->SendScintilla(QsciScintilla::SCI_SETINDICATORCURRENT, m_indicatorIndex);
        m_editor->SendScintilla(QsciScintilla::SCI_INDICATORFILLRANGE, byteStart, byteLen);
    }
}


CodeWidget::CodeWidget(QWidget *parent)
    : QWidget(parent)
{

    m_scintilla = new QsciScintilla(this);
    m_cpp_lexer = new QsciLexerCPP(this);

    // 动态添加用户类名
    m_py_lexer = new SmartPythonLexer(this);
    // m_py_lexer->userClasses << "MyClass" << "Database";

    m_default_lexer = new QsciLexerD(this);
    MapLexerColor = createDefaultColorMap();

    InitLexer();
    InitTextEdit();

    // 连接光标位置变化信号到自定义槽函数
    if (this->LineTagIsOpen)
    {
        connect(m_scintilla, &QsciScintilla::cursorPositionChanged, this, &CodeWidget::highlightCurrentLine);
        qDebug() << "Line tag has open";
    }
    else
    {
        qDebug() << "Line tag has close";
    }

    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->addWidget(m_scintilla);
    layout->setContentsMargins(0, 0, 0, 0); // 可选：移除布局的边距
    setLayout(layout);

    // 安装事件过滤器,模拟keyEvent
    m_scintilla->installEventFilter(this);


    // 无效
    // connect(m_scintilla, &QsciScintilla::textChanged, this, &CodeWidget::onTextChanged);

    // 有效
    connect(m_scintilla, SIGNAL(textChanged()), this, SLOT(onTextChanged()));

    // 添加函数调用高亮
    auto funcHighlighter = new PythonFunctionCallHighlighter(m_scintilla, this);
}

CodeWidget::~CodeWidget()
{
}

void CodeWidget::onTextChanged()
{
    qDebug() << "text changed!";
}

void CodeWidget::InitLexer()
{

    /******************************set cppLexer*********************************************/
    m_cpp_lexer->setDefaultColor(DefaultTextColor); /// 默认字体颜色
    m_cpp_lexer->setDefaultPaper(PaperColor);       /// 默认背景色
    m_cpp_lexer->setFont(QFont("Consolas", 14));    /// 默认字体

    m_cpp_lexer->setFoldAtElse(true);  // 在 else 处折叠代码块
    m_cpp_lexer->setFoldCompact(true); // 折叠时隐藏空行

    //.................................................................

    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Default), QsciLexerCPP::Default);

    // 设置注释颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Comment), QsciLexerCPP::Comment);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::CommentLine), QsciLexerCPP::CommentLine);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::CommentDoc), QsciLexerCPP::CommentDoc);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::CommentLineDoc), QsciLexerCPP::CommentLineDoc);

    // 设置数字颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Number), QsciLexerCPP::Number);

    // 设置关键字颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Keyword), QsciLexerCPP::Keyword);         // 设置关键字高亮颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::KeywordSet2), QsciLexerCPP::KeywordSet2); // 第二组关键字

    // 设置字符串颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::DoubleQuotedString), QsciLexerCPP::DoubleQuotedString);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::SingleQuotedString), QsciLexerCPP::SingleQuotedString);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::UnclosedString), QsciLexerCPP::UnclosedString);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::VerbatimString), QsciLexerCPP::VerbatimString);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::TripleQuotedVerbatimString), QsciLexerCPP::TripleQuotedVerbatimString);

    // 设置预处理器指令颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::PreProcessor), QsciLexerCPP::PreProcessor);

    // 设置操作符颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Operator), QsciLexerCPP::Operator);

    // 设置标识符颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Identifier), QsciLexerCPP::Identifier);

    // 设置其他特殊样式颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::UserLiteral), QsciLexerCPP::UserLiteral);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::TaskMarker), QsciLexerCPP::TaskMarker);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::EscapeSequence), QsciLexerCPP::EscapeSequence);
    // CppLexer->setColor(MapLexerColor.value(QsciLexerCPP::EscapeChar), QsciLexerCPP::EscapeChar);

    // 设置不活动区域样式颜色
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveDefault), QsciLexerCPP::InactiveDefault);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveComment), QsciLexerCPP::InactiveComment);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveCommentLine), QsciLexerCPP::InactiveCommentLine);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveNumber), QsciLexerCPP::InactiveNumber);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveKeyword), QsciLexerCPP::InactiveKeyword);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveDoubleQuotedString), QsciLexerCPP::InactiveDoubleQuotedString);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveSingleQuotedString), QsciLexerCPP::InactiveSingleQuotedString);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactivePreProcessor), QsciLexerCPP::InactivePreProcessor);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveOperator), QsciLexerCPP::InactiveOperator);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveIdentifier), QsciLexerCPP::InactiveIdentifier);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveUnclosedString), QsciLexerCPP::InactiveUnclosedString);
    m_cpp_lexer->setColor(MapLexerColor.value(QsciLexerCPP::InactiveRawString), QsciLexerCPP::InactiveRawString);

    QsciAPIs *cppAPIs = new QsciAPIs(m_cpp_lexer);
    if (cppAPIs->load("./res/qsci/cpp.api"))
    {
        qDebug() << "cpp api is ok";
        cppAPIs->prepare();
    }

    /******************************set pythonLexer*********************************************/

    m_py_lexer->setDefaultColor(DefaultTextColor); /// 默认字体颜色
    m_py_lexer->setDefaultPaper(PaperColor);       /// 默认背景色
    m_py_lexer->setFont(QFont("Consolas", 12));    /// 默认字体

    m_py_lexer->setFoldCompact(true); // 折叠时隐藏空行

    //.................................................................

    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Default), QsciLexerPython::Default); // 设置关键字高亮颜色

    // rgb(78, 201, 176)
    m_py_lexer->setColor(QColor(78, 201, 176), QsciLexerPython::ClassName);
    m_py_lexer->setColor(QColor(78, 201, 176), QsciLexerPython::HighlightedIdentifier);

    // rgb(220, 220, 170)
    m_py_lexer->setColor(QColor(220, 220, 170), QsciLexerPython::FunctionMethodName);

    // 设置注释颜色
    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Comment), QsciLexerPython::Comment);

    // 设置数字颜色
    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Number), QsciLexerPython::Number);

    // 设置关键字颜色
    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Keyword), QsciLexerPython::Keyword);

    // 设置字符串颜色

    // rgb(206, 145, 120)

    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::DoubleQuotedString), QsciLexerPython::DoubleQuotedString);
    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::SingleQuotedString), QsciLexerPython::SingleQuotedString);
    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::UnclosedString), QsciLexerPython::UnclosedString);

    m_py_lexer->setColor(QColor(206, 145, 120), QsciLexerPython::TripleSingleQuotedString);
    m_py_lexer->setColor(QColor(206, 145, 120), QsciLexerPython::TripleDoubleQuotedString);

    m_py_lexer->setColor(QColor(206, 145, 120), QsciLexerPython::DoubleQuotedFString);
    m_py_lexer->setColor(QColor(206, 145, 120), QsciLexerPython::SingleQuotedFString);
    m_py_lexer->setColor(QColor(206, 145, 120), QsciLexerPython::TripleSingleQuotedFString);
    m_py_lexer->setColor(QColor(206, 145, 120), QsciLexerPython::TripleDoubleQuotedFString);

    // rgb(78, 201, 176)
    m_py_lexer->setColor(QColor(78, 201, 176), QsciLexerPython::Decorator);

    // 设置操作符颜色
    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Operator), QsciLexerPython::Operator);

    // 设置标识符颜色
    m_py_lexer->setColor(MapLexerColor.value(QsciLexerCPP::Identifier), QsciLexerPython::Identifier);

    // ///自动补全的实现
    QsciAPIs *pyAPIs = new QsciAPIs(m_py_lexer);
    if (pyAPIs->load("./res/qsci/Python-3.11.api"))
    {
        qDebug() << "py api is ok";
        pyAPIs->prepare();
        m_py_lexer->setAPIs(pyAPIs);
    }

    /// 添加语言api文件解析keyword实现自动补全

    /******************************set defaultLexer*********************************************/

    m_default_lexer->setDefaultColor(DefaultTextColor); /// 默认字体颜色
    m_default_lexer->setDefaultPaper(PaperColor);       /// 默认背景色
    m_default_lexer->setFont(QFont("Consolas", 14));    /// 默认字体

    // 设置默认 Lexer
    // 设置默认文本颜色为白色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Default);

    // 设置注释颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Comment);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::CommentLine);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::CommentDoc);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::CommentNested);

    // 设置数字颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Number);

    // 设置关键字颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Keyword);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::KeywordSecondary);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::KeywordDoc);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Typedefs);

    // 设置字符串颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::String);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::UnclosedString);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Character);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::BackquoteString);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::RawString);

    // 设置操作符颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Operator);

    // 设置标识符颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::Identifier);

    // 设置文档注释颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::CommentLineDoc);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::CommentDocKeyword);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::CommentDocKeywordError);

    // 设置其他关键字颜色
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::KeywordSet5);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::KeywordSet6);
    m_default_lexer->setColor(DefaultTextColor, QsciLexerD::KeywordSet7);
}

void CodeWidget::InitTextEdit()
{
    m_scintilla->setSelectionBackgroundColor(QColor(38, 79, 120)); // 深蓝背景
    m_scintilla->setSelectionForegroundColor(QColor("#FFFFFF"));   // 白色文字

    // m_scintilla->setLexer(m_cpp_lexer);
    m_scintilla->setTabWidth(4);                                    /// 设置Tab长度为4空格

    // 括号匹配
    m_scintilla->setBraceMatching(QsciScintilla::SloppyBraceMatch); /// 启用括号匹配
    m_scintilla->setMatchedBraceForegroundColor(MatchedBraceForegroundColor);
    m_scintilla->setMatchedBraceBackgroundColor(MatchedBraceBackgroundColor);  
    m_scintilla->setUnmatchedBraceForegroundColor(MatchedBraceForegroundColor);
    m_scintilla->setUnmatchedBraceBackgroundColor(UnmatchedBraceBackgroundColor); 


    /////////////////
    m_scintilla->setCallTipsStyle(QsciScintilla::CallTipsNoContext); /// 调用提示
    m_scintilla->setCallTipsPosition(QsciScintilla::CallTipsBelowText);
    m_scintilla->setCallTipsVisible(0);

    m_scintilla->setCallTipsBackgroundColor(QColor(30, 30, 30));
    m_scintilla->setCallTipsForegroundColor(QColor(210, 210, 210));
    m_scintilla->setCallTipsHighlightColor(QColor(255, 71, 87));

    /// 自动补全,tab enter 确认选择，esc取消AutoCompletion
    m_scintilla->setAutoCompletionSource(QsciScintilla::AcsAll);
    m_scintilla->setAutoCompletionThreshold(2);           // 当用户输入至少一个字符后触发自动完成
    m_scintilla->setAutoCompletionReplaceWord(true);      // 自动替换当前词
    m_scintilla->setAutoCompletionCaseSensitivity(false); // 不区分大小写
    m_scintilla->setAutoCompletionShowSingle(true);       // 只有一个匹配项也显示补全提示

    ////////////////

    /// 无需设置TextEdit的背景色，直接设置lexer即可，会被lexer覆盖
    m_scintilla->setPaper(PaperColor);

    m_scintilla->setIndentationGuides(true); /// 启动缩进提示
    m_scintilla->setAutoIndent(true);        /// 启动自动缩进

    m_scintilla->setCaretForegroundColor(CursorColor); ////设置光标color

    m_scintilla->setCaretLineVisible(true);                       // 启用当前行高亮
    m_scintilla->setCaretLineBackgroundColor(LineHighLightColor); /// 设置当前行高亮颜色

    // 行号边栏颜色
    m_scintilla->setMarginsBackgroundColor(MarginBackColor);
    m_scintilla->setMarginsForegroundColor(MarginForeColor);

    m_scintilla->setMarginLineNumbers(CodeWidget::LineNumMargin, true); /// 设置行号栏
    m_scintilla->setMarginType(CodeWidget::LineNumMargin, QsciScintilla::NumberMargin);
    m_scintilla->setMarginWidth(CodeWidget::LineNumMargin, "0000"); /// 设置行号栏宽度

    // 代码折叠设置
    m_scintilla->setFolding(QsciScintilla::PlainFoldStyle, FoldMargin);
    m_scintilla->setMarginWidth(CodeWidget::FoldMargin, 12);

    // 折叠标记定义
    m_scintilla->markerDefine(QsciScintilla::Minus, QsciScintilla::SC_MARKNUM_FOLDEROPEN);
    m_scintilla->markerDefine(QsciScintilla::Plus, QsciScintilla::SC_MARKNUM_FOLDER);
    m_scintilla->markerDefine(QsciScintilla::Minus, QsciScintilla::SC_MARKNUM_FOLDEROPENMID);
    m_scintilla->markerDefine(QsciScintilla::Plus, QsciScintilla::SC_MARKNUM_FOLDEREND);

    // 折叠标记颜色
    m_scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDEREND);
    m_scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDEREND);
    m_scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDEROPENMID);
    m_scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDEROPENMID);
    m_scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDERSUB);
    m_scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDERSUB);
    m_scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDER);
    m_scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDER);
    m_scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDEROPEN);
    m_scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDEROPEN);
    m_scintilla->setFoldMarginColors(QColor("#272727"), QColor("#272727"));

    int LineTag = 0;
    m_scintilla->markerDefine(QsciScintilla::VerticalLine, LineTag); /// 当前行标识
    m_scintilla->setMarkerBackgroundColor(CurrentLineTagColor, LineTag);

    /// 符号自动补全
    QStringList customSeparators = {" ", "(", ")", ",", ";", ".", ":", "[", "]", "{", "}", "->", "::"};
    m_scintilla->setAutoCompletionWordSeparators(customSeparators);

    QString StyleSheet = R"(


.QsciScintilla QScrollBar:horizontal {
    border-top:1px solid gray;
    background-color: rgb(45,45,45);
    height: 12px;
    margin: 0px 0px 0px 0px;
    padding-left:12px;
    padding-right:12px;
    border-radius: 7px;

}




/* 定义 QScintilla 内部垂直滚动条的样式 */
.QsciScintilla QScrollBar:vertical {
    border-left:1px solid gray;
    background-color: rgb(45,45,45);
    width: 12px;
    margin: 0px 0px 0px 0px;
    padding-top:12px;
    padding-bottom:12px;
    border-radius: 7px;
}


QScrollBar::handle:vertical:hover{
    background-color:rgb(30,30,30);
    border-left:1px solid gray;
}
QScrollBar::handle:horizontal:hover{
    background-color:rgb(30,30,30);
    border-left:1px solid gray;
}

QScrollBar::handle:horizontal{
  background-color:rgb(80,80,80);
  border-radius:2px;
  min-width:20px;
  margin:2px 1px 2px 1px;
}

QScrollBar::handle:vertical{
  background-color:rgb(80,80,80);
  border-radius:2px;
  min-width:20px;
  margin:1px 2px 1px 2px;
}

/* 移除滚动区域 */
.QsciScintilla QScrollBar::add-page:horizontal, .QsciScintilla QScrollBar::sub-page:horizontal {
    background: none; /* 设置背景为无 */
}


/* 移除滚动区域 */
.QsciScintilla QScrollBar::add-page:vertical, .QsciScintilla QScrollBar::sub-page:vertical {
    background: none; /* 设置背景为无 */
}

    )";

    // m_scintilla->setStyleSheet(StyleSheet);
}

void CodeWidget::ModifyLexerColor(int WordType, QColor &col)
{
    MapLexerColor.value(WordType) = col;
    InitLexer();
}

void CodeWidget::ResortDefault()
{
    MapLexerColor = createDefaultColorMap();
    InitLexer();
}

void CodeWidget::CloseLineTag(bool flag)
{
    this->LineTagIsOpen = flag;
    if (flag)
    {
        connect(m_scintilla, &QsciScintilla::cursorPositionChanged, this, &CodeWidget::highlightCurrentLine);
    }
    else
    {
        disconnect(m_scintilla, &QsciScintilla::cursorPositionChanged, this, &CodeWidget::highlightCurrentLine);
    }
}

void CodeWidget::setCurLexer(QString &type)
{
    qDebug() << "type:" << type;
    if (type == "py")
    {
        qDebug() << "change to pylexer";
        m_scintilla->setLexer(m_py_lexer);
    }
    else if (type == "cpp" || type == "c")
    {
        qDebug() << "change to cpplexer";
        m_scintilla->setLexer(m_cpp_lexer);
    }
    else
    {
        m_scintilla->setLexer(m_default_lexer);
    }

    /// 修改lexer后textEdit一些属性会被清空,故需要重新设置
    InitTextEdit();
    // m_scintilla->setMatchedBraceBackgroundColor(MatchedBraceBackgroundColor);
    // m_scintilla->setMatchedBraceForegroundColor(MatchedBraceForegroundColor);
    // m_scintilla->setMarkerBackgroundColor(CurrentLineTagColor, 0);
    // m_scintilla->setMarginsBackgroundColor(MarginBackColor);///设置margin边栏的背景color
    // m_scintilla->setMarginsForegroundColor(Qt::white);///行号字体颜色
}

void CodeWidget::highlightCurrentLine(int line, int index)
{
    Q_UNUSED(index);
    qDebug() << "curLine:" << line;
    // 清除所有标记
    for (int i = 0; i < m_scintilla->lines(); ++i)
    {
        m_scintilla->markerDelete(i, 0);
        m_scintilla->markerDelete(i, 1);
    }

    // 在当前行添加标记
    if (line >= 0 && line < m_scintilla->lines())
    {
        m_scintilla->markerAdd(line, 0);
    }
}

void CodeWidget::findNext(const QString &text, bool caseSensitive, bool wholeWords, bool forward)
{
    qDebug() << "finding " << forward;

    // 设置搜索参数
    bool found = m_scintilla->findFirst(text, false, caseSensitive, wholeWords, true, forward, -1, -1, true, false, false);

    /// 因为find后一项后，cursor位于匹配项的end，故再find前一项会匹配到当前项，则需利用findNext再往前推一项
    if (!forward)
        m_scintilla->findNext();

    qDebug() << "found1:" << found;
    if (!found)
    {
        // 如果没有找到，可以从文档开头重新开始搜索
        m_scintilla->setCursorPosition(0, 0);
        found = m_scintilla->findFirst(text, false, caseSensitive, wholeWords, true, true, -1, -1, true, false, false);
        qDebug() << "found2:" << found;
    }

    if (!found)
    {
        QMessageBox::information(this, "查找", "未找到指定文本");
    }
}

void CodeWidget::selectAllText(const QString &text)
{
    qDebug() << "selectAll " << text;
    m_scintilla->clearIndicatorRange(0, 0, m_scintilla->lines(), 0, highlightIndicator);
    if (text.isEmpty())
        return;

    int startPos = 0;
    int endPos = m_scintilla->length();

    if (m_scintilla->hasSelectedText() && m_scintilla->selectedText() != text)
    {
        startPos = m_scintilla->SendScintilla(QsciScintilla::SCI_GETSELECTIONSTART);
        endPos = m_scintilla->SendScintilla(QsciScintilla::SCI_GETSELECTIONEND);
    }

    // 清除现有选区
    m_scintilla->SendScintilla(QsciScintilla::SCI_CLEARSELECTIONS);

    // 设置多选区模式
    m_scintilla->SendScintilla(QsciScintilla::SCI_SETMULTIPLESELECTION, 1);
    m_scintilla->SendScintilla(QsciScintilla::SCI_SETADDITIONALSELECTIONTYPING, 1);

    // 设置选择样式，透明度和Color
    m_scintilla->SendScintilla(QsciScintilla::SCI_SETSELALPHA, 75); // 半透明效果
    // textEdit->SendScintilla(QsciScintilla::SCI_SETSELBACK, true, QColor(0, 0, 255).rgb());

    /// 设置选区
    m_scintilla->SendScintilla(QsciScintilla::SCI_SETSELECTION, matchPosVec[0].first, matchPosVec[0].second);
    for (int i = 1; i < matchPosVec.size(); i++)
    {
        /// 限定多选范围
        if (matchPosVec[i].first >= startPos && matchPosVec[i].second <= endPos)
        {
            m_scintilla->SendScintilla(QsciScintilla::SCI_ADDSELECTION, matchPosVec[i].first, matchPosVec[i].second);
        }
    }
}

void CodeWidget::highLightAll(const QString &text)
{
    // return;
    // 清除所有旧的高亮
    m_scintilla->clearIndicatorRange(0, 0, m_scintilla->lines(), 0, highlightIndicator);

    // 清空匹配cursor Start to End map
    matchPosVec.clear();

    if (text.isEmpty())
        return;

    // 设置指示器样式
    m_scintilla->SendScintilla(QsciScintilla::SCI_INDICSETSTYLE, highlightIndicator, QsciScintilla::INDIC_FULLBOX); // 可选择其他样式如 INDIC_PLAIN, INDIC_SQUIGGLE 等
    m_scintilla->SendScintilla(QsciScintilla::SCI_INDICSETFORE, highlightIndicator, QColor(255, 0, 0));             // 设置前景色（可选）

    int startPos = 0;
    int endPos = m_scintilla->length();
    bool found = true;
    int line1, index1, line2, index2;
    qDebug() << "start:" << startPos << " endPos:" << endPos;

    while (found)
    {
        /// 注意该处匹配需要wrap(回转到开头匹配),不然会从cursor匹配,导致前面的匹配项丢失
        found = m_scintilla->findFirst(text, false, false, false, true, true);
        if (found)
        {

            /// 获取选择的初始和结束位置
            const int matchStart = m_scintilla->SendScintilla(QsciScintilla::SCI_GETSELECTIONSTART);
            const int matchEnd = m_scintilla->SendScintilla(QsciScintilla::SCI_GETSELECTIONEND);

            /// 将位置转化为 Line 和 Index
            m_scintilla->lineIndexFromPosition(matchStart, &line1, &index1);
            m_scintilla->lineIndexFromPosition(matchEnd, &line2, &index2);

            qDebug() << "startPos:" << startPos << " matchStart:" << matchStart << " matchEnd: " << matchEnd << "line index::" << line1 << " "
                     << index1 << " " << line2 << " " << index2;
            /// 高亮匹配区域
            m_scintilla->fillIndicatorRange(line1, index1, line2, index2, highlightIndicator);

            /// 存储匹配项的起始位置
            matchPosVec.push_back(qMakePair(matchStart, matchEnd));

            /// 防越wrap死循环
            if (startPos > matchStart)
                break;

            // 更新起始位置到当前匹配结束位置
            startPos = matchEnd;
        }
    }
}

void CodeWidget::replaceText(const QString &origin, const QString &replaced)
{
    qDebug() << "replace text :" << replaced;
    if (m_scintilla->hasSelectedText())
    {
        m_scintilla->replaceSelectedText(replaced);
        m_scintilla->findNext(); // 自动查找下一个匹配项
    }
}

void CodeWidget::replaceAll(const QString &origin, const QString &replaced)
{
    bool found = true;
    while (m_scintilla->hasSelectedText() && found)
    {
        m_scintilla->replaceSelectedText(replaced);
        found = m_scintilla->findNext(); // 自动查找下一个匹配项
    }
}

QMap<int, QColor> CodeWidget::createDefaultColorMap()
{
    QMap<int, QColor> colorMap;

    // 默认文本
    // rgb(214, 207, 154)
    // rgb(156, 220, 254)

    colorMap.insert(QsciLexerCPP::Default, QColor(156, 220, 254));

    // 注释
    colorMap.insert(QsciLexerCPP::Comment, QColor(87, 166, 74));
    colorMap.insert(QsciLexerCPP::CommentLine, QColor(87, 166, 74));
    colorMap.insert(QsciLexerCPP::CommentDoc, QColor(87, 166, 74));
    colorMap.insert(QsciLexerCPP::CommentLineDoc, QColor(87, 166, 74));

    // 数字
    colorMap.insert(QsciLexerCPP::Number, QColor(181, 206, 168));

    // 关键字
    // rgb(86, 156, 214)

    // rgb(78, 201, 176)

    // rgb(197, 134, 192)
    colorMap.insert(QsciLexerCPP::Keyword, QColor(197, 134, 192));

    // rgb(216, 160, 223)
    colorMap.insert(QsciLexerCPP::KeywordSet2, QColor(216, 160, 223)); // 第二组关键字

    // 字符串
    colorMap.insert(QsciLexerCPP::DoubleQuotedString, QColor(214, 157, 133));
    colorMap.insert(QsciLexerCPP::SingleQuotedString, QColor(214, 157, 133));
    colorMap.insert(QsciLexerCPP::UnclosedString, QColor(138, 96, 44));
    colorMap.insert(QsciLexerCPP::VerbatimString, QColor(138, 96, 44)); // rgb(138, 96, 44)
    colorMap.insert(QsciLexerCPP::TripleQuotedVerbatimString, QColor(214, 157, 133));

    // 预处理器指令
    colorMap.insert(QsciLexerCPP::PreProcessor, QColor(155, 155, 155));

    // 操作符
    colorMap.insert(QsciLexerCPP::Operator, QColor(180, 180, 180));

    // 标识符
    // rgb(220, 220, 170)
    colorMap.insert(QsciLexerCPP::Identifier, QColor(156, 220, 254));

    // 其他特殊样式
    colorMap.insert(QsciLexerCPP::UserLiteral, QColor(78, 201, 176));
    colorMap.insert(QsciLexerCPP::TaskMarker, QColor(193, 44, 31));
    colorMap.insert(QsciLexerCPP::EscapeSequence, QColor(213, 235, 225));
    // colorMap.insert(QsciLexerCPP::EscapeChar, QColor());

    // 不活动区域的样式
    colorMap.insert(QsciLexerCPP::InactiveDefault, QColor(214, 207, 154));
    colorMap.insert(QsciLexerCPP::InactiveComment, QColor(87, 166, 74));
    colorMap.insert(QsciLexerCPP::InactiveCommentLine, QColor(87, 166, 74));
    colorMap.insert(QsciLexerCPP::InactiveNumber, QColor(181, 206, 168));
    colorMap.insert(QsciLexerCPP::InactiveKeyword, QColor(86, 156, 214));
    colorMap.insert(QsciLexerCPP::InactiveDoubleQuotedString, QColor(214, 157, 133));
    colorMap.insert(QsciLexerCPP::InactiveSingleQuotedString, QColor(214, 157, 133));
    colorMap.insert(QsciLexerCPP::InactivePreProcessor, QColor(155, 155, 155));
    colorMap.insert(QsciLexerCPP::InactiveOperator, QColor(180, 180, 180));
    colorMap.insert(QsciLexerCPP::InactiveIdentifier, QColor(218, 218, 218));
    colorMap.insert(QsciLexerCPP::InactiveUnclosedString, QColor(218, 218, 218));
    colorMap.insert(QsciLexerCPP::InactiveRawString, QColor(218, 218, 218));

    return colorMap;
}

bool CodeWidget::eventFilter(QObject *obj, QEvent *event)
{
    if (obj == m_scintilla && event->type() == QEvent::KeyPress)
    {
        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);

        // 获取当前光标位置
        int line, index;
        m_scintilla->getCursorPosition(&line, &index);

        // 获取输入的字符
        QString text = keyEvent->text();

        // 检查是否输入了左括号
        if (text == "(" || text == "{" || text == "[" || text == "\"" || text == "'")
        {
            // 阻止默认行为（插入左括号）防止插入右括号覆盖自动插入的左括号
            event->accept();

            // 插入对应的右括号
            QString closingBracket;
            if (text == "(")
                closingBracket = ")";
            else if (text == "{")
                closingBracket = "}";
            else if (text == "[")
                closingBracket = "]";
            else if (text == "\"")
                closingBracket = "\"";
            else if (text == "'")
                closingBracket = "'";

            m_scintilla->insertAt(text, line, index);               // 手动插入左括号
            m_scintilla->insertAt(closingBracket, line, index + 1); // 手动插入右括号

            // 将光标移动到括号内
            m_scintilla->setCursorPosition(line, index + 1);

            return true; // 事件已处理
        }
    }

    // 调用基类的事件过滤器
    return QWidget::eventFilter(obj, event);
}
