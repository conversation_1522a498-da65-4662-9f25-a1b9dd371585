cmake_minimum_required(VERSION 3.5)

project(TVS VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_INCLUDE_CURRENT_DIR ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core Widgets PrintSupport LinguistTools)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Widgets PrintSupport LinguistTools)

# 方式1
get_filename_component(PROJECT_RESOURCES_DIR "${CMAKE_CURRENT_SOURCE_DIR}" DIRECTORY)

# 方式2
# set(PROJECT_RESOURCES_DIR ${CMAKE_SOURCE_DIR})


message(STATUS "PROJECT_RESOURCES_DIR: ${PROJECT_RESOURCES_DIR}")


include_directories(${Qt6Widgets_INCLUDE_DIRS})
include_directories(${PROJECT_RESOURCES_DIR}/3rd/plot/include)
include_directories(${PROJECT_RESOURCES_DIR}/3rd/yaml/include)

set(TS_FILES ${CMAKE_CURRENT_SOURCE_DIR}/TVS_zh_CN.ts)

set(RESOURCE_FILES
    ${CMAKE_CURRENT_SOURCE_DIR}/res/res.qrc
    ${CMAKE_CURRENT_SOURCE_DIR}/theme/darkstyle.qrc
    ${CMAKE_CURRENT_SOURCE_DIR}/theme/dock.qrc
)

file(GLOB_RECURSE MAIN_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/*.c"
)

list(FILTER MAIN_SOURCES EXCLUDE REGEX ".*/(build|Build|BUILD|_build|cmake-build-.*)/.*")
list(FILTER MAIN_SOURCES EXCLUDE REGEX ".*3rd/.*")
list(FILTER MAIN_SOURCES EXCLUDE REGEX ".*moc_.*\\.cpp$")
list(FILTER MAIN_SOURCES EXCLUDE REGEX "demo_code/.*")
list(FILTER MAIN_SOURCES EXCLUDE REGEX "demo_plot/.*")

# list(FILTER SOURCES EXCLUDE REGEX ".*_test\\.cpp$")  # 排除测试文件
file(GLOB QCUSTOMPLOT_SOURCES
    "3rd/qcustomplot/*.cpp"
    "3rd/qcustomplot/*.h"
)

# 合并所有源文件
set(PROJECT_SOURCES
    ${MAIN_SOURCES}
    ${QCUSTOMPLOT_SOURCES}
    ${TS_FILES}
    ${RESOURCE_FILES}
)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(TVS
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )

    # Define target properties for Android with Qt 6 as:
    # set_property(TARGET TVS APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
    # ${CMAKE_CURRENT_SOURCE_DIR}/android)
    # For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
    qt_create_translation(QM_FILES ${CMAKE_SOURCE_DIR} ${TS_FILES})
else()
    if(ANDROID)
        add_library(TVS SHARED
            ${PROJECT_SOURCES}
        )

    # Define properties for Android with Qt 5 after find_package() calls as:
    # set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(TVS
            ${PROJECT_SOURCES}
        )
    endif()

    qt5_create_translation(QM_FILES ${CMAKE_SOURCE_DIR} ${TS_FILES})
endif()

target_compile_options(TVS PRIVATE -Wa,-mbig-obj)

target_include_directories(TVS PRIVATE
    ${PROJECT_RESOURCES_DIR}
    ${PROJECT_RESOURCES_DIR}/3rd/qcustomplot
    ${PROJECT_RESOURCES_DIR}/3rd/quazip/include
    ${PROJECT_RESOURCES_DIR}/3rd/uilib/include/AdvancedDocking
    ${PROJECT_RESOURCES_DIR}/3rd/uilib/include/SARibbon
    ${PROJECT_RESOURCES_DIR}/3rd/scintilla/include
)

target_link_libraries(TVS PRIVATE
    Qt6::Core
    Qt6::Widgets
    Qt6::PrintSupport
)

set(UILIB_DIR ${PROJECT_RESOURCES_DIR}/3rd/uilib/libs)
set(QUAZIP_LIB_DIR ${PROJECT_RESOURCES_DIR}/3rd/quazip/lib)
set(PLOT_LIB_DIR ${PROJECT_RESOURCES_DIR}/3rd/plot/lib)
set(YAML_LIB_DIR ${PROJECT_RESOURCES_DIR}/3rd/yaml/lib)
set(SCINTILLA_LIB_DIR ${PROJECT_RESOURCES_DIR}/3rd/scintilla/lib)

# Windows 平台特定配置
if(WIN32)
    # 根据构建类型设置库后缀
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(LIB_DEBUG_SUFFIX d)
    else()
        set(LIB_DEBUG_SUFFIX "")
    endif()

    # 查找 qtadvanceddocking 库
    find_library(QT_ADVANCED_DOCKING_LIB
        NAMES qtadvanceddocking${LIB_DEBUG_SUFFIX}
        PATHS ${UILIB_DIR}
        NO_DEFAULT_PATH
    )

    if(QT_ADVANCED_DOCKING_LIB)
        target_link_libraries(TVS PRIVATE ${QT_ADVANCED_DOCKING_LIB})
        message(STATUS "Found qtadvanceddocking library: ${QT_ADVANCED_DOCKING_LIB}")
    else()
        message(WARNING "Could NOT find qtadvanceddocking library")
    endif()

    # 查找 SARibbonBar 库
    find_library(SARIBBON_BAR_LIB
        NAMES SARibbonBar${LIB_DEBUG_SUFFIX}
        PATHS ${UILIB_DIR}
        NO_DEFAULT_PATH
    )

    if(SARIBBON_BAR_LIB)
        target_link_libraries(TVS PRIVATE ${SARIBBON_BAR_LIB})
        message(STATUS "Found SARibbonBar library: ${SARIBBON_BAR_LIB}")
    else()
        message(WARNING "Could NOT find SARibbonBar library")
    endif()

    # 查找 quazip 库
    find_library(QUAZIP_LIB
        NAMES libquazip1-qt6${LIB_DEBUG_SUFFIX}
        PATHS ${QUAZIP_LIB_DIR}
        NO_DEFAULT_PATH
    )

    if(QUAZIP_LIB)
        target_link_libraries(TVS PRIVATE ${QUAZIP_LIB})
        message(STATUS "Found quazip library: ${QUAZIP_LIB}")
    else()
        message(WARNING "Could NOT find quazip library")
    endif()

    # 查找 plot 库
    find_library(PLOT_LIB
        NAMES libplot${LIB_DEBUG_SUFFIX}
        PATHS ${PLOT_LIB_DIR}
        NO_DEFAULT_PATH
    )

    if(PLOT_LIB)
        target_link_libraries(TVS PRIVATE ${PLOT_LIB})
        message(STATUS "Found plot library: ${PLOT_LIB}")
    else()
        message(WARNING "Could NOT find plot library")
    endif()

    
    # 查找 YAML 库
    find_library(YAML_LIB
        NAMES libyaml-cpp${LIB_DEBUG_SUFFIX}
        PATHS ${YAML_LIB_DIR}
        NO_DEFAULT_PATH
    )

    if(YAML_LIB)
        target_link_libraries(TVS PRIVATE ${YAML_LIB})
        message(STATUS "Found YAML library: ${YAML_LIB}")
    else()
        message(WARNING "Could NOT find YAML library")
    endif()

    # 定义 YAML_CPP_STATIC_DEFINE 宏，使用静态库
    target_compile_definitions(TVS PRIVATE YAML_CPP_STATIC_DEFINE)
    
    # 查找 SCINTILLA 库
    find_library(SCINTILLA_LIB
        NAMES qscintilla2_qt6${LIB_DEBUG_SUFFIX}
        PATHS ${SCINTILLA_LIB_DIR}
        NO_DEFAULT_PATH
    )

    if(SCINTILLA_LIB)
        target_link_libraries(TVS PRIVATE ${SCINTILLA_LIB})
        message(STATUS "Found SCINTILLA library: ${SCINTILLA_LIB}")
    else()
        message(WARNING "Could NOT find SCINTILLA library")
    endif()

endif()

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if(${QT_VERSION} VERSION_LESS 6.1.0)
    set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.TVS)
endif()

set_target_properties(TVS PROPERTIES
    ${BUNDLE_ID_OPTION}
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS TVS
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(TVS)
endif()
