#include "qsplitter.h"
#include "uimonitorstatus.h"
#include "ui/monitor/uidatatable.h"
#include "ui/monitor/monitor_steam.h"
#include "ui/monitor/uiwave.h"
#include "plot_widget.h"
#include "core/Graph.h"
#include "utils/kissfft/kiss_fft.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFrame>
#include <QVector>
#include <QPair>
#include "utils/fft.h"
#include "widgets/flowlayout.h"


UiPointCard::UiPointCard(const QString &name, const QString &code, QWidget *parent)
    : QWidget{parent}
{
    // 设置卡片的基本样式
    setFixedSize(200, 120);
    setStyleSheet(R"(
        QWidget {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            margin: 2px;
        }
        QWidget:hover {
            background-color: #e9ecef;
            border-color: #adb5bd;
        }
    )");

    // 主布局 - 垂直布局
    auto mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(12, 10, 12, 10);
    mainLayout->setSpacing(8);

    // 测点名称
    m_name = new QLabel(QString("测点名称：%1").arg(name));
    m_name->setAlignment(Qt::AlignLeft);
    m_name->setStyleSheet(R"(
        QLabel {
            font-weight: bold;
            font-size: 12px;
            color: #495057;
            border: none;
            background: transparent;
        }
    )");
    mainLayout->addWidget(m_name);

    // 编码
    m_code = new QLabel(QString("编码：%1").arg(code));
    m_code->setAlignment(Qt::AlignLeft);
    m_code->setStyleSheet(R"(
        QLabel {
            font-size: 11px;
            color: #6c757d;
            border: none;
            background: transparent;
        }
    )");
    mainLayout->addWidget(m_code);

    // 分隔线
    auto separator = new QFrame();
    separator->setFrameShape(QFrame::HLine);
    separator->setFrameShadow(QFrame::Sunken);
    separator->setStyleSheet(R"(
        QFrame {
            color: #dee2e6;
            background-color: #dee2e6;
            border: none;
            max-height: 1px;
        }
    )");
    mainLayout->addWidget(separator);

    // 当前值
    m_value = new QLabel("当前值/(单位)：0.0");
    m_value->setAlignment(Qt::AlignLeft);
    m_value->setStyleSheet(R"(
        QLabel {
            font-weight: bold;
            font-size: 13px;
            color: #007bff;
            border: none;
            background: transparent;
        }
    )");
    mainLayout->addWidget(m_value);

    // 添加弹性空间，让内容向上对齐
    mainLayout->addStretch();
}

void UiPointCard::updateValue(double value)
{
    // 默认单位为空，可以通过 setUnit 方法设置
    updateValue(value, m_unit);
}

void UiPointCard::updateValue(double value, const QString &unit)
{
    m_value->setText(QString("当前值/(%1)：%2").arg(unit).arg(value, 0, 'f', 2));
}

void UiPointCard::setUnit(const QString &unit)
{
    m_unit = unit;
    // 简化实现：直接更新当前显示的值
    QString currentText = m_value->text();
    if (currentText.contains("：")) {
        QString valueStr = currentText.split("：").last();
        double currentValue = valueStr.toDouble();
        updateValue(currentValue);
    }
}


UiMonitorSteam::UiMonitorSteam(const QString &id, QWidget *parent)
    : QWidget{parent}
{
   
    FlowLayout *flowLayout = new FlowLayout(this, 5, 5, 5);
    flowLayout->setContentsMargins(0,0,0,0);

    for (int i = 0; i < 10; ++i)
    {
        auto name = "测点"+QString::number(i+1);
        auto code = "code"+QString::number(i+1);
        auto card = new UiPointCard(name, code);
        flowLayout->addWidget(card);
    }

}

bool UiMonitorSteam::isUiDataVisiable() const
{
    // return m_uiDataTable->isVisible();
    return true;
}

void UiMonitorSteam::setUiDataVisiable(bool visible)
{
    // m_uiDataTable->setVisible(visible);
}

void UiMonitorSteam::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    // m_splitter_top->setMinimumSize(100, event->size().height() / 5);
    // m_uiWavePulse->setMinimumSize(100, event->size().height() / 5);
    // m_uiDataTable->setMinimumSize(100, event->size().height()/5);
}



void UiMonitorSteam::timeToAddData()
{
  

}
