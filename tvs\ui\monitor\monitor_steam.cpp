#include "qsplitter.h"
#include "uimonitorstatus.h"
#include "ui/monitor/uidatatable.h"
#include "ui/monitor/monitor_steam.h"
#include "ui/monitor/uiwave.h"
#include "plot_widget.h"
#include "core/Graph.h"
#include "utils/kissfft/kiss_fft.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QSplitter>
#include <QFrame>
#include <QPainter>
#include <QPaintEvent>
#include <QEnterEvent>
#include <QRandomGenerator>
#include <QVector>
#include <QPair>
#include "utils/fft.h"
#include "widgets/flowlayout.h"


#include <QGraphicsDropShadowEffect>
#include <QLocale>

InfoCard::InfoCard(const QString &name, const QString &code, QWidget *parent)
    : QFrame(parent), m_unit("")
{
    setupUI();
    m_nameLabel->setText(name);
    m_codeLabel->setText("编码: " + code);
}

void InfoCard::setupUI()
{
    // 基础设置
    this->setMinimumSize(200, 120);
    this->setMaximumWidth(300);
    this->setFrameShape(QFrame::StyledPanel);
    
    // 深色主题QSS样式 - 适配#2B2B2B背景
    this->setStyleSheet(R"(
        InfoCard {
            background-color: #363636;
            border-radius: 8px;
            border: 1px solid #444;
        }
        InfoCard:hover {
            background-color: #3E3E3E;
            border: 1px solid #555;
        }
    )");

    // 阴影效果调整
    auto *shadowEffect = new QGraphicsDropShadowEffect(this);
    shadowEffect->setBlurRadius(18);
    shadowEffect->setXOffset(0);
    shadowEffect->setYOffset(4);
    shadowEffect->setColor(QColor(10, 10, 10, 120));
    this->setGraphicsEffect(shadowEffect);

    // 控件样式 - 适配深色背景
    m_nameLabel = new QLabel(this);
    m_nameLabel->setAlignment(Qt::AlignCenter);
    m_nameLabel->setStyleSheet(R"(
        font-weight: bold; 
        font-size: 16px; 
        color: #E1E1E1;
    )");

    m_codeLabel = new QLabel(this);
    m_codeLabel->setAlignment(Qt::AlignCenter);
    m_codeLabel->setStyleSheet(R"(
        font-size: 12px; 
        color: #A0A0A0;
    )");

    // 分隔线 - 更柔和的颜色
    QFrame *line = new QFrame(this);
    line->setFrameShape(QFrame::HLine);
    line->setFrameShadow(QFrame::Sunken);
    line->setStyleSheet("color: #444;");

    // 数值显示 - 使用更醒目的青蓝色
    m_valueLabel = new QLabel(this);
    m_valueLabel->setAlignment(Qt::AlignCenter);
    m_valueLabel->setStyleSheet(R"(
        font-weight: bold; 
        font-size: 28px; 
        color: #4FC1E9;
    )");

    m_unitLabel = new QLabel(this);
    m_unitLabel->setAlignment(Qt::AlignCenter);
    m_unitLabel->setStyleSheet(R"(
        font-size: 14px; 
        color: #A0A0A0;
    )");

    // 布局保持不变
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(8);
    mainLayout->setContentsMargins(12, 12, 12, 12);

    mainLayout->addWidget(m_nameLabel);
    mainLayout->addWidget(m_codeLabel);
    mainLayout->addWidget(line);
    
    QHBoxLayout *valueLayout = new QHBoxLayout();
    valueLayout->addStretch();
    valueLayout->addWidget(m_valueLabel);
    valueLayout->addWidget(m_unitLabel);
    valueLayout->addStretch();
    
    mainLayout->addLayout(valueLayout);
    mainLayout->addStretch();
}

void InfoCard::updateValue(double value)
{
    m_valueLabel->setText(formatValue(value));
    if (!m_unit.isEmpty()) {
        m_unitLabel->setText("(" + m_unit + ")");
    }
}

void InfoCard::setUnit(const QString &unit)
{
    m_unit = unit;
    m_unitLabel->setText("(" + m_unit + ")");
}

QString InfoCard::formatValue(double value) const
{
    QLocale locale;
    if (value == floor(value)) {
        return locale.toString(value, 'f', 0);
    }
    return locale.toString(value, 'f', 2);
}


UiMonitorSteam::UiMonitorSteam(const QString &id, QWidget *parent)
    : QWidget{parent}
{
    // 主布局 - 垂直布局
    auto mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(10, 10, 10, 10);
    mainLayout->setSpacing(0);

    // 创建垂直分隔器
    auto splitter = new QSplitter(Qt::Vertical, this);
    splitter->setContentsMargins(0, 0, 0, 0);
    mainLayout->addWidget(splitter);

    // 上半部分：蒸汽压力
    auto pressureWidget = createSectionWidget("蒸汽压力", "kPa", 5);
    splitter->addWidget(pressureWidget);

    // 下半部分：蒸汽温度
    auto temperatureWidget = createSectionWidget("蒸汽温度", "°C", 5);
    splitter->addWidget(temperatureWidget);

    // 设置分隔器比例 (1:1)
    splitter->setSizes({1, 1});

    // 不允许折叠
    splitter->setCollapsible(0, false);
    splitter->setCollapsible(1, false);

}

QWidget* UiMonitorSteam::createSectionWidget(const QString& title, const QString& unit, int cardCount)
{
    // 创建主容器
    auto sectionWidget = new QWidget();
    auto sectionLayout = new QHBoxLayout(sectionWidget);
    sectionLayout->setContentsMargins(10, 10, 10, 10);
    sectionLayout->setSpacing(15);

    // 左侧标签
    auto titleLabel = new QLabel(title);
    titleLabel->setAlignment(Qt::AlignCenter);
    titleLabel->setFixedWidth(120);
    titleLabel->setStyleSheet(R"(
        QLabel {
            font-size: 16px;
            font-weight: bold;
            color: #2c3e50;
            background-color: #575757;
            border: 1px solid #bdc3c7;
            border-radius: 6px;
            padding: 10px;
        }
    )");
    sectionLayout->addWidget(titleLabel);

    // 右侧卡片容器
    auto cardContainer = new QWidget();
    auto flowLayout = new FlowLayout(cardContainer, 10, 10, 10);
    flowLayout->setContentsMargins(0, 0, 0, 0);

    // 生成卡片
    for (int i = 0; i < cardCount; ++i)
    {
        auto name = QString("%1测点%2").arg(title).arg(i + 1);
        auto code = QString("%1_%2").arg(title == "蒸汽压力" ? "P" : "T").arg(i + 1, 2, 10, QChar('0'));
        auto card = new InfoCard(name, code);
        card->setUnit(unit);

        // 生成合理范围的随机值
        double randomValue;
        if (unit == "kPa") {
            // 压力：100-500 kPa
            randomValue = 100.0 + QRandomGenerator::global()->generateDouble() * 400.0;
        } else {
            // 温度：80-200 °C
            randomValue = 80.0 + QRandomGenerator::global()->generateDouble() * 120.0;
        }
        card->updateValue(randomValue);

        flowLayout->addWidget(card);
    }

    sectionLayout->addWidget(cardContainer, 1); // 让卡片容器占据剩余空间

    return sectionWidget;
}

bool UiMonitorSteam::isUiDataVisiable() const
{
    // return m_uiDataTable->isVisible();
    return true;
}

void UiMonitorSteam::setUiDataVisiable(bool visible)
{
    // m_uiDataTable->setVisible(visible);
}

void UiMonitorSteam::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    // m_splitter_top->setMinimumSize(100, event->size().height() / 5);
    // m_uiWavePulse->setMinimumSize(100, event->size().height() / 5);
    // m_uiDataTable->setMinimumSize(100, event->size().height()/5);
}



void UiMonitorSteam::timeToAddData()
{
  

}
