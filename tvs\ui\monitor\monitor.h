#ifndef UIMONITOR_H
#define UIMONITOR_H

/******************************************************************************
  File Name     : uidata.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备查看主视图
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
class UiWave;
class UiDataTable;
class UiMonitorStatus;
class QLabel;

class UiMonitor : public QWidget
{
    Q_OBJECT
public:
    explicit UiMonitor(const QString& id, QWidget *parent = nullptr);

    bool isUiDataVisiable() const;
    void setUiDataVisiable(bool visible);

protected:
    QString m_id;
    QSplitter * m_splitter;
    QSplitter * m_splitter_top;
    UiWave * m_uiWaveAngularSpeed;
    UiWave * m_uiWaveSpectrum;
    UiWave * m_uiWavePulse;
    UiMonitorStatus * m_uiMonitorStatus;
    QLabel* m_info;

signals:

    // QWidget interface
protected:
    virtual void resizeEvent(QResizeEvent *event) override;
};

#endif // UIMONITOR_H
