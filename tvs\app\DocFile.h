#ifndef DocFile_H
#define DocFile_H

/******************************************************************************
  File Name     : DocFile.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 实现文档序列化保存功能
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/


#include <QHash>
#include <QMap>
#include <QObject>
#include <QSharedPointer>
#include "utils/orderedmap.h"
#include "utils/cppex.h"

/**
 * @brief The EquipmentData class
 * 设备信息
 */
class EquipmentData: public QObject
{
    Q_OBJECT
public:
    EquipmentData() {}
    EquipmentData(const QString &id, int type, const QString &name, const QString &ip, const QString &port);

public:
    QString m_id;
    int m_type;
    QString m_name;
    QString m_ip;
    QString m_port;

    friend QDataStream & operator<<(QDataStream &stream, const EquipmentData & obj);
    friend QDataStream & operator>>(QDataStream &stream,  EquipmentData &obj);
};

using EquipmentDataPtr = QSharedPointer<EquipmentData>;

inline QDataStream & operator<<(QDataStream &stream, const EquipmentData & obj)
{
    int rightcode = 1;
    stream << rightcode;
    stream << obj.m_id;
    stream << obj.m_type;
    stream << obj.m_name;
    stream << obj.m_ip;
    stream << obj.m_port;


    return stream;
}

inline QDataStream & operator>>(QDataStream &stream, EquipmentData & obj)
{
    int rightcode = 0;
    stream >> rightcode;
    stream >> obj.m_id;
    stream >> obj.m_type;
    stream >> obj.m_name;
    stream >> obj.m_ip;
    stream >> obj.m_port;

    return stream;
}

/**
 * @brief The TestData class
 * 测试信息
 */
class TestData: public QObject
{
    Q_OBJECT
public:
    TestData() {}
    TestData(const QString &id, int type, const QString &name);

public:
    QString m_id;
    int m_type;
    QString m_name;

    friend QDataStream & operator<<(QDataStream &stream, const TestData & obj);
    friend QDataStream & operator>>(QDataStream &stream,  TestData &obj);
};

using TestDataPtr = QSharedPointer<TestData>;

inline QDataStream & operator<<(QDataStream &stream, const TestData & obj)
{
    int rightcode = 1;
    stream << rightcode;
    stream << obj.m_id;
    stream << obj.m_type;
    stream << obj.m_name;

    return stream;
}

inline QDataStream & operator>>(QDataStream &stream, TestData & obj)
{
    int rightcode = 0;
    stream >> rightcode;
    stream >> obj.m_id;
    stream >> obj.m_type;
    stream >> obj.m_name;

    return stream;
}

class DocFile : public QObject
{
    Q_OBJECT
public:
    explicit DocFile(QObject *parent = nullptr);

    bool saveDoc(const QString &file="");
    bool loadDoc(const QString &file);

    bool hasEquipmentIp(const QString &ip) const;
    EquipmentDataPtr addEquipment(const QString &id, int type, const QString &name, const QString &ip, const QString &port);
    bool removeEquipment(const QString &id);

    TestDataPtr addTest(const QString &id, int type, const QString &name);
    bool removeTest(const QString &id);
    bool hasTestName(const QString &name) const;


public:
    OrderedMap<QString, EquipmentDataPtr> m_equipments;
    OrderedMap<QString, TestDataPtr> m_tests;

    QByteArray m_win_state;
    QByteArray m_dock_state;
    QByteArray m_cen_dock_state;

    QString m_file;

signals:
};



#endif // DocFile_H
