#ifndef UIPJINFO_H
#define UIPJINFO_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 工程信息页
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
class QLabel;
class QTextEdit;


class UiPjInfo: public QWidget
{
    Q_OBJECT


public:
    explicit UiPjInfo(QWidget *parent = nullptr);

public:
    QLabel * m_name;
    QLabel * m_size;
    QLabel * m_auth;
    QLabel * m_create_time;
    QLabel * m_last_editor;
    QLabel * m_last_edit_time;
    QTextEdit * m_remarks;


signals:
};




#endif // UIPJINFO_H
