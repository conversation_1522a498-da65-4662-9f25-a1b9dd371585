#include "propdata.h"
#include "qjsonobject.h"
#include "widgets/dragtreewidget.h"
#include "widgets/stdwidget.h"
#include <QFile>
#include <QJsonDocument>
#include <QVBoxLayout>
#include <app/settings.h>
#include <QJsonArray>

PropData::PropData(QWidget *parent)
    : QWidget{parent}
{
    // 创建垂直布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    // QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_pj_open.margins");
    // if(margins.size() == 4)
    // {
    //     mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    // }

    // 创建标题
    // auto *titleLabel = new LabelH3("数据源");
    // titleLabel->setObjectName("DataSource");
    // mainLayout->addWidget(titleLabel);

    //设置树形头部控件
    DragTreeWidget * tree = new DragTreeWidget();
    tree->setHeaderLabel("数据源");
    mainLayout->addWidget(tree);
    tree->setStyleSheet("default;"); // 清除已设置的样式表

    // 读取 JSON 文件
    QFile file("./config/datatree.json");
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        qDebug() << "Failed to open JSON file.";
        return;
    }

    // 解析 JSON 数据
    QByteArray jsonData = file.readAll();
    QJsonParseError parseError;
    QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonData, &parseError);
    if (jsonDoc.isNull() || parseError.error != QJsonParseError::NoError)
    {
        qDebug() << "Failed to parse JSON:" << parseError.errorString();
        return;
    }

    // 从根节点开始递归创建树形结构，并设置节点文字颜色
    initTreeByJson(tree, nullptr, jsonDoc.array());

    // 调整列宽以适应内容
    tree->resizeColumnToContents(0);

}

void PropData::initTreeByJson(QTreeWidget *tree, QTreeWidgetItem *parentItem, const QJsonValue &value)
{
    if (value.isArray())
    {
        const QJsonArray arr = value.toArray();
        for (const QJsonValue &val : arr)
        {
            if (val.isObject())
            {
                const QJsonObject obj = val.toObject();
                QString title = obj.value("title").toString();
                QString colorStr = obj.value("color").toString();
                QColor color(colorStr);

                QTreeWidgetItem *childItem = nullptr;
                if(parentItem)
                {
                    childItem = new QTreeWidgetItem(parentItem);
                }
                else
                {
                    childItem = new QTreeWidgetItem(tree);
                }
                childItem->setText(0, title);
                childItem->setForeground(0, color); // 设置节点文字颜色

                if (obj.contains("children"))
                {
                    initTreeByJson(tree, childItem, obj.value("children"));
                }
            }
        }
    }
}
