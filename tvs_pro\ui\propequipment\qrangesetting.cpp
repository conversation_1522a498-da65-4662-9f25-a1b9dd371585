#include "qrangesetting.h"
#include "widgets/switchbutton.h"

#include <app/settings.h>

#include <QComboBox>
#include <QGridLayout>
#include <QLabel>
#include <QLineEdit>

QRangeSetting::QRangeSetting(QWidget *parent)
    : QWidget{parent}
{
    //量程设置界面布局
    auto gridLayout = new QGridLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp_content.margins");
    if(margins.size() == 4)
    {
        gridLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.hspacing");

    gridLayout->setVerticalSpacing(vspacing);
    gridLayout->setHorizontalSpacing(hspacing);

    auto lbSettingCell = new QLabel("配置单元");                 //配置单元
    m_combSettingCell = new QComboBox;
    auto lbVoltageAutoRange = new QLabel("电压自动量程");         //电压自动量程
    m_switchVoltageAutoRange = new SwitchButton;
    auto lbVoltageRange = new QLabel("电压量程");                //电压量程
    m_combVoltageRange = new QComboBox;
    auto lbElectricAutoRange = new QLabel("电流自动量程");        //电流自动量程
    m_switchElectricAutoRange = new SwitchButton;
    auto lbElectricRange = new QLabel("电流量程");               //电流量程
    m_combElectricRange = new QComboBox;
    auto lbSensorRange = new QLabel("传感器量程");               //传感器量程
    m_combSensorRange = new QComboBox;
    auto lbSensorWithout = new QLabel("外部传感器");             //外部传感器
    m_switchSensorOut = new SwitchButton;
    auto lbSensorRatio = new QLabel("传感器比率");               //传感器比率
    m_sensorRatio = new QLineEdit;
    auto lbVoltageSensorPT = new QLabel("电压传感器PT");         //电压传感器PT
    m_voltageSensorPT = new QLineEdit;
    auto lbElectricSensorPT = new QLabel("电流传感器PT");        //电流传感器PT
    m_electricSensorPT = new QLineEdit;

    gridLayout->addWidget(lbSettingCell, 0, 0);
    gridLayout->addWidget(m_combSettingCell, 0, 1);

    gridLayout->addWidget(lbVoltageAutoRange, 1, 0);
    gridLayout->addWidget(m_switchVoltageAutoRange, 1, 1);

    gridLayout->addWidget(lbVoltageRange, 2, 0);
    gridLayout->addWidget(m_combVoltageRange, 2, 1);

    gridLayout->addWidget(lbElectricAutoRange, 3, 0);
    gridLayout->addWidget(m_switchElectricAutoRange, 3, 1);

    gridLayout->addWidget(lbElectricRange, 4, 0);
    gridLayout->addWidget(m_combElectricRange, 4, 1);

    gridLayout->addWidget(lbSensorRange, 5, 0);
    gridLayout->addWidget(m_combSensorRange, 5, 1);

    gridLayout->addWidget(lbSensorWithout, 6, 0);
    gridLayout->addWidget(m_switchSensorOut, 6, 1);

    gridLayout->addWidget(lbSensorRatio, 7, 0);
    gridLayout->addWidget(m_sensorRatio, 7, 1);

    gridLayout->addWidget(lbVoltageSensorPT, 8, 0);
    gridLayout->addWidget(m_voltageSensorPT, 8, 1);

    gridLayout->addWidget(lbElectricSensorPT, 9, 0);
    gridLayout->addWidget(m_electricSensorPT, 9, 1);


    gridLayout->setColumnStretch(3, 1);
}
