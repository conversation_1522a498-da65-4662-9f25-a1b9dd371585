#ifndef SINGLETON_H
#define SINGLETON_H

/******************************************************************************
  File Name     : singleton.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 单例模板
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <type_traits>


template <typename T, typename D = T>
class Singleton
{
    friend D;
    static_assert(std::is_base_of_v<T, D>, "T should be a base type for D");

public:
    static T& instance();

private:
    Singleton() = default;
    ~Singleton() = default;
    Singleton( const Singleton& ) = delete;
    Singleton& operator=( const Singleton& ) = delete;
};

template <typename T, typename D>
T& Singleton<T, D>::instance()
{
    static D inst;
    return inst;
}

// // Usage:
// class MyClass : public Singleton<MyClass>
// {
// public:
//     void foo(){}
// };

// // Access:
// MyClass::instance().foo();

#endif // SINGLETON_H
