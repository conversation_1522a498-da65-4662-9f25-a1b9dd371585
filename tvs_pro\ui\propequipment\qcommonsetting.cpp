/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-05-12 20:37:40
 * @LastEditors: zhai
 * @LastEditTime: 2025-05-25 15:44:46
 */
#include "qcommonsetting.h"

#include <app/settings.h>

QCommonSetting::QCommonSetting(QWidget *parent)
    : QWidget{parent}
{
    //常规设置界面布局
    auto gridLayout = new QGridLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp_content.margins");
    if(margins.size() == 4)
    {
        gridLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.hspacing");

    gridLayout->setVerticalSpacing(vspacing);
    gridLayout->setHorizontalSpacing(hspacing);

    auto lbSettingCellCom = new QLabel("配置单元");            //配置单元
    m_combSettingCellCom = new QComboBox;
    auto lbSynchSorce = new QLabel("同步源");                  //同步源
    m_combSynchSorce = new QComboBox;
    auto lbFreqfilter = new QLabel("频率滤波器");               //频率滤波器
    auto lbFreqfilterVoltage = new QLabel("电压");             //频率滤波器电压
    m_combFreqfilterVoltage = new QComboBox;
    auto lbFreqfilterElectric = new QLabel("电流");            //频率滤波器电流
    m_combFreqfilterElectric = new QComboBox;
    auto lbLinefilter = new QLabel("线路滤波器");               //线路滤波器
    auto lbLinefilterVoltage = new QLabel("电压");             //线路滤波器电压
    m_combLinefilterVoltage = new QComboBox;
    auto lbLinefilterElectric = new QLabel("电流");            //线路滤波器电流
    m_combLinefilterElectric = new QComboBox;

    gridLayout->addWidget(lbSettingCellCom, 0, 0);
    gridLayout->addWidget(m_combSettingCellCom, 0, 1);

    gridLayout->addWidget(lbSynchSorce, 1, 0);
    gridLayout->addWidget(m_combSynchSorce, 1, 1);

    gridLayout->addWidget(lbFreqfilter, 2, 0);

    gridLayout->addWidget(lbFreqfilterVoltage, 3, 0);
    gridLayout->addWidget(m_combFreqfilterVoltage, 3, 1);

    gridLayout->addWidget(lbFreqfilterElectric, 4, 0);
    gridLayout->addWidget(m_combFreqfilterElectric, 4, 1);

    gridLayout->addWidget(lbLinefilter, 5, 0);

    gridLayout->addWidget(lbLinefilterVoltage, 6, 0);
    gridLayout->addWidget(m_combLinefilterVoltage, 6, 1);

    gridLayout->addWidget(lbLinefilterElectric, 7, 0);
    gridLayout->addWidget(m_combLinefilterElectric, 7, 1);


    gridLayout->setColumnStretch(3, 1);
}
