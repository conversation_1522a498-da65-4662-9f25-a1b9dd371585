/*
 * @Descripttion:
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-05-08 19:40:58
 * @LastEditors: zhai
 * @LastEditTime: 2025-05-15 21:25:24
 */
#ifndef PLOT_WINDOW_H
#define PLOT_WINDOW_H

#include <QMainWindow>

#include "qcpl/qcpl_plot.h"
#include "core/DataSources.h"

class Graph;

class PLOT_EXPORT PlotItem
{
public:
    ~PlotItem();

    std::unique_ptr<Graph> graph;
    // 不需要管理QCP的指针内存
    QCPGraph* line;
};

class PLOT_EXPORT DaqDataSource : public DataSource
{
public:
    ConfigResult configure() override;
    GraphResult read() override;
    QString makeTitle() const override;
    QString displayStr() const override;

};


class PLOT_EXPORT PlotWidget : public QWidget
{
    Q_OBJECT

public:
    PlotWidget(QWidget *parent = nullptr);
    ~PlotWidget();

public:
    QCPL::Plot* getPlot() const
    {
        return _plot.get();
    }

    Graph* selectedGraph(bool warn = true) const;
    QVector<Graph *> selectedGraphs(bool warn = true) const;
    QCPGraph* selectedGraphLine(bool warn = true) const;

protected:
    void resizeEvent(QResizeEvent *event) override;

signals:
    void graphSelected(Graph* g);

private slots:
    void graphLineSelected(bool selected);

protected:
    std::unique_ptr<QCPL::Plot> _plot;
    std::list<std::unique_ptr<PlotItem>> _items;
    QString recentFormatFile;
    QCPColorScale *_colorScale = nullptr;
    int _colorMapsCount = 0;

    bool autolitmAfterAxesChanged = true;
    bool autolimitAfterGraphCreated = false;
    bool selectNewGraph = false;

    void addRandomSampleLine();
    void addRandomSampleColormap();
    void savePlotFormat();
    void loadPlotFormat();
    void pasteLegendFormat();
    void pasteTitleFormat();
    void pasteAxisFormat(QCPAxis *axis);
    void createColorScale();

    void addGraph(Graph* g);
    void markModified(const QString& reason);

    void selectGraphLine(QCPGraph* line, bool replot = true);

    PlotItem* itemForLine(QCPGraph* line) const;
    PlotItem* itemForGraph(Graph* graph) const;
    bool updateGraph(Graph* graph);
    bool addGraphData(Graph* graph, const QVector<double> &keys, const QVector<double> &values, bool alreadySorted=false, int maxDataPoints=0);

    void renameGraph();
    void formatGraph();
    void changeGraphAxes();
    void copyGraphFormat();
    void pasteGraphFormat();
    void deleteGraph();
    void deleteGraphs(const QVector<Graph*>& graphs);

};

#endif // PLOT_WINDOW_H
