/*
 * @Descripttion: 
 * @version: 0.x
 * @Author: zhai
 * @Date: 2025-05-12 20:37:40
 * @LastEditors: zhai
 * @LastEditTime: 2025-05-25 22:09:20
 */
#include "qadvancedsetting.h"

#include <app/settings.h>

QAdvancedSetting::QAdvancedSetting(QWidget *parent)
    : QWidget{parent}
{
    //高级设置界面布局
    auto gridLayout = new QGridLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp_content.margins");
    if(margins.size() == 4)
    {
        gridLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.hspacing");

    gridLayout->setVerticalSpacing(vspacing);
    gridLayout->setHorizontalSpacing(hspacing);

    m_btnComHarmonicWave = new QPushButton("常规谐波");           //常规谐波
    // m_btnComHarmonicWave->setFlat(true);

    m_btnPointsSetting = new QPushButton("积分设置");             //积分设置
    // m_btnPointsSetting->setFlat(true);

    m_btnIECHarmonicWave = new QPushButton("IEC&谐波");           //IEC&谐波
    // m_btnIECHarmonicWave->setFlat(true);

    m_btnElectricMachineSetting = new QPushButton("电机设置");    //电机设置
    // m_btnElectricMachineSetting->setFlat(true);


    gridLayout->addWidget(m_btnComHarmonicWave, 0, 0);
    gridLayout->addWidget(m_btnPointsSetting, 0, 1);

    gridLayout->addWidget(m_btnIECHarmonicWave, 1, 0);
    gridLayout->addWidget(m_btnElectricMachineSetting, 1, 1);

    gridLayout->setColumnStretch(3, 1);
}
