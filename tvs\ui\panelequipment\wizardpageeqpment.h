#ifndef WIZARDPAGEUIEQUIPMENT_H
#define WI<PERSON><PERSON><PERSON>AG<PERSON>UIEQUIPMENT_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        : 
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备新建向导步骤页
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QPushButton>
#include <QWizardPage>

class QLineEdit;

// 流式布局容器
class EquipmentFlow : public QWidget
{
    Q_OBJECT
public:
    using QWidget::QWidget;
};

class EquipmentItem : public QPushButton
{
    Q_OBJECT
public:
    EquipmentItem(const QString &title, const QString &info, const QString &picFile, QWidget *parent = nullptr);
};

class WizardPageEquipment : public QWizardPage
{
    Q_OBJECT
public:
    WizardPageEquipment();

    Q_PROPERTY(int equipment READ getEquipment NOTIFY equipmentChanged)

    int getEquipment();

signals:
    void equipmentChanged(int);

protected:
    QButtonGroup *m_buttonGroup;
};

class WizardPageEquipmentConnect : public QWizardPage
{
    Q_OBJECT
public:
    WizardPageEquipmentConnect();
    bool validatePage() override;
public:
    QLineEdit * m_name;
    QLineEdit * m_type;
    QLineEdit * m_communicate;
    QLineEdit * m_port;
    QLineEdit * m_ip;

    // QWizardPage interface
public:
    virtual void initializePage() override;
};


#endif // WIZARDPAGEUIEQUIPMENT_H
