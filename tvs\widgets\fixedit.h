#ifndef FIXEDIT_H
#define FIXEDIT_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 带前缀或后缀的LineEdit，支持图标或文字作为前后缀
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QLineEdit>
class QLabel;

class FixEdit : public QLineEdit
{
    Q_OBJECT
public:
    explicit FixEdit(const QString &text, bool prefix, QWidget *parent = nullptr);
    explicit FixEdit(const QIcon &icon, bool prefix, QWidget *parent = nullptr);

private:
    void addLabel(bool prefix);

private:
    QLabel *m_label;
};

#endif // FIXEDIT_H
