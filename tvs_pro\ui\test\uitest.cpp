#include "uitest.h"
#include "uitestparam.h"
#include "uitestwave.h"

#include <QTabWidget>
#include <QVBoxLayout>

UiTest::UiTest(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0,0,0,0);

    QTabWidget *tabWidget = new QTabWidget();
    mainLayout->addWidget(tabWidget);

    tabWidget->setAttribute(Qt::WA_StyledBackground);

    // 将标签位置设置为左侧
    tabWidget->setTabPosition(QTabWidget::West);

    // 创建标签页1
    auto *ui_wave = new UiTestWave();
    tabWidget->addTab(ui_wave, "设置测试参数");

    // 创建标签页2
    auto *ui_param = new UiTestParam();
    tabWidget->addTab(ui_param, "执行测试&分析数据");
}
