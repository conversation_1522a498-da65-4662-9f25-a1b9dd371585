#ifndef TOOLBOX_H
#define TOOLBOX_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 支持多个同时展开的工具栏
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
class QVBoxLayout;

class ToolBox : public QWidget
{
    Q_OBJECT
public:
    explicit ToolBox(QWidget *parent = nullptr);
    void addItem(QWidget *widget, const QString &title);

private:
    QVBoxLayout *m_layout;

signals:
};

#endif // TOOLBOX_H
