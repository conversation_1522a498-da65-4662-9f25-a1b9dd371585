#include "qequipmentsetting.h"
#include "widgets/switchbutton.h"

#include <app/settings.h>

#include <QComboBox>
#include <QGridLayout>
#include <QLabel>

QEquipmentSetting::QEquipmentSetting(QWidget *parent)
    : QWidget{parent}
{
    auto gridLayout = new QGridLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp_content.margins");
    if(margins.size() == 4)
    {
        gridLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.hspacing");

    gridLayout->setVerticalSpacing(vspacing);
    gridLayout->setHorizontalSpacing(hspacing);


    //设备设置界面布局
    auto lbDeviceType = new QLabel("设备类型");
    m_combDeviceType = new QComboBox;

    auto lbMeasureModel = new QLabel("测量模式");
    m_combMeasureModel = new QComboBox;
    m_btnMeasureModel = new QPushButton("快捷设置");
    // m_btnMeasureModel->setFlat(true);

    auto lbUpdateRate = new QLabel("更新率");
    m_combUpdateRate = new QComboBox;

    auto lbSynchPoints = new QLabel("同步积分");
    m_switchBtn = new SwitchButton;


    gridLayout->addWidget(lbDeviceType, 0, 0, 1, 1, Qt::AlignLeft );
    gridLayout->addWidget(m_combDeviceType, 0, 1, 1, 1, Qt::AlignLeft );

    gridLayout->addWidget(lbMeasureModel, 1, 0, 1, 1, Qt::AlignLeft);
    gridLayout->addWidget(m_combMeasureModel, 1, 1, 1, 1, Qt::AlignLeft);
    gridLayout->addWidget(m_btnMeasureModel, 1, 2, 1, 1, Qt::AlignLeft);

    gridLayout->addWidget(lbUpdateRate, 2, 0, 1, 1, Qt::AlignLeft);
    gridLayout->addWidget(m_combUpdateRate, 2, 1, 1, 1, Qt::AlignLeft );

    gridLayout->addWidget(lbSynchPoints, 3, 0, 1, 1, Qt::AlignLeft);
    gridLayout->addWidget(m_switchBtn, 3, 1, 1, 1, Qt::AlignLeft );

    gridLayout->setAlignment(Qt::AlignLeft);

    gridLayout->setColumnStretch(3, 1);
}
