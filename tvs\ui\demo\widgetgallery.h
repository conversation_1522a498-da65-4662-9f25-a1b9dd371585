#ifndef WIDGETGALLERY_H
#define WIDGETGALLERY_H

/******************************************************************************
  File Name     : themeeditor.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : Qt控件展示示例
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QDialog>

QT_BEGIN_NAMESPACE
class QGroupBox;
class QProgressBar;
class QTabWidget;
class QTextBrowser;
class QToolBox;
class QComboBox;

QT_END_NAMESPACE

class WidgetGallery : public QDialog
{
    Q_OBJECT

public:
    explicit WidgetGallery(QWidget *parent = nullptr);

    void setVisible(bool visible) override;

private slots:
    void changeStyle(const QString &styleName);
    void advanceProgressBar();
    void helpOnCurrentWidget();
    void updateSystemInfo();

private:
    static QGroupBox *createButtonsGroupBox(QWidget *parent = nullptr);
    static QTabWidget *createItemViewTabWidget();
    static QGroupBox *createSimpleInputWidgetsGroupBox();
    QToolBox *createTextToolBox();
    QProgressBar *createProgressBar();

    QProgressBar *progressBar;
    QTextBrowser *systemInfoTextBrowser;

    QComboBox* m_cmdComboBox;
};

#endif
