#ifndef COUNTCODE_H
#define COUNTCODE_H

/******************************************************************************
  File Name     : countcode.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 统计代码-开发用
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/


#include <QWidget>
class QTableWidget;
class QLineEdit;
class QPushButton;
class QLabel;

class CountCode : public QWidget
{
    Q_OBJECT
public:
    explicit CountCode(QWidget *parent = nullptr);

private:
    QStringList listFile;

private:
    void initForm();
    bool checkFile(const QString &fileName);
    void countCode(const QString &filePath);
    void countCode(const QStringList &files);
    void countCode(const QString &fileName, int &lineCode, int &lineBlank, int &lineNotes);

    void setupUi();

private slots:
    void on_btnOpenFile_clicked();
    void on_btnOpenPath_clicked();
    void on_btnClear_clicked();

protected:
    QAction *actionOpen;
    QAction *actionOpenDir;
    QAction *actionAbout;
    QAction *actionClearModel;
    QAction *actionClearModelLine;
    QAction *actionChinese;
    QAction *actionEnglish;
    QAction *actionUTF8;
    QAction *actionGB18030;
    QAction *actionQuit;

    QTableWidget *tableWidget;

    QLabel *labPercentCode;
    QLabel *labPercentNote;
    QLabel *labPercentBlank;

    QLineEdit *txtCode;
    QLineEdit *txtRow;
    QLineEdit *txtNote;
    QLineEdit *txtBlank;
    QLineEdit *txtCount;

    QLineEdit *txtSize;

    QLineEdit *txtPath;
    QLineEdit *txtFile;

    QLineEdit *txtFilter;

    QPushButton *btnOpenFile;
    QPushButton *btnOpenPath;
    QPushButton *btnClear;

signals:
};

#endif // COUNTCODE_H
