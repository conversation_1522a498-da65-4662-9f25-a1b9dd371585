#include "widgetgallery.h"
#include "widgets/stdwidget.h"
#include "widgets/buttonedit.h"
#include "widgets/colorbutton.h"
#include "widgets/fixedit.h"
#include "widgets/toast.h"

#include <QApplication>
#include <QCheckBox>
#include <QComboBox>
#include <QCommandLinkButton>
#include <QDateTimeEdit>
#include <QDial>
#include <QDialogButtonBox>
#include <QFileSystemModel>
#include <QGridLayout>
#include <QGroupBox>
#include <QMenu>
#include <QLabel>
#include <QLineEdit>
#include <QListWidget>
#include <QPlainTextEdit>
#include <QProgressBar>
#include <QPushButton>
#include <QRadioButton>
#include <QScrollBar>
#include <QShortcut>
#include <QSpinBox>
#include <QStandardItemModel>
#include <QStyle>
#include <QStyleFactory>
#include <QTextBrowser>
#include <QTreeView>
#include <QTableWidget>
#include <QTextEdit>
#include <QToolBox>
#include <QToolButton>

#include <QIcon>
#include <QDesktopServices>
#include <QScreen>
#include <QWindow>

#include <QDebug>
#include <QFileDialog>
#include <QLibraryInfo>
#include <QMessageBox>
#include <QSysInfo>
#include <QTextStream>
#include <QTimer>

#include <utils/actionmanager.h>
#include "JlCompress.h"

static inline QString className(const QObject *o)
{
    return QString::fromUtf8(o->metaObject()->className());
}

static inline void setClassNameToolTip(QWidget *w)
{
    w->setToolTip(className(w));
}

static QString helpUrl(const QString &page)
{
    QString result;
    QTextStream(&result) << "https://doc.qt.io/qt-" << QT_VERSION_MAJOR
                         << '/' << page << ".html";
    return result;
}

static inline QString helpUrl(const QWidget *w)
{
    return helpUrl(className(w).toLower());
}

static void launchHelp(const QWidget *w)
{
    QDesktopServices::openUrl(helpUrl(w));
}

static void launchModuleHelp()
{
    QDesktopServices::openUrl(helpUrl(QLatin1String("qtwidgets-index")));
}

template <class Widget>
Widget *createWidget(const char *name, QWidget *parent = nullptr)
{
    auto result = new Widget(parent);
    result->setObjectName(QLatin1String(name));
    setClassNameToolTip(result);
    return result;
}

template <class Widget, class Parameter>
Widget *createWidget1(const Parameter &p1, const char *name, QWidget *parent = nullptr)
{
    auto result = new Widget(p1, parent);
    result->setObjectName(QLatin1String(name));
    setClassNameToolTip(result);
    return result;
}

QTextStream &operator<<(QTextStream &str, const QRect &r)
{
    str << r.width() << 'x' << r.height() << Qt::forcesign << r.x() << r.y()
        << Qt::noforcesign;
    return str;
}

static QString highDpiScaleFactorRoundingPolicy()
{
    QString result;
    QDebug(&result) << QGuiApplication::highDpiScaleFactorRoundingPolicy();
    if (result.endsWith(QLatin1Char(')')))
        result.chop(1);
    const int lastSep = result.lastIndexOf(QLatin1String("::"));
    if (lastSep != -1)
        result.remove(0, lastSep + 2);
    return result;
}

WidgetGallery::WidgetGallery(QWidget *parent)
    : QDialog(parent)
    , progressBar(createProgressBar())
{
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);

    // auto styleComboBox = createWidget<QComboBox>("styleComboBox");
    // const QString defaultStyleName = QApplication::style()->objectName();
    // QStringList styleNames = QStyleFactory::keys();
    // for (int i = 1, size = styleNames.size(); i < size; ++i)
    // {
    //     if (defaultStyleName.compare(styleNames.at(i), Qt::CaseInsensitive) == 0)
    //     {
    //         styleNames.swapItemsAt(0, i);
    //         break;
    //     }
    // }
    // styleComboBox->addItems(styleNames);

    // auto styleLabel = createWidget1<QLabel>(tr("&Style:"), "styleLabel");
    // styleLabel->setBuddy(styleComboBox);

    // auto helpLabel = createWidget1<QLabel>(tr("Press F1 over a widget to see Documentation"), "helpLabel");

    auto disableWidgetsCheckBox = createWidget1<QCheckBox>(tr("&Disable widgets"), "disableWidgetsCheckBox");

    auto buttonsGroupBox = createButtonsGroupBox(this);
    auto itemViewTabWidget = createItemViewTabWidget();
    auto simpleInputWidgetsGroupBox = createSimpleInputWidgetsGroupBox();
    auto textToolBox = createTextToolBox();

    // connect(styleComboBox, &QComboBox::textActivated,
    //         this, &WidgetGallery::changeStyle);

    connect(disableWidgetsCheckBox, &QCheckBox::toggled,
            buttonsGroupBox, &QWidget::setDisabled);
    connect(disableWidgetsCheckBox, &QCheckBox::toggled,
            textToolBox, &QWidget::setDisabled);
    connect(disableWidgetsCheckBox, &QCheckBox::toggled,
            itemViewTabWidget, &QWidget::setDisabled);
    connect(disableWidgetsCheckBox, &QCheckBox::toggled,
            simpleInputWidgetsGroupBox, &QWidget::setDisabled);


    m_cmdComboBox = createWidget<QComboBox>("cmdComboBox");
    m_cmdComboBox->addItems(ActionManager::instance().allCmds());
    connect(m_cmdComboBox, &QComboBox::textActivated, [](const QString &cmd)
    {

    });

    auto testBtn = new QPushButton("Test Cmd");
    connect(testBtn, &QPushButton::clicked, [=]()
    {
        ActionManager::instance().executeCommand(m_cmdComboBox->currentText());
    });


    auto topLayout = new QHBoxLayout;
    // topLayout->addWidget(styleLabel);
    // topLayout->addWidget(styleComboBox);
    topLayout->addWidget(m_cmdComboBox);
    topLayout->addWidget(testBtn);


    topLayout->addStretch(1);
    // topLayout->addWidget(helpLabel);
    // topLayout->addStretch(1);
    topLayout->addWidget(disableWidgetsCheckBox);

    auto dialogButtonBox = createWidget1<QDialogButtonBox>(QDialogButtonBox::Help | QDialogButtonBox::Close,
                           "dialogButtonBox");
    // connect(dialogButtonBox, &QDialogButtonBox::helpRequested, this, launchModuleHelp);
    // connect(dialogButtonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);

    auto mainLayout = new QGridLayout(this);
    mainLayout->addLayout(topLayout, 0, 0, 1, 2);
    mainLayout->addWidget(buttonsGroupBox, 1, 0);
    mainLayout->addWidget(simpleInputWidgetsGroupBox, 1, 1);
    mainLayout->addWidget(itemViewTabWidget, 2, 0);
    mainLayout->addWidget(textToolBox, 2, 1);
    mainLayout->addWidget(progressBar, 3, 0, 1, 2);
    mainLayout->addWidget(dialogButtonBox, 4, 0, 1, 2);

    mainLayout->setRowStretch(2, 1);

    setWindowTitle(tr("Widget Gallery Qt %1").arg(QT_VERSION_STR));

    new QShortcut(QKeySequence::HelpContents, this, this, &WidgetGallery::helpOnCurrentWidget);
}

void  WidgetGallery::setVisible(bool visible)
{
    QDialog::setVisible(visible);
    if (visible)
    {
        connect(windowHandle(), &QWindow::screenChanged, this, &WidgetGallery::updateSystemInfo);
        updateSystemInfo();
    }
}

void WidgetGallery::changeStyle(const QString &styleName)
{
    QApplication::setStyle(QStyleFactory::create(styleName));
}

void WidgetGallery::advanceProgressBar()
{
    int curVal = progressBar->value();
    int maxVal = progressBar->maximum();
    progressBar->setValue(curVal + (maxVal - curVal) / 100);
}

QGroupBox *WidgetGallery::createButtonsGroupBox(QWidget *parent)
{
    auto result = createWidget1<QGroupBox>(tr("Buttons"), "buttonsGroupBox");

    auto defaultPushButton = createWidget1<QPushButton>(tr("Default Push Button"), "defaultPushButton");
    defaultPushButton->setDefault(true);

    auto togglePushButton = createWidget1<QPushButton>(tr("Toggle Push Button"), "togglePushButton");
    togglePushButton->setCheckable(true);
    togglePushButton->setChecked(true);

    auto flatPushButton = createWidget1<QPushButton>(tr("Flat Push Button"), "flatPushButton");
    flatPushButton->setFlat(true);

    auto toolButton = createWidget<QToolButton>("toolButton");
    toolButton->setText(tr("Tool Button"));

    auto menuToolButton = createWidget<QToolButton>("menuButton");
    menuToolButton->setText(tr("Menu Button"));
    auto toolMenu = new QMenu(menuToolButton);
    menuToolButton->setPopupMode(QToolButton::InstantPopup);
    toolMenu->addAction("Option");
    toolMenu->addSeparator();
    auto action = toolMenu->addAction("Checkable Option");
    action->setCheckable(true);
    menuToolButton->setMenu(toolMenu);

    auto toolLayout = new QHBoxLayout;
    toolLayout->addWidget(toolButton);
    toolLayout->addWidget(menuToolButton);

    // auto commandLinkButton = createWidget1<QCommandLinkButton>(tr("Command Link Button"), "commandLinkButton");
    // commandLinkButton->setDescription(tr("Description"));

    auto buttonLayout = new QVBoxLayout;
    buttonLayout->addWidget(defaultPushButton);
    buttonLayout->addWidget(togglePushButton);
    buttonLayout->addWidget(flatPushButton);
    buttonLayout->addLayout(toolLayout);
    // buttonLayout->addWidget(commandLinkButton);


    auto clrBtn = new ColorButton(nullptr);
    buttonLayout->addWidget(clrBtn);

    auto tabbar = new GroupButton();

    tabbar->addTab("选项1");
    tabbar->addTab("选项2");
    tabbar->addTab("选项3");
    buttonLayout->addWidget(tabbar);

    auto btn_compress_dir = new QPushButton("压缩文件夹");
    auto btn_extract_dir = new QPushButton("解压文件");

    auto zipLayout = new QHBoxLayout;
    zipLayout->addWidget(btn_compress_dir);
    zipLayout->addWidget(btn_extract_dir);

    buttonLayout->addLayout(zipLayout);
    buttonLayout->addStretch();

    connect(btn_compress_dir, &QPushButton::clicked, [=]()
    {
        QString dirName = QFileDialog::getExistingDirectory(nullptr, tr("选择文件夹"), "", QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
        if(dirName != "")
        {
            QString fileName = QFileDialog::getSaveFileName( nullptr, tr("文件保存为"), "", "青智工程文件(*.qz)");
            if(fileName != "")
            {
                try
                {
                    bool ret = JlCompress::compressDir(fileName, dirName);
                    qDebug() << "compressDir: " << ret;
                }
                catch (...)
                {
                    qDebug() << "compressDir: error";
                }
            }
        }
    });

    connect(btn_extract_dir, &QPushButton::clicked, [=]()
    {
        QString fileName = QFileDialog::getOpenFileName( nullptr, tr("打开工程"), "", "青智工程文件(*.qz)");
        if(fileName != "")
        {
            QString dirName = QFileDialog::getExistingDirectory(nullptr, tr("选择文件夹"), "", QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
            if(dirName != "")
            {
                try
                {
                    QStringList ret = JlCompress::extractDir(fileName, dirName);
                    qDebug() << "extractDir: " << ret;
                }
                catch (...)
                {
                    qDebug() << "extractDir: error";
                }
            }
        }
    });


    auto radioButton1 = createWidget1<QRadioButton>(tr("Radio button 1"), "radioButton1");
    auto radioButton2 = createWidget1<QRadioButton>(tr("Radio button 2"), "radioButton2");
    auto radioButton3 = createWidget1<QRadioButton>(tr("Radio button 3"), "radioButton3");
    radioButton1->setChecked(true);

    auto checkBox2 =  createWidget1<QCheckBox>(tr("check box"), "checkBox");

    auto checkBox3 =  createWidget1<QCheckBox>(tr("Tri-state check"), "checkBox");
    checkBox3->setTristate(true);
    checkBox3->setCheckState(Qt::PartiallyChecked);

    auto checkableLayout = new QVBoxLayout;
    checkableLayout->addWidget(radioButton1);
    checkableLayout->addWidget(radioButton2);
    checkableLayout->addWidget(radioButton3);
    checkableLayout->addWidget(checkBox2);
    checkableLayout->addWidget(checkBox3);

    checkableLayout->addStretch();


    auto msgBtn1 = new QPushButton("消息窗-消息");
    connect(msgBtn1, &QPushButton::clicked, [=]()
    {
        // QMessageBox msgBox;
        // msgBox.setIcon(QMessageBox::Information);
        // msgBox.setText("The document has been modified.");
        // msgBox.exec();

        QMessageBox::information(parent, tr("青智仪器"),
                                 tr("文档已修改\n"
                                    "是否保存更改？")
                                );
    });


    auto msgBtn2 = new QPushButton("消息窗-问题");
    connect(msgBtn2, &QPushButton::clicked, [=]()
    {
        int ret = QMessageBox::question(parent, tr("青智仪器"),
                                        tr("文档已修改\n"
                                           "是否保存更改？")
                                       );
    });


    auto msgBtn3 = new QPushButton("消息窗-警告");
    connect(msgBtn3, &QPushButton::clicked, [=]()
    {
        int ret = QMessageBox::warning(parent, tr("青智仪器"),
                                       tr("文档已修改\n"
                                          "是否保存更改？")
                                      );
    });

    auto msgBtn4 = new QPushButton("消息窗-错误");
    connect(msgBtn4, &QPushButton::clicked, [=]()
    {
        int ret = QMessageBox::critical(parent, tr("青智仪器"),
                                        tr("文档已修改\n"
                                           "是否保存更改？"));
    });

    auto msgBtn5 = new QPushButton("消息窗-关于");
    connect(msgBtn5, &QPushButton::clicked, [=]()
    {
        QMessageBox::about(parent, tr("青智仪器"),
                           ("版本: 1.87.2 (user setup)\n"
                            "提交: 863d2581ecda6849923a2118d93a088b0745d9d6\n"
                            "日期: 2024-03-08T15:20:17.278Z (2 周前)"));
    });


    auto msgBtn6 = new QPushButton("消息窗-所有按钮");
    connect(msgBtn6, &QPushButton::clicked, [=]()
    {
        QMessageBox::information(parent, "标题", "内容",
                                 QMessageBox::Ok|
                                 QMessageBox::Save|
                                 QMessageBox::SaveAll|
                                 QMessageBox::Open |
                                 QMessageBox::Yes|
                                 QMessageBox::YesToAll|
                                 QMessageBox::No|
                                 QMessageBox::NoToAll|
                                 QMessageBox::Abort|
                                 QMessageBox::Retry|
                                 QMessageBox::Ignore|
                                 QMessageBox::Close|
                                 QMessageBox::Cancel|
                                 QMessageBox::Discard|
                                 QMessageBox::Help|
                                 QMessageBox::Apply|
                                 QMessageBox::Reset|
                                 QMessageBox::RestoreDefaults
                                );
    });

    auto msgBtn7 = new QPushButton("消息窗-自定义按钮");
    connect(msgBtn7, &QPushButton::clicked, [=]()
    {
        int ret = QMessageBox::warning(parent, tr("青智仪器"),
                                       tr("文档已修改\n"
                                          "是否保存更改？"),
                                       QMessageBox::Save | QMessageBox::Discard | QMessageBox::Cancel,
                                       QMessageBox::Save
                                      );
    });

    auto msgBtn8 = new QPushButton("消息窗-提示");
    connect(msgBtn8, &QPushButton::clicked, [=]()
    {
        Toast::showMsg("保存成功");
    });


    auto msgboxLayout = new QVBoxLayout;
    msgboxLayout->addWidget(msgBtn1);
    msgboxLayout->addWidget(msgBtn2);
    msgboxLayout->addWidget(msgBtn3);
    msgboxLayout->addWidget(msgBtn4);
    msgboxLayout->addWidget(msgBtn5);
    msgboxLayout->addWidget(msgBtn6);
    msgboxLayout->addWidget(msgBtn7);
    // msgboxLayout->addWidget(msgBtn8);
    msgboxLayout->addStretch();


    auto tbtn1 = new QPushButton("Normal");
    auto tbtn2 = new ButtonPrimary("Primary");
    auto tbtn3 = new ButtonSuccess("Success");
    auto tbtn4 = new ButtonInfo("Info");
    auto tbtn5 = new ButtonWarning("Warning");
    auto tbtn6 = new ButtonDanger("Danger");

    auto typebtnLayout = new QVBoxLayout;
    typebtnLayout->addWidget(tbtn1);
    typebtnLayout->addWidget(tbtn2);
    typebtnLayout->addWidget(tbtn3);
    typebtnLayout->addWidget(tbtn4);
    typebtnLayout->addWidget(tbtn5);
    typebtnLayout->addWidget(tbtn6);
    typebtnLayout->addStretch();


    auto mainLayout = new QHBoxLayout(result);
    mainLayout->addLayout(buttonLayout);
    mainLayout->addLayout(typebtnLayout);
    mainLayout->addLayout(msgboxLayout);
    mainLayout->addLayout(checkableLayout);
    // mainLayout->addStretch();
    return result;
}

static QWidget *embedIntoHBoxLayout(QWidget *w, int margin = 5)
{
    auto result = new QWidget;
    auto layout = new QHBoxLayout(result);
    layout->setContentsMargins(margin, margin, margin, margin);
    layout->addWidget(w);
    return result;
}

QToolBox *WidgetGallery::createTextToolBox()
{
    auto result = createWidget<QToolBox>("toolBox");

    const QString plainText = tr("Twinkle, twinkle, little star,\n"
                                 "How I wonder what you are.\n"
                                 "Up above the world so high,\n"
                                 "Like a diamond in the sky.\n"
                                 "Twinkle, twinkle, little star,\n"
                                 "How I wonder what you are!\n");
    // Create centered/italic HTML rich text
    QString richText = QLatin1String("<html><head/><body><i>");
    for (const auto &line : plainText.split(QLatin1Char('\n')))
        richText += QLatin1String("<center>") + line + QLatin1String("</center>");
    richText += QLatin1String("</i></body></html>");

    auto textEdit = createWidget1<QTextEdit>(richText, "textEdit");
    auto plainTextEdit = createWidget1<QPlainTextEdit>(plainText, "plainTextEdit");

    systemInfoTextBrowser = createWidget<QTextBrowser>("systemInfoTextBrowser");

    result->addItem(embedIntoHBoxLayout(textEdit), tr("Text Edit"));
    result->addItem(embedIntoHBoxLayout(plainTextEdit), tr("Plain Text Edit"));
    result->addItem(embedIntoHBoxLayout(systemInfoTextBrowser), tr("Text Browser"));
    return result;
}

QTabWidget *WidgetGallery::createItemViewTabWidget()
{
    auto result = createWidget<QTabWidget>("bottomLeftTabWidget");
    result->setSizePolicy(QSizePolicy::Preferred, QSizePolicy::Ignored);

    auto treeView = createWidget<QTreeView>("treeView");
    auto fileSystemModel = new QFileSystemModel(treeView);
    fileSystemModel->setRootPath(QDir::rootPath());
    treeView->setModel(fileSystemModel);

    auto tableWidget = createWidget<QTableWidget>("tableWidget");
    tableWidget->setRowCount(10);
    tableWidget->setColumnCount(10);

    auto listModel = new QStandardItemModel(0, 1, result);
    listModel->appendRow(new QStandardItem(QIcon(QLatin1String(":/qt-project.org/styles/commonstyle/images/diropen-128.png")),
                                           tr("Directory")));
    listModel->appendRow(new QStandardItem(QIcon(QLatin1String(":/qt-project.org/styles/commonstyle/images/computer-32.png")),
                                           tr("Computer")));

    auto listView = createWidget<QListView>("listView");
    listView->setModel(listModel);

    auto iconModeListView = createWidget<QListView>("iconModeListView");
    iconModeListView->setViewMode(QListView::IconMode);
    iconModeListView->setModel(listModel);

    result->addTab(embedIntoHBoxLayout(treeView), tr("&Tree View"));
    result->addTab(embedIntoHBoxLayout(tableWidget), tr("T&able"));
    result->addTab(embedIntoHBoxLayout(listView), tr("&List"));
    result->addTab(embedIntoHBoxLayout(iconModeListView), tr("&Icon Mode List"));
    return result;
}

QGroupBox *WidgetGallery::createSimpleInputWidgetsGroupBox()
{
    auto result = createWidget1<QGroupBox>(tr("Simple Input Widgets"), "bottomRightGroupBox");
    result->setCheckable(true);
    result->setChecked(true);

    auto lineEdit = createWidget1<QLineEdit>("s3cRe7", "lineEdit");
    lineEdit->setClearButtonEnabled(true);
    lineEdit->setEchoMode(QLineEdit::Password);

    auto ipEdit = createWidget1<QLineEdit>("127.0.0.1", "ipEdit");
    ipEdit->setClearButtonEnabled(true);
    ipEdit->setValidator(new QRegularExpressionValidator(QRegularExpression("\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b")));
    ipEdit->setInputMask("***************; ");

    auto spinBox = createWidget<QSpinBox>("spinBox", result);
    spinBox->setValue(50);

    auto dateTimeEdit = createWidget<QDateTimeEdit>("dateTimeEdit", result);
    dateTimeEdit->setDateTime(QDateTime::currentDateTime());

    auto slider = createWidget<QSlider>("slider", result);
    slider->setOrientation(Qt::Horizontal);
    slider->setValue(40);

    auto scrollBar = createWidget<QScrollBar>("scrollBar", result);
    scrollBar->setOrientation(Qt::Horizontal);
    setClassNameToolTip(scrollBar);
    scrollBar->setValue(60);

    auto dial = createWidget<QDial>("dial", result);
    dial->setValue(30);
    dial->setNotchesVisible(true);

    auto btnEdit1 = new ButtonEdit("...");
    auto btnEdit2 = new ButtonEdit(QIcon("./res/icon/Open.png"));

    connect(btnEdit1, &ButtonEdit::buttonClicked,[=]()
    {
        QString fileName = QFileDialog::getOpenFileName(
                               nullptr,
                               tr("选择文件"),
                               "D:/",
                               tr("images(*.png *jpeg *bmp);;video files(*.avi *.mp4 *.wmv);;All files(*.*)"));
        btnEdit1->setText(fileName);
    }
           );

    connect(btnEdit2, &ButtonEdit::buttonClicked,[=]()
    {
        QString fileName = QFileDialog::getExistingDirectory(nullptr, tr("选择文件夹"), "", QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
        btnEdit2->setText(fileName);
    }
           );


    auto fixEdit1 = new FixEdit("单位：", true);
    auto fixEdit2 = new FixEdit("千克", false);
    auto fixEdit3 = new FixEdit(QIcon("./res/icon/option_normal.png"), true);
    auto fixEdit4 = new FixEdit(QIcon("./res/icon/option_normal.png"), false);



    auto layout = new QGridLayout(result);
    layout->addWidget(lineEdit, 0, 0, 1, 2);
    layout->addWidget(ipEdit, 1, 0, 1, 2);
    layout->addWidget(spinBox, 2, 0, 1, 2);
    layout->addWidget(dateTimeEdit, 3, 0, 1, 2);
    layout->addWidget(slider, 4, 0);
    layout->addWidget(scrollBar, 5, 0);
    layout->addWidget(dial, 4, 1, 2, 1);
    layout->addWidget(btnEdit1, 6, 0);
    layout->addWidget(btnEdit2, 7, 0);
    layout->addWidget(fixEdit1, 8, 0);
    layout->addWidget(fixEdit2, 9, 0);
    layout->addWidget(fixEdit3, 10, 0);
    layout->addWidget(fixEdit4, 11, 0);
    layout->setRowStretch(12, 1);
    return result;
}

QProgressBar *WidgetGallery::createProgressBar()
{
    auto result = createWidget<QProgressBar>("progressBar");
    result->setRange(0, 10000);
    result->setValue(0);

    auto timer = new QTimer(this);
    connect(timer, &QTimer::timeout, this, &WidgetGallery::advanceProgressBar);
    timer->start(1000);
    return result;
}

void WidgetGallery::updateSystemInfo()
{
    QString systemInfo;
    QTextStream str(&systemInfo);
    str << "<html><head/><body><h3>Build</h3><p>" << QLibraryInfo::build() << "</p>"
        << "<h3>Operating System</h3><p>" << QSysInfo::prettyProductName() << "</p>"
        << "<h3>Screens</h3><p>High DPI scale factor rounding policy: "
        << highDpiScaleFactorRoundingPolicy() << "</p><ol>";
    const auto screens = QGuiApplication::screens();
    for (auto screen : screens)
    {
        const bool current = screen == this->screen();
        str << "<li>";
        if (current)
            str << "<i>";
        str << '"' << screen->name() << "\" " << screen->geometry() << ", "
            << screen->logicalDotsPerInchX() << "DPI, DPR="
            << screen->devicePixelRatio();
        if (current)
            str << "</i>";
        str << "</li>";
    }
    str << "</ol></body></html>";
    systemInfoTextBrowser->setHtml(systemInfo);
}

void WidgetGallery::helpOnCurrentWidget()
{
    // Skip over internal widgets
    for (auto w = QApplication::widgetAt(QCursor::pos(screen())); w; w = w->parentWidget())
    {
        const QString name = w->objectName();
        if (!name.isEmpty() && !name.startsWith(QLatin1String("qt_")))
        {
            launchHelp(w);
            break;
        }
    }
}
