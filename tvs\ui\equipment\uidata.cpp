#include "uidata.h"
#include "qheaderview.h"
#include "widgets/stdwidget.h"

#include <QLabel>
#include <QStackedWidget>
#include <QTableWidget>
#include <QToolBar>
#include <QVBoxLayout>
#include <mainwindow.h>

#include <app/settings.h>

#include <utils/actionmanager.h>
#include <Qsci/qsciscintilla.h>
#include <Qsci/qscilexerpython.h>
#include <Qsci/qscilexercpp.h>
#include <Qsci/qsciapis.h>
#include "widgets/codewidget/codewidget.h"



// 创建并配置编辑器
QsciScintilla *createPythonEditor(QWidget *parent)
{
    QsciScintilla *scintilla = new QsciScintilla(parent);
    QFont font("Consolas", 12); // 推荐等宽字体

    // 基础设置
    scintilla->setUtf8(true);
    scintilla->setFont(font);
    scintilla->setMarginsFont(font);
    
    QFontMetrics fontmetrics(font);;
    
    // 行号设置
    scintilla->setMarginWidth(0, fontmetrics.horizontalAdvance(QString::number(scintilla->lines())) + 5);
    scintilla->setMarginLineNumbers(0, true);
    
    // 边缘线设置
    scintilla->setEdgeMode(QsciScintilla::EdgeLine);
    scintilla->setEdgeColumn(150);
    scintilla->setEdgeColor(QColor("#BBB8B5"));
    
    // 括号匹配
    scintilla->setBraceMatching(QsciScintilla::StrictBraceMatch);
    
    // 当前行设置
    scintilla->setCaretLineVisible(true);
    scintilla->setCaretLineBackgroundColor(QColor("#2D2D2D"));
    scintilla->setCaretForegroundColor(QColor("white"));
    
    // 选中文本设置
    scintilla->setSelectionBackgroundColor(QColor("#606060"));
    scintilla->setSelectionForegroundColor(QColor("#FFFFFF"));
    
    // 缩进相关设置
    scintilla->setIndentationsUseTabs(true);
    scintilla->setIndentationWidth(4);
    scintilla->setTabIndents(true);
    scintilla->setAutoIndent(true);
    scintilla->setBackspaceUnindents(true);
    scintilla->setTabWidth(4);
    
    // 缩进参考线
    scintilla->setIndentationGuides(true);
    
    // 行号边栏颜色
    scintilla->setMarginsBackgroundColor(QColor("#272727"));
    scintilla->setMarginsForegroundColor(QColor("#CCCCCC"));
    
    // 代码折叠设置
    scintilla->setFolding(QsciScintilla::PlainFoldStyle);
    scintilla->setMarginWidth(2, 12);
    
    // 折叠标记定义
    scintilla->markerDefine(QsciScintilla::Minus, QsciScintilla::SC_MARKNUM_FOLDEROPEN);
    scintilla->markerDefine(QsciScintilla::Plus, QsciScintilla::SC_MARKNUM_FOLDER);
    scintilla->markerDefine(QsciScintilla::Minus, QsciScintilla::SC_MARKNUM_FOLDEROPENMID);
    scintilla->markerDefine(QsciScintilla::Plus, QsciScintilla::SC_MARKNUM_FOLDEREND);
    
    // 折叠标记颜色
    scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDEREND);
    scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDEREND);
    scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDEROPENMID);
    scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDEROPENMID);
    scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDERSUB);
    scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDERSUB);
    scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDER);
    scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDER);
    scintilla->setMarkerBackgroundColor(QColor("#FFFFFF"), QsciScintilla::SC_MARKNUM_FOLDEROPEN);
    scintilla->setMarkerForegroundColor(QColor("#272727"), QsciScintilla::SC_MARKNUM_FOLDEROPEN);
    scintilla->setFoldMarginColors(QColor("#272727"), QColor("#272727"));
    
    // 空白字符设置
    scintilla->setWhitespaceVisibility(QsciScintilla::WsInvisible);
    scintilla->setWhitespaceSize(2);
    
    // 边栏设置
    scintilla->setMarginWidth(1, 0);
    
    // 设置词法分析器
    QsciLexerPython* lexer = new QsciLexerPython();
    lexer->setFont(font);
    lexer->setColor(QColor("#ffffff"));
    scintilla->setLexer(lexer);
    
    // 语法高亮设置
    lexer->setColor(QColor("#ffffff"));
    lexer->setPaper(QColor("#333333"));
    lexer->setColor(QColor("#5BA5F7"), QsciLexerPython::ClassName);
    lexer->setColor(QColor("#FF0B66"), QsciLexerPython::Keyword);
    lexer->setColor(QColor("#00FF40"), QsciLexerPython::Comment);
    lexer->setColor(QColor("#BD4FE8"), QsciLexerPython::Number);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::DoubleQuotedString);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::TripleSingleQuotedString);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::TripleDoubleQuotedString);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::SingleQuotedString);
    lexer->setColor(QColor("#04F452"), QsciLexerPython::FunctionMethodName);
    lexer->setColor(QColor("#FFFFFF"), QsciLexerPython::Operator);
    lexer->setColor(QColor("#FFFFFF"), QsciLexerPython::Identifier);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::CommentBlock);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::UnclosedString);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::HighlightedIdentifier);
    lexer->setColor(QColor("#F1E607"), QsciLexerPython::Decorator);
    


    return scintilla;

}


// 创建并配置编辑器
QsciScintilla *createPythonEditor2(QWidget *parent)
{
    QsciScintilla *editor = new QsciScintilla(parent);

    // === 1. 基础设置 ===
    QFont font("Consolas", 12); // 推荐等宽字体
    editor->setFont(font);
    editor->setMarginsFont(font);

    // === 2. 深色主题配置 ===
    QsciLexerPython *pythonLexer = new QsciLexerPython();
    pythonLexer->setDefaultFont(font);
    
    editor->setPaper(QColor("#1E1E1E")); // 背景色
    editor->setColor(QColor("#D4D4D4")); // 默认字体颜色
    editor->setLexer(pythonLexer);

    // 设置深色配色方案
    pythonLexer->setPaper(QColor("#1E1E1E"));  // 背景色
    pythonLexer->setColor(QColor("#D4D4D4"));  // 默认文本颜色
    
    // 语法高亮颜色配置
    pythonLexer->setColor(QColor("#569CD6"), QsciLexerPython::Keyword);            // 关键字 (蓝色)
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::DoubleQuotedString); // 双引号字符串 (橙色)
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::SingleQuotedString); // 单引号字符串 (橙色)
    pythonLexer->setColor(QColor("#B5CEA8"), QsciLexerPython::Comment);            // 注释 (绿色)
    pythonLexer->setColor(QColor("#DCDCAA"), QsciLexerPython::Number);             // 数字
    pythonLexer->setColor(QColor("#9CDCFE"), QsciLexerPython::ClassName);          // 类名 (浅蓝)
    pythonLexer->setColor(QColor("#4EC9B0"), QsciLexerPython::FunctionMethodName); // 函数/方法名
    pythonLexer->setColor(QColor("#D7BA7D"), QsciLexerPython::Operator);           // 操作符
    pythonLexer->setColor(QColor("#C586C0"), QsciLexerPython::Decorator);          // 装饰器
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::TripleSingleQuotedString); // 三引号字符串
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::TripleDoubleQuotedString); // 三引号字符串
    pythonLexer->setColor(QColor("#D16969"), QsciLexerPython::UnclosedString);     // 未闭合字符串
    
    // F-Strings 支持 (Python 3.6+)
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::DoubleQuotedFString);
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::SingleQuotedFString);
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::TripleSingleQuotedFString);
    pythonLexer->setColor(QColor("#CE9178"), QsciLexerPython::TripleDoubleQuotedFString);
    

    // === 3. 行号与边距 ===
    editor->setMarginType(0, QsciScintilla::NumberMargin);
    editor->setMarginWidth(0, "0000");                    // 自适应宽度
    editor->setMarginsBackgroundColor(QColor("#252526")); // 行号背景色
    editor->setMarginsForegroundColor(QColor("#858585")); // 行号文字色

    // === 4. 代码折叠设置 ===
    editor->setFolding(QsciScintilla::BoxedTreeFoldStyle); // 折叠样式
    editor->setFoldMarginColors(QColor("#252526"), QColor("#252526")); // 折叠边栏颜色
    editor->setMarginType(1, QsciScintilla::SymbolMargin); // 设置第二个边栏为符号边栏
    editor->setMarginWidth(1, 16);                        // 折叠边栏宽度
    editor->setMarginSensitivity(1, true);                // 允许点击折叠

    // === 5. 缩进与制表符 ===
    editor->setAutoIndent(true);
    editor->setIndentationGuides(true); // 显示缩进参考线
    editor->setTabWidth(4);
    editor->setIndentationsUseTabs(false); // 用空格代替制表符

    // === 6. 括号匹配高亮 ===
    editor->setBraceMatching(QsciScintilla::SloppyBraceMatch);
    editor->setMatchedBraceBackgroundColor(QColor("#51504F")); // 匹配括号背景色
    editor->setMatchedBraceForegroundColor(QColor("#FFD700")); // 匹配括号文字色

    // === 7. 自动补全配置 ===
    editor->setAutoCompletionSource(QsciScintilla::AcsAll); // 补全来源（全部内容）
    editor->setAutoCompletionCaseSensitivity(true);         // 区分大小写
    editor->setAutoCompletionThreshold(1);                  // 输入1个字符后触发补全
    editor->setAutoCompletionReplaceWord(true);             // 补全时替换当前单词

    // 加载Python API（关键字+内置函数补全）
    QsciAPIs *apis = new QsciAPIs(pythonLexer);
    apis->add("def class if else for while import from return break continue");
    apis->add("print len str int list dict tuple set range");
    apis->add("True False None and or not");
    apis->prepare();

    // === 8. 其他视觉优化 ===
    editor->setCaretLineVisible(true);                      // 高亮当前行
    editor->setCaretLineBackgroundColor(QColor("#2D2D30")); // 当前行背景色
    editor->setCaretForegroundColor(QColor("#FFFFFF"));     // 光标颜色
    
    // 设置选中文本颜色
    editor->setSelectionBackgroundColor(QColor("#264F78"));
    editor->setSelectionForegroundColor(QColor("#FFFFFF"));

    return editor;
}

UiData::UiData(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_toolbar_pane.margins");
    if (margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto toolBar = new QToolBar();
    mainLayout->addWidget(toolBar);

    QVBoxLayout *paneLayout = new QVBoxLayout();
    mainLayout->addLayout(paneLayout, 1);

    QVector<int> pane_margins = YamlConfigUI::instance().getArray<int>("ui_pane.margins");
    if (pane_margins.size() == 4)
    {
        paneLayout->setContentsMargins(pane_margins[0], pane_margins[1], pane_margins[2], pane_margins[3]);
    }

    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    toolBar->addWidget(new QLabel("数值视图"));

    QWidget *blank = new QWidget();
    blank->setObjectName("Blank");
    blank->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    toolBar->addWidget(blank);

    QAction *hide = toolBar->addAction(QIcon("./res/icon/widget/hide.png"), "");
    connect(hide, &QAction::triggered, [this]()
            {
        this->hide();
        MainWindow::instance->setActionChecked("数值视图", false); });

    // tabbar
    auto tabbar = new GroupButton();

    tabbar->addTab("常规数据");
    tabbar->addTab("积分数据");
    tabbar->addTab("谐波指标");
    tabbar->addTab("谐波列表");
    tabbar->addTab("用户自定义");

    tabbar->setCurrentIndex(0);
    tabbar->setSizePolicy(QSizePolicy(QSizePolicy::Maximum, QSizePolicy::Maximum));

    paneLayout->addWidget(tabbar);

    // stackedWidget
    auto stackedWidget = new QStackedWidget();
    stackedWidget->setCurrentIndex(0);
    paneLayout->addWidget(stackedWidget, 1);

    connect(tabbar, SIGNAL(currentChanged(int)), stackedWidget, SLOT(setCurrentIndex(int)));

    ///////////////////////////////////////////////////////////////////////////////////////
    // tb_common
    QTableWidget *tb_common = new QTableWidget;

    // 隐藏表头
    tb_common->verticalHeader()->setVisible(false);
    tb_common->horizontalHeader()->setVisible(false);

    // 整行选中
    tb_common->setSelectionBehavior(QAbstractItemView::SelectRows);
    // 不可编辑
    tb_common->setEditTriggers(QAbstractItemView::NoEditTriggers);
    // 单选
    tb_common->setSelectionMode(QAbstractItemView::SingleSelection);
    // 开启隔行背景颜色, alternate-background-color: rgb(218, 233, 231);
    tb_common->setAlternatingRowColors(true);

    QVector<QVector<QString>> tb1_data =
        {
            {"功率数据", "V", "A", "V", "A", "W", "var", "VA", "Hz", "Hz", "λ"},
            {"机械数据", "rpm", "Nm", "°", "W", "rpm", "%", "", "", "", ""},
            {"其他数据", "η1", "η2", "η3", "Udef1", "Udef2", "F1", "F2", "F3", "F4", "F5"},

        };

    tb_common->setRowCount(tb1_data.size());
    tb_common->setColumnCount(tb1_data[0].size());

    // 设置表头
    // QStringList headers;
    // for (int col = 0; col < data[0].size(); ++col) {
    //     headers << QString("Column %1").arg(col + 1);
    // }
    // tb_common->setHorizontalHeaderLabels(headers);

    // 填充表格数据
    for (int row = 0; row < tb1_data.size(); ++row)
    {
        for (int col = 0; col < tb1_data[row].size(); ++col)
        {
            QTableWidgetItem *item = new QTableWidgetItem(tb1_data[row][col]);
            tb_common->setItem(row, col, item);
        }
    }

    stackedWidget->addWidget(tb_common);

    ///////////////////////////////////////////////////////////////////////////////////////
    // 其他表格
    auto tabel1 = new QLabel("");
    tabel1->setStyleSheet("background-color: lightyellow;");
    stackedWidget->addWidget(tabel1);

    auto tabel2 = new QLabel("");
    tabel2->setStyleSheet("background-color: lightblue;");
    stackedWidget->addWidget(tabel2);

    auto tabel3 = new QLabel("");
    tabel3->setStyleSheet("background-color: lightgreen;");
    stackedWidget->addWidget(tabel3);

    // QsciScintilla *editor = createPythonEditor(nullptr);
    CodeWidget *editor = new CodeWidget(nullptr);
    QString le ("py");
    editor->setCurLexer(le);

    
    stackedWidget->addWidget(editor);
}
