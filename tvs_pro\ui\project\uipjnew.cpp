#include "uipjnew.h"
#include "widgets/stdwidget.h"


#include <MainWindow.h>
#include <QGridLayout>
#include <QLabel>

#include <app/settings.h>

UiPjNewGrid::UiPjNewGrid(QWidget *parent)
    : QWidget{parent}
{
    // 创建左边的栅格布局
    m_gridLayout = new QGridLayout(this);

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_pj_new.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_pj_new.hspacing");

    m_gridLayout->setVerticalSpacing(vspacing);
    m_gridLayout->setHorizontalSpacing(hspacing);

    auto btn_equip = setIconButton( 0, 0, "操作设备", "");
    setButton(1, 0, "连接设备");
    setLabel( 2, 0, "↓");
    setButton(3, 0, "配置设备参数");
    setLabel( 4, 0, "↓");
    setButton(5, 0, "操作设备&监视数据");
    setLabel( 6, 0, "↓");
    setButton(7, 0, "分析数据");


    setIconButton( 0, 1, "分析文件", "");
    setButton(1, 1, "导入文件");
    setLabel( 2, 1, "↓");
    setButton(3, 1, "选择关心的数据");
    setLabel( 4, 1, "↓");
    setButton(5, 1, "分析数据");


    auto btn_test = setIconButton( 0, 2, "执行测试", "");
    setButton(1, 2, "连接设备");
    setLabel( 2, 2, "↓");
    setButton(3, 2, "配置设备参数");
    setLabel( 4, 2, "↓");
    setButton(5, 2, "操作设备&监视数据");
    setLabel( 6, 2, "↓");
    setButton(7, 2, "分析数据");


    auto btn_open = setIconButton( 0, 3, "打开工程", "");
    setButton(1, 3, "打开已有工程");


    // 添加弹簧以使布局在窗口变化时适应
    m_gridLayout->setRowStretch(8, 1);
    m_gridLayout->setColumnStretch(4, 1);

    // 按钮响应事件
    connect(btn_equip, &QPushButton::clicked, []()
    {
        MainWindow::instance->newEquipment();
    });

    connect(btn_test, &QPushButton::clicked, []()
    {
        MainWindow::instance->newTest();
    });

    connect(btn_open, &QPushButton::clicked, []()
    {
        MainWindow::instance->openProject();
    });
}


QPushButton* UiPjNewGrid::setIconButton(int row, int column, const QString &text, const QString &fileIcon)
{
    auto btn = new WelcomeIconButton(text);
    // btn->setIcon(QIcon(fileIcon));               // 按键设置图标
    // btn->setIconSize(QSize(30, 30));             // 按键设置图标大小
    // btn->setStyleSheet("WelcomeIconButton { background-image: url("+fileIcon+"); } ");

    m_gridLayout->addWidget(btn, row, column);
    return btn;
}


void UiPjNewGrid::setLabel(int row, int column, const QString &text)
{
    m_gridLayout->addWidget(new QLabel(text), row, column, Qt::AlignCenter);
}

void UiPjNewGrid::setButton(int row, int column, const QString &text)
{
    auto btn = new QPushButton(text);
    btn->setEnabled(false);
    m_gridLayout->addWidget(btn, row, column);
}

UiPjNew::UiPjNew(QWidget *parent)
    : QWidget{parent}
{
    // 创建垂直布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_pj_new.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    // 创建标题
    auto *titleLabel = new LabelH2("打开");
    titleLabel->setObjectName("welcome");
    mainLayout->addWidget(titleLabel);

    auto uiNew = new UiPjNewGrid();
    mainLayout->addWidget(uiNew);
}
