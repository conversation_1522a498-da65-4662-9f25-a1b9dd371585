#include "countcode.h"
#include "qfile.h"
#include "qheaderview.h"
#include "qtextstream.h"
#include "qfiledialog.h"
#include "qfileinfo.h"
#include "qdebug.h"

#include <QCoreApplication>
#include <QGridLayout>
#include <QLabel>
#include <QLineEdit>
#include <QPushButton>
#include <QTableWidget>


CountCode::CountCode(QWidget *parent)
    : QWidget{parent}
{
    setupUi();
    this->initForm();
    on_btnClear_clicked();
}


void CountCode::initForm()
{
    QStringList headText;
    headText << "文件名" << "类型" << "大小" << "总行数" << "代码行数" << "注释行数" << "空白行数" << "路径";
    QList<int> columnWidth;
    columnWidth << 130 << 50 << 70 << 80 << 70 << 70 << 70 << 150;

    int columnCount = headText.count();
    tableWidget->setColumnCount(columnCount);
    tableWidget->setHorizontalHeaderLabels(headText);
    tableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    tableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    tableWidget->setSelectionMode(QAbstractItemView::SingleSelection);
    tableWidget->verticalHeader()->setVisible(false);
    tableWidget->horizontalHeader()->setStretchLastSection(true);
    tableWidget->horizontalHeader()->setHighlightSections(false);
    tableWidget->verticalHeader()->setDefaultSectionSize(20);
    tableWidget->verticalHeader()->setHighlightSections(false);

    for (int i = 0; i < columnCount; i++)
    {
        tableWidget->setColumnWidth(i, columnWidth.at(i));
    }

    //设置前景色
    txtCount->setStyleSheet("color:#17A086;");
    txtSize->setStyleSheet("color:#CA5AA6;");
    txtRow->setStyleSheet("color:#CD1B19;");
    txtCode->setStyleSheet("color:#22A3A9;");
    txtNote->setStyleSheet("color:#D64D54;");
    txtBlank->setStyleSheet("color:#A279C5;");

    //设置字体加粗
    QFont font;
    font.setBold(true);
    if (font.pointSize() > 0)
    {
        font.setPointSize(font.pointSize() + 1);
    }
    else
    {
        font.setPixelSize(font.pixelSize() + 2);
    }

    txtCount->setFont(font);
    txtSize->setFont(font);
    txtRow->setFont(font);
    txtCode->setFont(font);
    txtNote->setFont(font);
    txtBlank->setFont(font);


    txtFilter->setPlaceholderText("中间空格隔开,例如 *.h *.cpp *.c");
}

bool CountCode::checkFile(const QString &fileName)
{
    if (fileName.startsWith("moc_") || fileName.startsWith("ui_") || fileName.startsWith("qrc_"))
    {
        return false;
    }

    QFileInfo file(fileName);
    QString suffix = "*." + file.suffix();
    QString filter = txtFilter->text().trimmed();
    QStringList filters = filter.split(" ");
    return filters.contains(suffix);
}

void CountCode::countCode(const QString &filePath)
{
    QDir dir(filePath);
    QFileInfoList fileInfos = dir.entryInfoList();
    foreach (QFileInfo fileInfo, fileInfos)
    {
        QString fileName = fileInfo.fileName();
        if (fileInfo.isFile())
        {
            if (checkFile(fileName))
            {
                listFile << fileInfo.filePath();
            }
        }
        else
        {
            if (fileName == "." || fileName == "..")
            {
                continue;
            }

            //递归找出文件
            countCode(fileInfo.absoluteFilePath());
        }
    }
}

void CountCode::countCode(const QStringList &files)
{
    int lineCode;
    int lineBlank;
    int lineNotes;
    int count = files.count();
    on_btnClear_clicked();
    tableWidget->setRowCount(count);

    quint32 totalLines = 0;
    quint32 totalBytes = 0;
    quint32 totalCodes = 0;
    quint32 totalNotes = 0;
    quint32 totalBlanks = 0;

    for (int i = 0; i < count; i++)
    {
        QFileInfo fileInfo(files.at(i));
        countCode(fileInfo.filePath(), lineCode, lineBlank, lineNotes);
        int lineAll = lineCode + lineBlank + lineNotes;

        QTableWidgetItem *itemName = new QTableWidgetItem;
        itemName->setText(fileInfo.fileName());

        QTableWidgetItem *itemSuffix = new QTableWidgetItem;
        itemSuffix->setText(fileInfo.suffix());

        QTableWidgetItem *itemSize = new QTableWidgetItem;
        itemSize->setText(QString::number(fileInfo.size()));

        QTableWidgetItem *itemLine = new QTableWidgetItem;
        itemLine->setText(QString::number(lineAll));

        QTableWidgetItem *itemCode = new QTableWidgetItem;
        itemCode->setText(QString::number(lineCode));

        QTableWidgetItem *itemNote = new QTableWidgetItem;
        itemNote->setText(QString::number(lineNotes));

        QTableWidgetItem *itemBlank = new QTableWidgetItem;
        itemBlank->setText(QString::number(lineBlank));

        QTableWidgetItem *itemPath = new QTableWidgetItem;
        itemPath->setText(fileInfo.filePath());

        itemSuffix->setTextAlignment(Qt::AlignCenter);
        itemSize->setTextAlignment(Qt::AlignCenter);
        itemLine->setTextAlignment(Qt::AlignCenter);
        itemCode->setTextAlignment(Qt::AlignCenter);
        itemNote->setTextAlignment(Qt::AlignCenter);
        itemBlank->setTextAlignment(Qt::AlignCenter);

        tableWidget->setItem(i, 0, itemName);
        tableWidget->setItem(i, 1, itemSuffix);
        tableWidget->setItem(i, 2, itemSize);
        tableWidget->setItem(i, 3, itemLine);
        tableWidget->setItem(i, 4, itemCode);
        tableWidget->setItem(i, 5, itemNote);
        tableWidget->setItem(i, 6, itemBlank);
        tableWidget->setItem(i, 7, itemPath);

        totalBytes  += fileInfo.size();
        totalLines  += lineAll;
        totalCodes  += lineCode;
        totalNotes  += lineNotes;
        totalBlanks += lineBlank;

        if (i % 100 == 0)
        {
            QCoreApplication::processEvents();
        }
    }

    //显示统计结果
    listFile.clear();
    txtCount->setText(QString::number(count));
    txtSize->setText(QString::number(totalBytes));
    txtRow->setText(QString::number(totalLines));
    txtCode->setText(QString::number(totalCodes));
    txtNote->setText(QString::number(totalNotes));
    txtBlank->setText(QString::number(totalBlanks));

    //计算百分比
    double percent = 0.0;
    //代码行所占百分比
    percent = ((double)totalCodes / totalLines) * 100;
    labPercentCode->setText(QString("%1%").arg(percent, 5, 'f', 2, QChar(' ')));
    //注释行所占百分比
    percent = ((double)totalNotes / totalLines) * 100;
    labPercentNote->setText(QString("%1%").arg(percent, 5, 'f', 2, QChar(' ')));
    //空行所占百分比
    percent = ((double)totalBlanks / totalLines) * 100;
    labPercentBlank->setText(QString("%1%").arg(percent, 5, 'f', 2, QChar(' ')));
}

void CountCode::countCode(const QString &fileName, int &lineCode, int &lineBlank, int &lineNotes)
{
    lineCode = lineBlank = lineNotes = 0;
    QFile file(fileName);
    if (file.open(QFile::ReadOnly))
    {
        QTextStream out(&file);
        QString line;
        bool isNote = false;
        while (!out.atEnd())
        {
            line = out.readLine();

            //移除前面的空行
            if (line.startsWith(" "))
            {
                line.remove(" ");
            }

            //判断当前行是否是注释
            if (line.startsWith("/*"))
            {
                isNote = true;
            }

            //注释部分
            if (isNote)
            {
                lineNotes++;
            }
            else
            {
                if (line.startsWith("//"))      //注释行
                {
                    lineNotes++;
                }
                else if (line.isEmpty())        //空白行
                {
                    lineBlank++;
                }
                else                            //代码行
                {
                    lineCode++;
                }
            }

            //注释结束
            if (line.endsWith("*/"))
            {
                isNote = false;
            }
        }
    }
}

void CountCode::setupUi()
{
    this->resize(800, 600);

    auto mainLayout = new QVBoxLayout(this);
    mainLayout->setSpacing(6);
    mainLayout->setContentsMargins(11, 11, 11, 11);

    tableWidget = new QTableWidget(this);
    mainLayout->addWidget(tableWidget);

    auto gridLayout = new QGridLayout();
    mainLayout->addLayout(gridLayout);

    gridLayout->setContentsMargins(0, 0, 0, 0);
    gridLayout->setSpacing(6);

    txtCode = new QLineEdit(this);
    txtCode->setAlignment(Qt::AlignCenter);
    txtCode->setReadOnly(true);
    gridLayout->addWidget(txtCode, 0, 3, 1, 1);

    txtRow = new QLineEdit(this);
    txtRow->setAlignment(Qt::AlignCenter);
    txtRow->setReadOnly(true);
    gridLayout->addWidget(txtRow, 2, 1, 1, 1);

    txtNote = new QLineEdit(this);
    txtNote->setAlignment(Qt::AlignCenter);
    txtNote->setReadOnly(true);
    gridLayout->addWidget(txtNote, 1, 3, 1, 1);

    txtBlank = new QLineEdit(this);
    txtBlank->setAlignment(Qt::AlignCenter);
    txtBlank->setReadOnly(true);
    gridLayout->addWidget(txtBlank, 2, 3, 1, 1);

    txtCount = new QLineEdit(this);
    txtCount->setAlignment(Qt::AlignCenter);
    txtCount->setReadOnly(true);

    gridLayout->addWidget(txtCount, 0, 1, 1, 1);

    labPercentCode = new QLabel("", this);
    labPercentCode->setAlignment(Qt::AlignCenter);
    gridLayout->addWidget(labPercentCode, 0, 4, 1, 1);

    auto labBlank = new QLabel("空白行数", this);
    gridLayout->addWidget(labBlank, 2, 2, 1, 1);

    txtSize = new QLineEdit(this);
    txtSize->setAlignment(Qt::AlignCenter);
    txtSize->setReadOnly(true);
    gridLayout->addWidget(txtSize, 1, 1, 1, 1);

    auto labFilter = new QLabel("过滤", this);
    gridLayout->addWidget(labFilter, 2, 5, 1, 1);

    auto labFile = new QLabel("文件", this);
    gridLayout->addWidget(labFile, 0, 5, 1, 1);

    txtPath = new QLineEdit(this);

    txtPath->setReadOnly(true);

    gridLayout->addWidget(txtPath, 1, 6, 1, 1);

    txtFile = new QLineEdit(this);

    txtFile->setReadOnly(true);

    gridLayout->addWidget(txtFile, 0, 6, 1, 1);

    auto labPath = new QLabel("目录", this);
    gridLayout->addWidget(labPath, 1, 5, 1, 1);

    txtFilter = new QLineEdit("*.h *.cpp *.c", this);
    gridLayout->addWidget(txtFilter, 2, 6, 1, 1);

    labPercentNote = new QLabel("", this);
    labPercentNote->setAlignment(Qt::AlignCenter);

    gridLayout->addWidget(labPercentNote, 1, 4, 1, 1);

    labPercentBlank = new QLabel("", this);
    labPercentBlank->setAlignment(Qt::AlignCenter);

    gridLayout->addWidget(labPercentBlank, 2, 4, 1, 1);

    auto labCount = new QLabel("文件数", this);
    gridLayout->addWidget(labCount, 0, 0, 1, 1);

    auto labCode = new QLabel("代码行数", this);
    gridLayout->addWidget(labCode, 0, 2, 1, 1);

    auto labNote = new QLabel("注释行数", this);
    gridLayout->addWidget(labNote, 1, 2, 1, 1);

    auto labSize = new QLabel("字节数", this);
    gridLayout->addWidget(labSize, 1, 0, 1, 1);

    auto labRow = new QLabel("总行数", this);
    gridLayout->addWidget(labRow, 2, 0, 1, 1);


    auto btnOpenFile = new QPushButton("打开文件", this);
    gridLayout->addWidget(btnOpenFile, 0, 7, 1, 1);

    auto btnOpenPath = new QPushButton("打开目录", this);
    gridLayout->addWidget(btnOpenPath, 1, 7, 1, 1);

    auto btnClear = new QPushButton("清空结果", this);
    gridLayout->addWidget(btnClear, 2, 7, 1, 1);


    gridLayout->setColumnStretch(6, 1);

    connect(btnOpenFile, &QPushButton::clicked, this, &CountCode::on_btnOpenFile_clicked);
    connect(btnOpenPath, &QPushButton::clicked, this, &CountCode::on_btnOpenPath_clicked);
    connect(btnClear, &QPushButton::clicked, this, &CountCode::on_btnClear_clicked);

    // QMetaObject::connectSlotsByName(this);
}

void CountCode::on_btnOpenFile_clicked()
{
    QString filter = QString("代码文件(%1)").arg(txtFilter->text().trimmed());
    QStringList files = QFileDialog::getOpenFileNames(this, "选择文件", "./", filter);
    if (files.size() > 0)
    {
        txtFile->setText(files.join("|"));
        countCode(files);
    }
}

void CountCode::on_btnOpenPath_clicked()
{
    QString path = QFileDialog::getExistingDirectory(this, "选择目录", "./",  QFileDialog::ShowDirsOnly | QFileDialog::DontResolveSymlinks);
    if (!path.isEmpty())
    {
        txtPath->setText(path);
        listFile.clear();
        countCode(path);
        countCode(listFile);
    }
}

void CountCode::on_btnClear_clicked()
{
    txtCount->setText("0");
    txtSize->setText("0");
    txtRow->setText("0");

    txtCode->setText("0");
    txtNote->setText("0");
    txtBlank->setText("0");

    labPercentCode->setText("0%");
    labPercentNote->setText("0%");
    labPercentBlank->setText("0%");
    tableWidget->setRowCount(0);
}

