#ifndef YAMLCONFIG_H
#define YAMLCONFIG_H

#include <QString>
#include <QStringList>
#include <QList>
#include <QFile>
#include <QTextStream>
#include <QDebug>
#include <yaml-cpp/yaml.h>
#include <type_traits>
#include <fstream>

class YamlConfig
{
protected:
    explicit YamlConfig(const QString &filePath);

public:
    template <typename T>
    T getValue(const QString &keyPath, const T &defaultValue = T());

    template <typename T>
    void setValue(const QString &keyPath, const T &value);

    template <typename T>
    QList<T> getArray(const QString &keyPath, const QList<T> &defaultValue = {});

    template <typename T>
    void setArray(const QString &keyPath, const QList<T> &values);

private:
    QString m_filePath;
    YAML::Node m_root;

    YAML::Node getNodeByPath(const QString &keyPath, bool createIfMissing = false);
    YAML::Node getNodeRecursive(YAML::Node &current, const QStringList &keys, int index, bool createIfMissing);

    void saveToFile();
};

// --- Implementation ---

inline YamlConfig::YamlConfig(const QString &filePath) : m_filePath(filePath)
{
    try
    {
        m_root = YAML::LoadFile(filePath.toStdString());
    }
    catch (...)
    {
        m_root = YAML::Node(YAML::NodeType::Map);
    }
}

inline YAML::Node YamlConfig::getNodeByPath(const QString &keyPath, bool createIfMissing)
{
    QStringList keys = keyPath.split(".");
    return getNodeRecursive(m_root, keys, 0, createIfMissing);
}

inline YAML::Node YamlConfig::getNodeRecursive(YAML::Node &current, const QStringList &keys, int index, bool createIfMissing)
{
    if (index >= keys.size())
        return current;

    if (!current.IsMap())
        return YAML::Node();  // 返回空节点

    const std::string key = keys[index].toStdString();

    if (!current[key])
    {
        if (createIfMissing && index < keys.size() - 1)
        {
            current[key] = YAML::Node(YAML::NodeType::Map); // 仅中间路径创建为 Map
        }
        else if (!createIfMissing)
        {
            return YAML::Node();  // 不存在并且不创建，直接返回空节点
        }
    }

    // 提前命名子节点，防止绑定临时值
    YAML::Node child = current[key];

    return getNodeRecursive(child, keys, index + 1, createIfMissing);
}


inline void YamlConfig::saveToFile()
{
    try
    {
        std::ofstream fout(m_filePath.toStdString());
        fout << m_root;
    }
    catch (...)
    {
        qWarning() << "Failed to save YAML file:" << m_filePath;
    }
}

// --- Templates ---

template <typename T>
T YamlConfig::getValue(const QString &keyPath, const T &defaultValue)
{
    YAML::Node node = getNodeByPath(keyPath);
    if (!node || node.IsNull())
        return defaultValue;

    try
    {
        if constexpr (std::is_same_v<T, QString>)
        {
            return QString::fromStdString(node.as<std::string>());
        }
        else
        {
            return node.as<T>();
        }
    }
    catch (...)
    {
        return defaultValue;
    }
}

template <typename T>
void YamlConfig::setValue(const QString &keyPath, const T &value)
{
    YAML::Node node = getNodeByPath(keyPath, true);

    if constexpr (std::is_same_v<T, QString>)
    {
        node = value.toStdString();
    }
    else
    {
        node = value;
    }

    saveToFile();
}

template <typename T>
QList<T> YamlConfig::getArray(const QString &keyPath, const QList<T> &defaultValue)
{
    YAML::Node node = getNodeByPath(keyPath);
    if (!node || !node.IsSequence())
        return defaultValue;

    QList<T> result;
    try
    {
        for (const auto &item : node)
        {
            if constexpr (std::is_same_v<T, QString>)
            {
                result.append(QString::fromStdString(item.as<std::string>()));
            }
            else
            {
                result.append(item.as<T>());
            }
        }
        return result;
    }
    catch (...)
    {
        return defaultValue;
    }
}

template <typename T>
void YamlConfig::setArray(const QString &keyPath, const QList<T> &values)
{
    YAML::Node node = getNodeByPath(keyPath, true);
    YAML::Node arrayNode = YAML::Node(YAML::NodeType::Sequence);

    for (const auto &v : values)
    {
        if constexpr (std::is_same_v<T, QString>)
        {
            arrayNode.push_back(v.toStdString());
        }
        else
        {
            arrayNode.push_back(v);
        }
    }

    node = arrayNode;
    saveToFile();
}

#endif // YAMLCONFIG_H
