#ifndef UIEQUIPWAVE_H
#define UIEQUIPWAVE_H

/******************************************************************************
  File Name     : uidata.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备查看的波形视图
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
#include <QTimer>
class PlotWidget;

class UiEquipWave : public QWidget
{
    Q_OBJECT
public:
    explicit UiEquipWave(QWidget *parent = nullptr);

private slots:
    void realtimeDataSlot();

protected:
    QTimer dataTimer;
    PlotWidget* customPlot;

signals:
};

#endif // UIEQUIPWAVE_H
