#include "paneltest.h"
#include "utils/cppex.h"
#include "wizarduitest.h"
#include <mainwindow.h>

#include <QListWidget>
#include <QMenu>
#include <QPushButton>
#include <QVBoxLayout>


PanelTest::PanelTest(QWidget *parent)
    : QWidget{parent}, m_curItem(nullptr)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    auto newBtn = new QPushButton("新建测试");
    // newBtn->setFlat(true);

    newBtn->setIcon(QIcon("./res/widget/icon/add.png"));

    mainLayout->addWidget(newBtn);

    m_testList = new QListWidget();
    mainLayout->addWidget(m_testList);

    connect(m_testList, &QListWidget::itemDoubleClicked, [this](QListWidgetItem *item)
    {
        auto id = item->data(Qt::UserRole).toString();

        MainWindow::instance->ActivateTestView(id, item->text());
    });


    connect(newBtn, &QPushButton::clicked, [this]()
    {
        MainWindow::instance->newTest();
    });


    // 右键菜单
    m_testList->setContextMenuPolicy(Qt::CustomContextMenu);

    m_contextMenu = new QMenu;
    auto actShow = new QAction("显示测试窗口", this);
    auto actHide = new QAction("隐藏测试窗口", this);
    auto actInfo = new QAction("详细信息", this);
    auto actDel = new QAction("删除", this);

    m_contextMenu->addAction(actShow);
    m_contextMenu->addAction(actHide);
    m_contextMenu->addAction(actInfo);
    m_contextMenu->addAction(actDel);

    connect(m_testList, &QListWidget::customContextMenuRequested, [=](const QPoint &pos)
    {
        QListWidgetItem *item = m_testList->itemAt(pos);
        if(item == nullptr)
            return;

        m_curItem = item;
        m_contextMenu->exec(QCursor::pos());
    });

    connect(actShow, &QAction::triggered,[=]
    {
        auto id = m_curItem->data(Qt::UserRole).toString();
        MainWindow::instance->ActivateTestView(id, m_curItem->text());
    });

    connect(actHide, &QAction::triggered,[=]
    {
        auto id = m_curItem->data(Qt::UserRole).toString();
        MainWindow::instance->DeactivateTestView(id, false);
    });

    connect(actInfo, &QAction::triggered,[=]
    {
        auto id = m_curItem->data(Qt::UserRole).toString();
    });

    connect(actDel, &QAction::triggered,[=]
    {
        auto id = m_curItem->data(Qt::UserRole).toString();
        MainWindow::instance->DeleteTest(id);
        delete m_curItem;
    });
}

void PanelTest::clearAll()
{
    m_testList->clear();
}

void PanelTest::addTest(const QString &id, int type, const QString &name)
{
    QListWidgetItem* item = new QListWidgetItem(name);
    item->setData(Qt::UserRole, id);
    m_testList->addItem(item);
}
