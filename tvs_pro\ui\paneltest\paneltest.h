#ifndef PANELTEST_H
#define PANELTEST_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 左侧测试管理面板
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
class QListWidget;
class QListWidgetItem;


class PanelTest : public QWidget
{
    Q_OBJECT
public:
    explicit PanelTest(QWidget *parent = nullptr);
    void clearAll();
    void addTest(const QString &id, int type, const QString &name);

protected:
    QListWidget * m_testList;
    QMenu * m_contextMenu;
    QListWidgetItem *m_curItem;

signals:
};

#endif // PANELTEST_H
