#ifndef BASE_TYPES_H
#define BASE_TYPES_H

#include "plot_global.h"
#include "core/OriResult.h"

#include <QVector>

using Values = QVector<double>;

struct PLOT_EXPORT GraphPoints
{
    Values xs;
    Values ys;

    int size() const
    {
        return xs.size();
    }
};

using GraphResult = Ori::Result<GraphPoints>;

struct PLOT_EXPORT CsvGraphParams
{
    QString title;
    QString valueSeparators;
    bool decimalPoint;
    int columnX, columnY;
    int skipFirstLines;
};

struct PLOT_EXPORT PlottingRange
{
    double start;
    double stop;
    double step;
    int points = 100;
    bool useStep = false;

    QVector<double> calcValues() const;
    QString verify() const;
};

struct PLOT_EXPORT MinMax
{
    double min;
    double max;
};

struct PLOT_EXPORT RandomSampleParams
{
    PlottingRange rangeX;
    MinMax rangeY;
};

#define MSG_PLOT_RENAMED 1
#define MSG_GRAPH_RENAMED 2
#define MSG_GRAPH_DELETED 3
#define MSG_AXIS_FACTOR_CHANGED 4
#define MSG_PLOT_DELETED 5

#endif // BASE_TYPES_H
