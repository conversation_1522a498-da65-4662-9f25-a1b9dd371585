#ifndef UIMONITOR_ELEC_H
#define UIMONITOR_ELEC_H

/******************************************************************************
  File Name     : uidata.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备查看主视图
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
#include <QTimer>
#include <QElapsedTimer>

class UiWave;
class UiDataTable;
class UiMonitorStatus;
class QLabel;
class Graph;

class UiMonitorElec : public QWidget
{
    Q_OBJECT
public:
    explicit UiMonitorElec(const QString& id, QWidget *parent = nullptr);

    bool isUiDataVisiable() const;
    void setUiDataVisiable(bool visible);

protected:
    QString m_id;
    UiWave * m_uiWaveVoltage;
    UiWave * m_uiWaveVoltageSpectrum;
    
    UiWave * m_uiWaveCurrent;
    UiWave * m_uiWaveCurrentSpectrum;

    UiWave * m_uiWaveFieldCurrent;
    UiMonitorStatus * m_uiMonitorStatus;
    QLabel* m_info;

    Graph * m_graph_voltage_a;
    Graph * m_graph_voltage_b;
    Graph * m_graph_voltage_c;
    Graph * m_graph_voltage_spectrum_a;
    Graph * m_graph_voltage_spectrum_b;
    Graph * m_graph_voltage_spectrum_c;

    Graph * m_graph_current_a;
    Graph * m_graph_current_b;
    Graph * m_graph_current_c;
    Graph * m_graph_current_spectrum_a;
    Graph * m_graph_current_spectrum_b;
    Graph * m_graph_current_spectrum_c;

    Graph * m_graph_field_current;

    QTimer dataTimer;

    // 数据生成相关的成员变量（避免静态变量共享问题）
    QElapsedTimer m_dataTimer;
    bool m_firstRun;
    double m_lastPointKey;


private slots:
    void timeToAddData();

signals:

    // QWidget interface
protected:
    virtual void resizeEvent(QResizeEvent *event) override;
};

#endif // UIMONITOR_ELEC_H
