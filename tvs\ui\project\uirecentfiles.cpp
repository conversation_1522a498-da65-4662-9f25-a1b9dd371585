#include "uirecentfiles.h"
#include "widgets/stdwidget.h"
#include "widgets/uirichlist.h"

#include <MainWindow.h>
#include <QFileInfo>
#include <QMessageBox>
#include <QVBoxLayout>

UiRecentFiles::UiRecentFiles(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0,0,0,0);

    auto *fileLabel = new LabelH3("最近打开的工程");
    mainLayout->addWidget(fileLabel);

    auto uiList = new UiRichList();
    mainLayout->addWidget(uiList);
    mainLayout->addStretch(1);

    // 初始化
    foreach (auto filePath, MainWindow::instance->m_recent_files)
    {
        QFileInfo fileInfo(filePath);
        QString fileName = fileInfo.fileName();
        uiList->addItem(fileName, filePath, "./res/icon/Open.png");
    }

    // 点击事件
    connect(uiList, &UiRichList::item_clicked, [](QString title, QString info)
    {
        MainWindow::instance->loadDoc(info);
    });

    // 列表变化信号响应
    connect(MainWindow::instance, &MainWindow::recentFilesChanged, [uiList](QStringList files)
    {
        uiList->clearAll();

        foreach (auto filePath, files)
        {
            QFileInfo fileInfo(filePath);
            QString fileName = fileInfo.fileName();
            uiList->addItem(fileName, filePath, "./res/icon/Open.png");
        }
    });
}
