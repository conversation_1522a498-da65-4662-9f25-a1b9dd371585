#include "actionmanager.h"
#include <QDebug>


ActionManager::ActionManager()
{
}


void ActionManager::registerCommand(const QString& cmd, std::function<void(bool)> function)
{
    m_commands[cmd] = function;
}



void ActionManager::executeCommand(const QString& cmd, bool checked)
{
    if (m_commands.contains(cmd))
    {
        m_commands[cmd](checked);
    }
    else
    {
        qDebug() << "Command not found: " << cmd;
    }
}

QList<QString> ActionManager::allCmds() const
{
    return m_commands.keys();
}




