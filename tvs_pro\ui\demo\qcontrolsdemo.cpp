#include "qcontrolsdemo.h"

#include <QFileDialog>
#include <QSizePolicy>

#include <QGroupBox>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QTimeEdit>
#include <QDateEdit>
#include <QDateTimeEdit>
#include <QTextEdit>
#include <QSlider>
#include <QDial>
#include <QProgressBar>
#include <QLCDNumber>
#include <QRadioButton>
#include <QCheckBox>
#include <QListView>
#include <QSlider>


QControlsDemo::QControlsDemo(QWidget *parent) : QWidget(parent),
    layout_(new QGridLayout)
{
    {
        auto label1 = new QLabel(tr("Label"));
        auto label2 = new QLabel(tr("Disabled"));
        label2->setEnabled(false);

        auto grid = new QGridLayout;
        grid->addWidget(label1, 0, 0);
        grid->addWidget(label2, 0, 1);


        auto groupbox = new QGroupBox(tr("QLabel"));
        groupbox->setLayout(grid);

        layout_->addWidget(groupbox, 1, 0);
    }

    {
        auto button1 = new QPushButton(tr("Button"));
        auto button2 = new QPushButton(tr("Disabled"));
        button2->setEnabled(false);


        auto grid = new QGridLayout;
        grid->addWidget(button1, 0, 0);
        grid->addWidget(button2, 1, 0);

        auto groupbox = new QGroupBox(tr("QButton"));
        groupbox->setLayout(grid);

        layout_->addWidget(groupbox, 1, 1);
    }

    {
        auto line1 = new QLineEdit(tr("Line Edit"));
        auto line2 = new QLineEdit(tr("Read Only"));
        line2->setReadOnly(true);
        auto line3 = new QLineEdit(tr("Disabled"));
        line3->setEnabled(false);

        auto grid = new QGridLayout;
        grid->addWidget(line1, 0, 0);
        grid->addWidget(line2, 1, 0);
        grid->addWidget(line3, 2, 0);

        auto groupbox = new QGroupBox(tr("QLineEdit"));
        groupbox->setLayout(grid);

        layout_->addWidget(groupbox, 2, 0, 1, 2);
    }

    {
        auto progress1 = new QProgressBar;
        progress1->setValue(66);
        auto progress2 = new QProgressBar;
        progress2->setValue(66);
        progress2->setEnabled(false);

        auto grid = new QGridLayout;
        grid->addWidget(progress1, 0, 0);
        grid->addWidget(progress2, 0, 1);

        auto groupbox = new QGroupBox(tr("QProgressBar"));
        groupbox->setLayout(grid);

        layout_->addWidget(groupbox, 3, 0, 1, 2);
    }

    {
        auto radio1 = new QRadioButton(tr("Radio Button"));
        auto radio2 = new QRadioButton(tr("Disabled"));
        radio2->setEnabled(false);

        auto grid = new QGridLayout;
        grid->addWidget(radio1, 0, 0);
        grid->addWidget(radio2, 0, 1);


        auto groupbox = new QGroupBox(tr("QRadioButton"));
        groupbox->setLayout(grid);

        layout_->addWidget(groupbox, 4, 0);
    }

    {
        auto check1 = new QCheckBox(tr("Check Box"));
        auto check2 = new QCheckBox(tr("Disabled"));
        check2->setEnabled(false);

        auto grid = new QGridLayout;
        grid->addWidget(check1, 0, 0);
        grid->addWidget(check2, 0, 1);

        auto groupbox = new QGroupBox(tr("QCheckBox"));
        groupbox->setLayout(grid);

        layout_->addWidget(groupbox, 5, 0);
    }

    {
        auto slider1 = new QSlider(Qt::Horizontal);
        slider1->setValue(66);
        auto slider2 = new QSlider(Qt::Horizontal);
        slider2->setValue(66);
        slider2->setEnabled(false);

        auto slider7 = new QSlider(Qt::Vertical);
        slider7->setValue(66);
        auto slider8 = new QSlider(Qt::Vertical);
        slider8->setValue(66);
        slider8->setEnabled(false);



        auto grid = new QGridLayout;
        grid->addWidget(slider1, 0, 0);
        grid->addWidget(slider2, 1, 0);


        grid->addWidget(slider7, 0, 1, 6, 1);
        grid->addWidget(slider8, 0, 2, 6, 1);


        auto groupbox = new QGroupBox(tr("QSlider"));
        groupbox->setLayout(grid);

        layout_->addWidget(groupbox, 4, 1, 2, 1);
    }

    this->setLayout(layout_);
}
