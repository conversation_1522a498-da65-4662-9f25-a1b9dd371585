#ifndef PANELEQUIPMENT_H
#define PANELEQUIPMENT_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 左侧设备管理面板
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/


#include <QWidget>

class UiEquipmentList;

class PanelEquipment : public QWidget
{
    Q_OBJECT
public:
    explicit PanelEquipment(QWidget *parent = nullptr);

    void clearAll();
    void addEquipment(const QString &id, int type, const QString &name, const QString &ip);

protected:
    UiEquipmentList * m_equipList;

signals:
};

#endif // PANELEQUIPMENT_H
