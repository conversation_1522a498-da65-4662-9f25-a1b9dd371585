#include "wizardpagetest.h"
#include "wizardpagetestparam.h"
#include "wizarduitest.h"


WizardUiTest::WizardUiTest(QWidget *parent)
    : QWizard(parent)
{
    setWindowTitle( "测试-向导页面");

    // 添加页面
    WizardPageTest *page1 = new WizardPageTest();
    addPage(page1);

    WizardPageTestParam *page2 = new WizardPageTestParam;
    addPage(page2);

    //去掉帮助按钮
    setWindowFlags(windowFlags()&~Qt::WindowContextHelpButtonHint);

    //设置导航样式
    setWizardStyle( QWizard::ModernStyle );

    // 隐藏取消按钮
    setOptions(QWizard::NoCancelButton);

    int nWidth = 590;
    int nHeight = 500;
    if (parent != nullptr)
    {
        setGeometry(parent->x() + parent->width()/2 - nWidth/2,
                    parent->y() + parent->height()/2 - nHeight/2,
                    nWidth, nHeight);
    }
    else
    {
        resize(nWidth, nHeight);
    }
}
