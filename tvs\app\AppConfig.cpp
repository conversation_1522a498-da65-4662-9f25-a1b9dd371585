#include "AppConfig.h"
#include <QFile>
#include <QDebug>
#include <QFileInfo>
#include <QStandardPaths>
#include <yaml-cpp/yaml.h>

AppConfig::AppConfig()
{
    // Try to load config, but don't fail if it doesn't exist
    loadConfig();
}

QString AppConfig::getDefaultConfigPath() const
{
    // 使用相对路径
    QString configPath = "./config/app_config.yaml";
    QFileInfo configFile(configPath);
    
    // 确保路径是绝对路径
    QString absolutePath = configFile.absoluteFilePath();
    
    // 确保config目录存在
    QDir configDir = configFile.dir();
    if (!configDir.exists()) {
        if (!QDir().mkpath(configDir.absolutePath())) {
            qWarning() << "Failed to create config directory:" << configDir.absolutePath();
            return QString();
        }
    }
    
    return QDir::cleanPath(absolutePath);
}

bool AppConfig::loadConfig(const QString &configPath)
{
    QString configFile = configPath;
    
    // If no path provided, use the default
    if (configFile.isEmpty()) {
        configFile = getDefaultConfigPath();
    }
    
    // Check if config file exists
    QFileInfo configFileInfo(configFile);
    if (!configFileInfo.exists()) {
        qWarning() << "Configuration file not found:" << configFile;
        return false;
    }
    
    try {
        // Load and parse the YAML file
        YAML::Node config = YAML::LoadFile(configFile.toStdString());
        
        // Load basic application info
        if (config["app"]) {
            auto appNode = config["app"];
            
            // Only update values that exist in the config file
            if (appNode["name"]) {
                m_appName = QString::fromStdString(appNode["name"].as<std::string>());
            }
            
            if (appNode["company"]) {
                m_companyName = QString::fromStdString(appNode["company"].as<std::string>());
            }
            
            if (appNode["window_title"]) {
                m_windowTitle = QString::fromStdString(appNode["window_title"].as<std::string>());
            }
            
            qDebug() << "Loaded configuration from:" << configFile;
            return true;
        } else {
            qWarning() << "Invalid configuration format in file:" << configFile;
            return false;
        }
    } catch (const YAML::Exception &e) {
        qWarning() << "Failed to parse config file:" << configFile;
        qWarning() << "YAML Error:" << e.what();
        return false;
    } catch (const std::exception &e) {
        qWarning() << "Error loading config file:" << configFile;
        qWarning() << "Error:" << e.what();
        return false;
    }
}
