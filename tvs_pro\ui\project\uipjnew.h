#ifndef UIPJNEW_H
#define UIPJNEW_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 工程新建页
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
#include <QPushButton>

class QGridLayout;


class WelcomeIconButton : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};

class UiPjNewGrid : public QWidget
{
    Q_OBJECT
public:
    explicit UiPjNewGrid(QWidget *parent = nullptr);

protected:
    QGridLayout *m_gridLayout;

    void setLabel(int row, int column, const QString &text);
    void setButton(int row, int column, const QString &text);
    QPushButton* setIconButton(int row, int column, const QString &text, const QString &fileIcon);

signals:
};


class UiPjNew : public QWidget
{
    Q_OBJECT
public:
    explicit UiPjNew(QWidget *parent = nullptr);
};



#endif // UIPJNEW_H
