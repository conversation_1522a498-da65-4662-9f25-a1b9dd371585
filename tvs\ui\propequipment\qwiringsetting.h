#ifndef QWIRINGSETTING_H
#define QWIRINGSETTING_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备属性-接线配置
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>


class SwitchButton;
class QPushButton;

class QWiringSetting : public QWidget
{
    Q_OBJECT
public:
    explicit QWiringSetting(QWidget *parent = nullptr);

private:
    QPushButton *m_btn1;
    QPushButton *m_btn2;
    QPushButton *m_btn3;
    QPushButton *m_btn4;
    QPushButton *m_btn5;
    QPushButton *m_btn6;
    QPushButton *m_btn7;
    SwitchButton *m_swichIsAloneSetting;

signals:
};

#endif // QWIRINGSETTING_H
