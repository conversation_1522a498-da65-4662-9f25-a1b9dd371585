#ifndef UIMONITORSTATUS_H
#define UIMONITORSTATUS_H

/******************************************************************************
  File Name     : uidata.h
  Version       : 1.0
  Author        : 
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备查看的底部状态栏
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QFrame>
#include <QWidget>

class QHBoxLayout;

class UiMonitorStatus : public QFrame
{
    Q_OBJECT
public:
    explicit UiMonitorStatus(QWidget *parent = nullptr);
    QHBoxLayout *m_layout;

public:
    // void Add
signals:
};

#endif // UIMONITORSTATUS_H
