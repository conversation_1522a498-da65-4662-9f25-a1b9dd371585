cmake_minimum_required(VERSION 3.5)

project(DEMO_CODE VERSION 0.1 LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_INCLUDE_CURRENT_DIR ON)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Core Widgets)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Core Widgets)

# 方式1
get_filename_component(PROJECT_RESOURCES_DIR "${CMAKE_CURRENT_SOURCE_DIR}" DIRECTORY)

# 方式2
# set(PROJECT_RESOURCES_DIR ${CMAKE_SOURCE_DIR})

message(STATUS "PROJECT_RESOURCES_DIR: ${PROJECT_RESOURCES_DIR}")

include_directories(${Qt6Widgets_INCLUDE_DIRS})

file(GLOB_RECURSE MAIN_SOURCES
    "${PROJECT_RESOURCES_DIR}/tvs/widgets/codewidget/*.cpp"
    "${PROJECT_RESOURCES_DIR}/tvs/widgets/codewidget/*.h"
)

# 合并所有源文件
set(PROJECT_SOURCES
    ${MAIN_SOURCES}
    ${QCUSTOMPLOT_SOURCES}
    main.cpp
    mainwindow.cpp
    mainwindow.h
)

if(${QT_VERSION_MAJOR} GREATER_EQUAL 6)
    qt_add_executable(DEMO_CODE
        MANUAL_FINALIZATION
        ${PROJECT_SOURCES}
    )

    # Define target properties for Android with Qt 6 as:
    # set_property(TARGET DEMO_CODE APPEND PROPERTY QT_ANDROID_PACKAGE_SOURCE_DIR
    # ${CMAKE_CURRENT_SOURCE_DIR}/android)
    # For more information, see https://doc.qt.io/qt-6/qt-add-executable.html#target-creation
else()
    if(ANDROID)
        add_library(DEMO_CODE SHARED
            ${PROJECT_SOURCES}
        )

    # Define properties for Android with Qt 5 after find_package() calls as:
    # set(ANDROID_PACKAGE_SOURCE_DIR "${CMAKE_CURRENT_SOURCE_DIR}/android")
    else()
        add_executable(DEMO_CODE
            ${PROJECT_SOURCES}
        )
    endif()
endif()

target_compile_options(DEMO_CODE PRIVATE -Wa,-mbig-obj)

target_include_directories(DEMO_CODE PRIVATE
    ${PROJECT_RESOURCES_DIR}
    ${PROJECT_RESOURCES_DIR}/3rd/scintilla/include
)

target_link_libraries(DEMO_CODE PRIVATE
    Qt6::Core
    Qt6::Widgets
)

set(SCINTILLA_LIB_DIR ${PROJECT_RESOURCES_DIR}/3rd/scintilla/lib)

# Windows 平台特定配置
if(WIN32)
    # 根据构建类型设置库后缀
    if(CMAKE_BUILD_TYPE STREQUAL "Debug")
        set(LIB_DEBUG_SUFFIX d)
    else()
        set(LIB_DEBUG_SUFFIX "")
    endif()

    # 查找 SCINTILLA 库
    find_library(SCINTILLA_LIB
        NAMES qscintilla2_qt6${LIB_DEBUG_SUFFIX}
        PATHS ${SCINTILLA_LIB_DIR}
        NO_DEFAULT_PATH
    )

    if(SCINTILLA_LIB)
        target_link_libraries(DEMO_CODE PRIVATE ${SCINTILLA_LIB})
        message(STATUS "Found SCINTILLA library: ${SCINTILLA_LIB}")
    else()
        message(WARNING "Could NOT find SCINTILLA library")
    endif()

endif()

# Qt for iOS sets MACOSX_BUNDLE_GUI_IDENTIFIER automatically since Qt 6.1.
# If you are developing for iOS or macOS you should consider setting an
# explicit, fixed bundle identifier manually though.
if(${QT_VERSION} VERSION_LESS 6.1.0)
    set(BUNDLE_ID_OPTION MACOSX_BUNDLE_GUI_IDENTIFIER com.example.DEMO_CODE)
endif()

set_target_properties(DEMO_CODE PROPERTIES
    ${BUNDLE_ID_OPTION}
    MACOSX_BUNDLE_BUNDLE_VERSION ${PROJECT_VERSION}
    MACOSX_BUNDLE_SHORT_VERSION_STRING ${PROJECT_VERSION_MAJOR}.${PROJECT_VERSION_MINOR}
    MACOSX_BUNDLE TRUE
    WIN32_EXECUTABLE TRUE
)

include(GNUInstallDirs)
install(TARGETS DEMO_CODE
    BUNDLE DESTINATION .
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)

if(QT_VERSION_MAJOR EQUAL 6)
    qt_finalize_executable(DEMO_CODE)
endif()
