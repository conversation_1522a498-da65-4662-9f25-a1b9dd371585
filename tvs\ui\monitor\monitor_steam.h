#ifndef UIMONITOR_STEAM_H
#define UIMONITOR_STEAM_H

/******************************************************************************
  File Name     : uidata.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备查看主视图
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
#include <QTimer>
#include <QElapsedTimer>

class UiWave;
class UiDataTable;
class UiMonitorStatus;
class QLabel;
class Graph;

// 点位信息卡片，显示测点名称、编码、当前值
class UiPointCard : public QWidget
{
    Q_OBJECT
public:
    explicit UiPointCard(const QString& name, const QString& code, QWidget *parent = nullptr);

    void updateValue(double value);

private:
    QLabel* m_name;
    QLabel* m_code;
    QLabel* m_value;
};

class UiMonitorSteam : public QWidget
{
    Q_OBJECT
public:
    explicit UiMonitorSteam(const QString& id, QWidget *parent = nullptr);

    bool isUiDataVisiable() const;
    void setUiDataVisiable(bool visible);

protected:
    QString m_id;
    UiWave * m_uiWaveAngularSpeed;
    UiWave * m_uiWaveAngularSpectrum;
    
    UiWave * m_uiWaveSpeed;
    UiWave * m_uiWaveSpectrum;

    UiWave * m_uiWavePulse;
    UiMonitorStatus * m_uiMonitorStatus;
    QLabel* m_info;

    Graph * m_graph_rad;
    Graph * m_graph_spectrum;
    Graph * m_graph_pulse;

    QTimer dataTimer;

    // 数据生成相关的成员变量（避免静态变量共享问题）
    QElapsedTimer m_dataTimer;
    bool m_firstRun;
    double m_lastPointKey;


private slots:
    void timeToAddData();

signals:

    // QWidget interface
protected:
    virtual void resizeEvent(QResizeEvent *event) override;
};

#endif // UIMONITOR_STEAM_H
