#ifndef TOOLPAGE_H
#define TOOLPAGE_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 支持多个同时展开的工具栏 - 页面组件
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include "qlabel.h"
#include "qpushbutton.h"
#include <QWidget>

class QFormLayout;
class QLabel;
class QVBoxLayout;
class QPushButton;

class ToolBoxButton : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};


class ToolBoxIcon : public QLabel
{
    Q_OBJECT
public:
    using QLabel::QLabel; // 继承父类的构造函数
};

class ToolBoxContent : public QWidget
{
    Q_OBJECT
public:
    using QWidget::QWidget; // 继承父类的构造函数
};


class ToolPage : public QWidget
{
    Q_OBJECT

public:
    explicit ToolPage(QWidget *parent = nullptr);

public slots:
    void addWidget(const QString &title, QWidget *widget);
    void expand();
    void collapse();

private slots:
    void onPushButtonFoldClicked();

private:
    bool m_expanded;
    ToolBoxButton *m_button;
    ToolBoxIcon *m_label;
    ToolBoxContent * m_content;
    QVBoxLayout *m_layout;
};

#endif // TOOLPAGE_H
