#ifndef QCOMMONSETTING_H
#define QCOMMONSETTING_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备属性-常规设置
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>

#include <QLabel>
#include <QPushButton>
#include <QComboBox>
#include <QGridLayout>
#include <QLineEdit>

//常规设置
class QCommonSetting : public QWidget
{
    Q_OBJECT
public:
    explicit QCommonSetting(QWidget *parent = nullptr);

private:
    //配置单元
    QComboBox *m_combSettingCellCom;
    //同步源
    QComboBox *m_combSynchSorce;
    //频率滤波器
    //频率滤波器电压
    QComboBox *m_combFreqfilterVoltage;
    //频率滤波器电流
    QComboBox *m_combFreqfilterElectric;
    //线路滤波器电压
    QComboBox *m_combLinefilterVoltage;
    //线路滤波器电流
    QComboBox *m_combLinefilterElectric;

signals:
};

#endif // QCOMMONSETTING_H
