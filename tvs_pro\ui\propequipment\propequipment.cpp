
#include "qadvancedsetting.h"
#include "qcommonsetting.h"
#include "qrangesetting.h"
#include "qwiringsetting.h"
#include "propequipment.h"
#include "qequipmentsetting.h"
#include "widgets/toolbox.h"

#include <app/settings.h>


PropEquipment::PropEquipment(QWidget *parent)
    : QWidget(parent)
{
    setAttribute(Qt::WA_StyledBackground);
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0,0,0,0);

    // QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp.margins");
    // if(margins.size() == 4)
    // {
    //     mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    // }

    // auto spacing = YamlConfigUI::instance().getValue<int>("ui_porp.spacing");
    // mainLayout->setSpacing(spacing);

    toolBox = new ToolBox();
    mainLayout->addWidget(toolBox);

    m_widget_devicesetting = new QEquipmentSetting;
    m_widget_writingsetting = new QWiringSetting;
    m_widget_rangesetting = new QRangeSetting;
    m_widget_commonsetting = new QCommonSetting;
    m_widget_advancedsetting = new QAdvancedSetting;

    toolBox->addItem( m_widget_devicesetting,QStringLiteral("设备设置"));
    toolBox->addItem( m_widget_writingsetting,QStringLiteral("接线设置"));
    toolBox->addItem( m_widget_rangesetting,QStringLiteral("量程配置"));
    toolBox->addItem( m_widget_commonsetting,QStringLiteral("常规设置"));
    toolBox->addItem( m_widget_advancedsetting,QStringLiteral("高级设置"));
}
