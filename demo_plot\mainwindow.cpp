#include "mainwindow.h"

#include <QBoxLayout>
#include <QElapsedTimer>
#include <QRandomGenerator>
#include "plot_widget.h"
#include "qcpl/qcpl_graph_grid.h"

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
{
    // 创建控件
    dataGrid = new QCPL::GraphDataGrid;
    plot = new PlotWidget(this);
    plot->getPlot()->setShowTracers(false);

    // 创建分隔条
    QSplitter *splitter = new QSplitter(Qt::Horizontal, this);
    splitter->addWidget(dataGrid);
    splitter->addWidget(plot);
    splitter->setStretchFactor(0, 1); // 左侧权重
    splitter->setStretchFactor(1, 3); // 右侧权重

    connect(plot, &PlotWidget::graphSelected, this, &MainWindow::graphSelected);

    // 设置为中央部件
    setCentralWidget(splitter);

    // setCentralWidget(plot);
    // plot->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Expanding);

    // auto layout = new QVBoxLayout(this);
    // layout->setContentsMargins(0, 0, 0, 0);
    // layout->setSpacing(0);
    // layout->addWidget(plot);
}

MainWindow::~MainWindow()
{

    // auto dataSource = new DaqDataSource();
    // graph = new Graph(dataSource);
    // graph->refreshData(true);
    // addGraph(graph);

    // connect(&dataTimer, SIGNAL(timeout()), this, SLOT(timeToAddData()));
    // dataTimer.start(1000);
}

void MainWindow::graphSelected(Graph *)
{
    updateDataGrid();
}

void MainWindow::updateDataGrid()
{
    if (auto graph = plot->selectedGraph(false); graph)
        showData(graph);
}

void MainWindow::timeToAddData()
{
    static QElapsedTimer timer; // 替换 QTime
    static bool firstRun = true;

    if (firstRun)
    {
        timer.start(); // 第一次运行时启动计时器
        firstRun = false;
    }

    double key = timer.elapsed() / 1000.0; // 开始到现在的时间，单位秒
    static double lastPointKey = 0;

    double fsample = 1000;           // 采样率，单位Hz
    double interval = 1.0 / fsample; // 采样间隔，单位秒

    int n = std::ceil((key - lastPointKey) / interval);
    if (n == 0)
        return;

    QVector<double> keys, values;
    for (int i = 0; i < n; ++i)
    {
        keys.append(lastPointKey + i * interval);
        values.append(qSin(lastPointKey + i * 30 * interval) + QRandomGenerator::global()->generateDouble() * 1);
    }

    // 添加数据到graph
    // customPlot->graph(0)->addData(key, qSin(key)+QRandomGenerator::global()->generateDouble()*1*qSin(key/0.3843));
    // customPlot->graph(1)->addData(key, qCos(key)+QRandomGenerator::global()->generateDouble()*0.5*qSin(key/0.4364));

    // 记录当前时刻
    lastPointKey = key;

    // addGraphData(m_graph, keys, values, true, 10000);
}

void MainWindow::showData(Graph *graph)
{
    if (graph)
    {
        // _graphId = graph->id();
        // _iconGraph->setPixmap(graph->icon().pixmap(16, 16));
        // _titleGraph->setText(graph->title());
        dataGrid->setData(graph->data().xs, graph->data().ys);
    }
}

// void DataGridPanel::copyData()
// {
//     _dataGrid->copy();
// }

// bool DataGridPanel::hasFocus() const
// {
//     return _dataGrid->hasFocus();
// }
