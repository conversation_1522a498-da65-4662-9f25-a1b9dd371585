#include "panelequipment.h"
#include "ui/panelequipment/uiequipmentlist.h"
#include "ui/panelequipment/wizardeqpment.h"
#include "utils/cppex.h"


#include <MainWindow.h>
#include <QLabel>
#include <QMessageBox>
#include <QPushButton>
#include <QVBoxLayout>
#include <QWizard>

PanelEquipment::PanelEquipment(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    auto newBtn = new QPushButton("新建设备");
    newBtn->setIcon(QIcon("./res/widget/icon/add.png"));

    mainLayout->addWidget(newBtn);

    m_equipList = new UiEquipmentList();
    mainLayout->addWidget(m_equipList);

    connect(newBtn, &QPushButton::clicked, [this]()
    {
        MainWindow::instance->newEquipment();
    });
}

void PanelEquipment::clearAll()
{
    m_equipList->clear();
}

void PanelEquipment::addEquipment(const QString &id, int type, const QString &name, const QString &ip)
{
    QStringList equipmentList =  {"8730C", "8930H", "8962A1", "device1", "device2", "device3", "device4"};
    m_equipList->AddEquipment(name+"-"+ip, QString("./res/equipment/%1.png").arg(equipmentList[type]), id);
}


