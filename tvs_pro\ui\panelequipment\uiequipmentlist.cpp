#include "uiequipmentlist.h"
#include "widgets/stdwidget.h"
#include <QVBoxLayout>
#include <mainwindow.h>
#include "utils/MessageHelper.h"
#include <utils/actionmanager.h>

UiEquipmentListItemBar::UiEquipmentListItemBar(const QString &id, QWidget *parent)
    : QWidget{parent}, m_id(id)
{
    auto layout = new QHBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    auto btnConnect = new QPushButton();
    btnConnect->setObjectName("Connect");
    btnConnect->setCheckable(true);

    auto btnDelete = new QPushButton();
    btnDelete->setObjectName("Delete");

    connect(btnConnect, &QPushButton::clicked, [=](bool checked)
            { emit equipConnect(m_id, checked); });

    connect(btnDelete, &QPushButton::clicked, [=]()
            { emit equipDelete(m_id); });

    connect(MainWindow::instance, &MainWindow::equipmentConnectChanged, [=](QString id, bool bconnect)
            {
        if(m_id == id)
        {
            btnConnect->setChecked(bconnect);
        } });

    layout->addWidget(btnConnect);
    layout->addWidget(btnDelete);
    layout->addStretch(1);
}

UiEquipmentListItemRect::UiEquipmentListItemRect(const QString &title, const QString &icon, const QString &id, QWidget *parent)
    : QWidget{parent}
{
    setAttribute(Qt::WA_StyledBackground);
    auto layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    m_toolbar = new UiEquipmentListItemBar(id);
    layout->addWidget(m_toolbar);

    auto pic = new LabelIcon();
    pic->setPixmap(QPixmap(icon));
    pic->setScaledContents(true);
    pic->setAlignment(Qt::AlignCenter);

    layout->addWidget(pic, 1);

    auto wtitle = new LabelTitle(title);
    wtitle->setAlignment(Qt::AlignCenter);

    layout->addWidget(wtitle);
}

UiEquipmentListItem::UiEquipmentListItem(const QString &title, const QString &icon, const QString &id, QWidget *parent)
    : QWidget{parent}, m_id(id), m_title(title)
{
    setAttribute(Qt::WA_StyledBackground);
    setSizePolicy(QSizePolicy::MinimumExpanding, QSizePolicy::MinimumExpanding);

    auto layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    auto rect = new UiEquipmentListItemRect(title, icon, id, parent);
    // rect->setFixedSize(238, 180);
    layout->addWidget(rect, 0, Qt::AlignCenter);

    UiEquipmentList *uiList = qobject_cast<UiEquipmentList *>(parent);
    if (uiList)
    {
        connect(rect->m_toolbar, &UiEquipmentListItemBar::equipConnect, uiList, &UiEquipmentList::connectEquip);
        connect(rect->m_toolbar, &UiEquipmentListItemBar::equipDelete, uiList, &UiEquipmentList::deleteEquip);
    }
}

UiEquipmentList::UiEquipmentList(QWidget *parent)
    : QListWidget{parent}
{
    setSpacing(0);

    QObject::connect(this, &QListWidget::itemDoubleClicked, [this](QListWidgetItem *item)
                     {
        if (UiEquipmentListItem *equip = qobject_cast<UiEquipmentListItem*>(itemWidget(item)))
        {
            MainWindow::instance->ActivateEquipmentView(equip->m_id, equip->m_title);
        } });

    // 测试数据
    // for (int i = 0; i < 8; ++i)
    // {
    //     UiEquipmentListItem* pItemWidget = new UiEquipmentListItem(this);
    //     QListWidgetItem* pItem = new QListWidgetItem("");

    //     // IconMode
    //     // pItem->setSizeHint(QSize(238, 180));

    //     addItem(pItem);
    //     setItemWidget(pItem, pItemWidget);
    // }

    // IconMode
    // setViewMode(QListView::IconMode);
    // setResizeMode(QListView::Adjust);
    // setFlow(QListView::TopToBottom);
    // setWrapping(false);
    // setMovement(QListView::Static);
    // setItemAlignment(Qt::AlignCenter);
}

void UiEquipmentList::AddEquipment(const QString &title, const QString &icon, const QString &id)
{
    UiEquipmentListItem *pItemWidget = new UiEquipmentListItem(title, icon, id, this);
    QListWidgetItem *pItem = new QListWidgetItem("");
    addItem(pItem);
    setItemWidget(pItem, pItemWidget);
}

void UiEquipmentList::resizeEvent(QResizeEvent *event)
{
    QListWidget::resizeEvent(event);
    for (int i = 0; i < count(); ++i)
    {
        auto listitem = item(i);
        if (QWidget *widget = itemWidget(listitem))
        {
            // int listWidgetWidth = width();
            // QSize size = widget->size();
            // QSize size = listitem->sizeHint();

            QRect rc = visualItemRect(listitem);
            QSize size = rc.size() - QSize(8, 8);

            widget->setFixedSize(size);
        }
    }
}

QListWidgetItem *UiEquipmentList::findItemByID(const QString &id)
{
    for (int i = 0; i < count(); ++i)
    {
        auto listitem = item(i);
        if (UiEquipmentListItem *widget = qobject_cast<UiEquipmentListItem *>(itemWidget(listitem)))
        {
            if (widget->m_id == id)
                return listitem;
        }
    }

    return nullptr;
}

void UiEquipmentList::connectEquip(const QString &id, bool connect)
{
    if (connect)
    {
        MainWindow::instance->connectEquipment(id);
        MessageHelper::information(this, tr("仪表已连接"));
    }
    else
    {
        MainWindow::instance->deconnectEquipment(id);
        MessageHelper::information(this, tr("仪表已断开"));
    }
}

void UiEquipmentList::deleteEquip(const QString &id)
{
    auto item = findItemByID(id);
    if (item)
    {
        auto ret = MessageHelper::question(this, 
                                        tr("确定删除设备吗？"),
                                        QMessageBox::Yes | QMessageBox::No,
                                        QMessageBox::No);

        if (ret == QMessageBox::No)
        {
            return;
        }

        MainWindow::instance->DeleteEquipment(id);

        removeItemWidget(item);
        delete item;
    }
}
