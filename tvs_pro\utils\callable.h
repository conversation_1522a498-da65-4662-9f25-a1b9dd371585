#ifndef CALLABLE_H
#define CALLABLE_H

#pragma once

#include "singleton.h"
#include <vector>
#include <memory>
#include <functional>
#include <any>
#include <unordered_map>
#include <stdexcept>

class ICallable
{
public:
    virtual ~ICallable() {}
    virtual std::any Call(const std::vector<std::any>& args) const = 0;
};

template<typename R, typename... Args>
class Callable : public ICallable
{
public:
    using FunType = std::function<R(Args...)>;

    Callable(FunType func) : _func(func) {}

    std::any Call(const std::vector<std::any>& args) const override
    {
        return CallImpl(args, std::index_sequence_for<Args...> {});
    }

private:
    template<std::size_t... Is>
    std::any CallImpl(const std::vector<std::any>& args, std::index_sequence<Is...>) const
    {
        return _func(std::any_cast<std::decay_t<Args>>(args[Is])...);
    }

    FunType _func;
};



struct UnknownKey : public std::runtime_error
{
    UnknownKey() : std::runtime_error("Factory: unknown key") {}
    virtual ~UnknownKey() throw() {}
};

struct BadArguments : public std::runtime_error
{
    BadArguments() : std::runtime_error("Factory: non-matching arguments") {}
    virtual ~BadArguments() throw() {}
};


class CallableRegistry : public Singleton<CallableRegistry>
{
public:
    template<typename R, typename... Args>
    void Register(const std::string& key, std::function<R(Args...)> func)
    {
        _callables[key] = std::make_unique<Callable<R, Args...>>(func);
    }

    template<typename Func>
    void Register(const std::string& key, Func func)
    {
        Register( key, std::function(func));
    }

    template<typename R, typename... Args>
    R Call(const std::string& key, Args&&... args) const
    {
        auto it = _callables.find(key);
        if (it != _callables.end())
        {
            auto callable = dynamic_cast<Callable<R, Args...>*>(it->second.get());
            if (callable)
            {
                std::vector<std::any> argList{std::forward<Args>(args)...};
                return std::any_cast<R>(callable->Call(argList));
            }
        }

        throw std::runtime_error("callable not found or wrong signature");
    }

    friend class Singleton<CallableRegistry>;

private:
    std::unordered_map<std::string, std::unique_ptr<ICallable>> _callables;
};


#endif


