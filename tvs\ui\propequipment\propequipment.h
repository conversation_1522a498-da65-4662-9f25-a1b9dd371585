#ifndef PROPEQUIPMENT_H
#define PROPEQUIPMENT_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备属性面板
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>


class QEquipmentSetting;
class QWiringSetting;
class QRangeSetting;
class QCommonSetting;
class QAdvancedSetting;
class ToolBox;


class PropEquipment : public QWidget
{
    Q_OBJECT

public:
    PropEquipment(QWidget *parent = nullptr);

private:
    QEquipmentSetting *m_widget_devicesetting;      //设备设置
    QWiringSetting *m_widget_writingsetting;        //接线设置
    QRangeSetting *m_widget_rangesetting;           //量程配置
    QCommonSetting *m_widget_commonsetting;         //常规设置
    QAdvancedSetting *m_widget_advancedsetting;     //高级设置

    ToolBox *toolBox;                               //自定义控件手风琴
};

#endif // PROPEQUIPMENT_H
