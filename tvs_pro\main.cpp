
#include "mainwindow.h"

#include <QApplication>
#include <QLocale>
#include <QTimer>
#include <QTranslator>
#include <qfile.h>

#include "ui/utils.h"
#include "widgets/animatedsplashscreen.h"


//非阻塞延时方法
void delayMSec(int iMsec)
{
    QTime m_qTimer = QTime::currentTime().addMSecs(iMsec);
    while (QTime::currentTime() < m_qTimer)
    {
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
    }
}


int main(int argc, char *argv[])
{
    QApplication a(argc, argv);

    QTranslator translator;
    const QStringList uiLanguages = QLocale::system().uiLanguages();
    for (const QString &locale : uiLanguages)
    {
        const QString baseName = "TVS_" + QLocale(locale).name();
        if (translator.load(":/i18n/" + baseName))
        {
            a.installTranslator(&translator);
            break;
        }
    }

    // QPixmap pixmap(":/splash.png");
    AnimatedSplashScreen *splash = new AnimatedSplashScreen("./res/start.gif");
    splash->show();


    QTranslator trans;
    if(trans.load("./qtbase_zh_CN.qm"))
    {
        QApplication::installTranslator(&trans);
    }

    // Loading some items
    splash->showMessage("加载模块...");
    QCoreApplication::processEvents();

    MainWindow w;

    loadQssFile(qApp, "./theme/darkstyle.qss");

    delayMSec(2000);

    QTimer::singleShot(1, [&]()
    {
        w.showMaximized();

        // w.setPanelWidth();
        // w.show();
        splash->finish(&w);
    });


    return a.exec();
}
