#include "app/settings.h"
#include "widgets/stdwidget.h"
#include "uipjinfo.h"

#include <QBoxLayout>
#include <QVBoxLayout>
#include <QLabel>
#include <QFormLayout>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>


UiPjInfo::UiPjInfo(QWidget *parent)
    : QWidget{parent}
{
    auto *label = new LabelH2("信息");

    QFormLayout *formLayout = new QFormLayout();

    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(label);
    mainLayout->addLayout(formLayout);
    mainLayout->addStretch();

    formLayout->setVerticalSpacing(20);
    formLayout->setHorizontalSpacing(100);

    // YamlConfigUI::instance().setArray("ui_pj_info/margins", QVector<int>({10, 10, 10, 10}));
    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_pj_info.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_pj_info.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_pj_info.hspacing");

    formLayout->setVerticalSpacing(vspacing);
    formLayout->setHorizontalSpacing(hspacing);

    m_name = new QLabel("测试");
    m_size = new QLabel("测试");
    m_auth = new QLabel("测试");
    m_create_time = new QLabel("测试");
    m_last_editor = new QLabel("测试");
    m_last_edit_time = new QLabel("测试");
    m_remarks = new QTextEdit();
    // m_remarks->setMaximumWidth(200);


    formLayout->addRow("文件名", m_name);
    formLayout->addRow("大小", m_size);
    formLayout->addRow("作者", m_auth);
    formLayout->addRow("创建时间", m_create_time);
    formLayout->addRow("上次修改者", m_last_editor);
    formLayout->addRow("上次修改时间", m_last_edit_time);

    // auto lineedit = new QLineEdit();
    // lineedit->setMaximumWidth(200);
    // layout->addRow("作者", lineedit);

    formLayout->addRow("备注:", m_remarks);
}





// class MyForm : public QWidget
// {
//     Q_OBJECT
//     Q_PROPERTY(QString labelText READ labelText WRITE setLabelText NOTIFY labelTextChanged)
//     Q_PROPERTY(QString editText READ editText WRITE setEditText NOTIFY editTextChanged)

// public:
//     explicit MyForm(QWidget *parent = nullptr) : QWidget(parent)
//     {
//         label = new QLabel(this);
//         edit = new QLineEdit(this);

//         QVBoxLayout *layout = new QVBoxLayout(this);
//         layout->addWidget(label);
//         layout->addWidget(edit);
//         setLayout(layout);

//         setLabelText("Label Text");
//         setEditText("Edit Text");

//         connect(edit, &QLineEdit::textChanged, this, &MyForm::editTextChanged);
//     }

//     QString editText() const
//     {
//         return edit->text();
//     }

//     Q_PROPERTY(QString labelText READ labelText WRITE setLabelText NOTIFY labelTextChanged)
// public:
//     QString labelText() const
//     {
//         return label->text();
//     }

// public slots:
//     void setLabelText(const QString &text)
//     {
//         if (label->text() != text)
//         {
//             label->setText(text);
//             emit labelTextChanged(text);
//         }
//     }
// signals:
//     void labelTextChanged(const QString &text);

// private:
//     QLabel *label;


//     void setEditText(const QString &text)
//     {
//         if (edit->text() != text)
//         {
//             edit->setText(text);
//             emit editTextChanged(text);
//         }
//     }

//     void editTextChanged(const QString &text);

// private:
//     QLineEdit *edit;
// };
