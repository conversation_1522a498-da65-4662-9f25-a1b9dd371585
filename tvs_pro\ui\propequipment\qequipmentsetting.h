#ifndef QDEVICESETTING_H
#define QDEVICESETTING_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备属性-设备配置
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>

class SwitchButton;
class QComboBox;
class QPushButton;

//设备配置
class QEquipmentSetting : public QWidget
{
    Q_OBJECT
public:
    explicit QEquipmentSetting(QWidget *parent = nullptr);

private:
    //设备类型
    QComboBox *m_combDeviceType;
    //测量模式
    QComboBox *m_combMeasureModel;
    QPushButton *m_btnMeasureModel;
    //更新率
    QComboBox *m_combUpdateRate;
    //同步积分
    SwitchButton *m_switchBtn;

signals:
};

#endif // QDEVICESETTING_H
