#include "propchannel.h"
#include "qheaderview.h"
#include "widgets/colorbutton.h"
#include "widgets/stdwidget.h"
#include <QCheckBox>
#include <QPushButton>
#include <QTableWidget>
#include <QVBoxLayout>
#include <app/settings.h>

PropChannel::PropChannel(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto btnDel = new QPushButton("全部删除");
    // btnDel->setFlat(true);
    mainLayout->addWidget(btnDel);

    QTableWidget * table = new QTableWidget();
    mainLayout->addWidget(table, 1);


    QStringList strList =  {"U1", "U2", "U3", "U4", "U5"};

    table->setRowCount(strList.length());     // 设置表格行数
    table->setColumnCount(3);                 // 设置表格列数
    table->setEditTriggers(QAbstractItemView::NoEditTriggers);     // 设置表格内容不可编辑
    table->verticalHeader()->setVisible(false);
    table->horizontalHeader()->setStretchLastSection(true);

    // 表头
    QStringList header = {"通道源选择", "通道颜色", "删除"};
    table->setHorizontalHeaderLabels(header);

    for (int i = 0; i < strList.length(); ++i)
    {
        QCheckBox *checkbox = new QCheckBox(strList[i]);
        checkbox->setChecked(false);
        table->setCellWidget(i, 0, checkbox);

        ColorButton *color = new ColorButton();
        table->setCellWidget(i, 1, color);

        IconButton *btn = new IconButton(QIcon("./res/icon/close.png"), "");
        table->setCellWidget(i, 2, btn);
    }
}
