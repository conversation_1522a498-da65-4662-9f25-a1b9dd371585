#ifndef ANIMATEDSPLASHSCREEN_H
#define ANIMATEDSPLASHSCREEN_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 程序启动画面，支持GIF
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/


#include <QSplashScreen>
#include <QString>
#include <QMovie>
#include <QPixmap>

class AnimatedSplashScreen : public QSplashScreen
{
    Q_OBJECT
public:
    AnimatedSplashScreen(const QString &gifFile);
    ~AnimatedSplashScreen();

private slots:
    void movieFrameChanged(int frameNumber);

private:
    QMovie *m_pMovie = nullptr;
    QPixmap pixmap;
};

#endif // ANIMATEDSPLASHSCREEN_H
