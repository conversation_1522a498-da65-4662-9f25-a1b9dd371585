#ifndef UITESTWAVE_H
#define UITESTWAVE_H

/******************************************************************************
  File Name     : dlgexport.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 测试视图-波形面板
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QLabel>
#include <QWidget>






class QGraphicsSceneDragDropEvent;

class UiTestDrop : public QLabel
{
    Q_OBJECT
public:
    explicit UiTestDrop(QWidget *parent = nullptr);

protected:
    void dragMoveEvent(QDragMoveEvent *event) override;
    void dragEnterEvent(QDragEnterEvent* event) override;
    void dropEvent(QDropEvent* event) override;

signals:
};



class UiTestWave : public QWidget
{
    Q_OBJECT
public:
    explicit UiTestWave(QWidget *parent = nullptr);

signals:
};

#endif // UITESTWAVE_H
