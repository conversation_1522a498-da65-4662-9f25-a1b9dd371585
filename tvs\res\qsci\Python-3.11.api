ArithmeticError(??)
AssertionError(??)
AttributeError(??)
BaseException(??)
BaseExceptionGroup(??)
BlockingIOError(??)
BrokenPipeError(??)
BufferError(??)
BytesWarning(??)
ChildProcessError(??)
ConnectionAbortedError(??)
ConnectionError(??)
ConnectionRefusedError(??)
ConnectionResetError(??)
DeprecationWarning(??)
EOFError(??)
Ellipsis(??)
EncodingWarning(??)
EnvironmentError(??)
Exception(??)
ExceptionGroup(??)
False(??)
FileExistsError(??)
FileNotFoundError(??)
FloatingPointError(??)
FutureWarning(??)
GeneratorExit(??)
IOError(??)
ImportError(??)
ImportWarning(??)
IndentationError(??)
IndexError(??)
InterruptedError(??)
IsADirectoryError(??)
KeyError(??)
KeyboardInterrupt(??)
LookupError(??)
MemoryError(??)
ModuleNotFoundError(??)
NameError(??)
None(??)
NotADirectoryError(??)
NotImplemented(??)
NotImplementedError(??)
OSError(??)
OverflowError(??)
PendingDeprecationWarning(??)
PermissionError(??)
ProcessLookupError(??)
RecursionError(??)
ReferenceError(??)
ResourceWarning(??)
RuntimeError(??)
RuntimeWarning(??)
StopAsyncIteration(??)
StopIteration(??)
SyntaxError(??)
SyntaxWarning(??)
SystemError(??)
SystemExit(??)
TabError(??)
TimeoutError(??)
True(??)
TypeError(??)
UnboundLocalError(??)
UnicodeDecodeError(??)
UnicodeEncodeError(??)
UnicodeError(??)
UnicodeTranslateError(??)
UnicodeWarning(??)
UserWarning(??)
ValueError(??)
Warning(??)
ZeroDivisionError(??)
__phello__.spam.initialized(??)
__phello__.spam.main(??)
abc.ABC(??)
abc.ABCMeta(??)
abc.abstractclassmethod(??)
abc.abstractmethod(??)
abc.abstractproperty(??)
abc.abstractstaticmethod(??)
abc.get_cache_token(??)
abc.update_abstractmethods(??)
abs(??)
aifc.Aifc_read(??)
aifc.Aifc_write(??)
aifc.Chunk(??)
aifc.Error(??)
aifc.builtins(??)
aifc.namedtuple(??)
aifc.open(??)
aifc.struct(??)
aifc.warnings(??)
aiter(??)
all(??)
anext(??)
any(??)
argparse.Action(??)
argparse.ArgumentDefaultsHelpFormatter(??)
argparse.ArgumentError(??)
argparse.ArgumentParser(??)
argparse.ArgumentTypeError(??)
argparse.BooleanOptionalAction(??)
argparse.FileType(??)
argparse.HelpFormatter(??)
argparse.MetavarTypeHelpFormatter(??)
argparse.Namespace(??)
argparse.ONE_OR_MORE
argparse.OPTIONAL
argparse.PARSER
argparse.REMAINDER
argparse.RawDescriptionHelpFormatter(??)
argparse.RawTextHelpFormatter(??)
argparse.SUPPRESS
argparse.ZERO_OR_MORE
argparse.ngettext(??)
argparse.warnings(??)
ascii(??)
ast.AST(??)
ast.Add(??)
ast.And(??)
ast.AnnAssign(expr target, expr annotation, expr? value, int simple)
ast.Assert(expr test, expr? msg)
ast.Assign(expr* targets, expr value, string? type_comment)
ast.AsyncFor(expr target, expr iter, stmt* body, stmt* orelse, string? type_comment)
ast.AsyncFunctionDef(identifier name, arguments args, stmt* body, expr* decorator_list, expr? returns, string? type_comment)
ast.AsyncWith(withitem* items, stmt* body, string? type_comment)
ast.Attribute(expr value, identifier attr, expr_context ctx)
ast.AugAssign(expr target, operator op, expr value)
ast.AugLoad(??)
ast.AugStore(??)
ast.Await(expr value)
ast.BinOp(expr left, operator op, expr right)
ast.BitAnd(??)
ast.BitOr(??)
ast.BitXor(??)
ast.BoolOp(boolop op, expr* values)
ast.Break(??)
ast.Bytes(??)
ast.Call(expr func, expr* args, keyword* keywords)
ast.ClassDef(identifier name, expr* bases, keyword* keywords, stmt* body, expr* decorator_list)
ast.Compare(expr left, cmpop* ops, expr* comparators)
ast.Constant(constant value, string? kind)
ast.Continue(??)
ast.Del(??)
ast.Delete(expr* targets)
ast.Dict(expr* keys, expr* values)
ast.DictComp(expr key, expr value, comprehension* generators)
ast.Div(??)
ast.Ellipsis(??)
ast.Eq(??)
ast.ExceptHandler(expr? type, identifier? name, stmt* body)
ast.Expr(expr value)
ast.Expression(expr body)
ast.ExtSlice(??)
ast.FloorDiv(??)
ast.For(expr target, expr iter, stmt* body, stmt* orelse, string? type_comment)
ast.FormattedValue(expr value, int conversion, expr? format_spec)
ast.FunctionDef(identifier name, arguments args, stmt* body, expr* decorator_list, expr? returns, string? type_comment)
ast.FunctionType(expr* argtypes, expr returns)
ast.GeneratorExp(expr elt, comprehension* generators)
ast.Global(identifier* names)
ast.Gt(??)
ast.GtE(??)
ast.If(expr test, stmt* body, stmt* orelse)
ast.IfExp(expr test, expr body, expr orelse)
ast.Import(alias* names)
ast.ImportFrom(identifier? module, alias* names, int? level)
ast.In(??)
ast.Index(??)
ast.IntEnum(??)
ast.Interactive(stmt* body)
ast.Invert(??)
ast.Is(??)
ast.IsNot(??)
ast.JoinedStr(expr* values)
ast.LShift(??)
ast.Lambda(arguments args, expr body)
ast.List(expr* elts, expr_context ctx)
ast.ListComp(expr elt, comprehension* generators)
ast.Load(??)
ast.Lt(??)
ast.LtE(??)
ast.MatMult(??)
ast.Match(expr subject, match_case* cases)
ast.MatchAs(pattern? pattern, identifier? name)
ast.MatchClass(expr cls, pattern* patterns, identifier* kwd_attrs, pattern* kwd_patterns)
ast.MatchMapping(expr* keys, pattern* patterns, identifier? rest)
ast.MatchOr(pattern* patterns)
ast.MatchSequence(pattern* patterns)
ast.MatchSingleton(constant value)
ast.MatchStar(identifier? name)
ast.MatchValue(expr value)
ast.Mod(??)
ast.Module(stmt* body, type_ignore* type_ignores)
ast.Mult(??)
ast.Name(identifier id, expr_context ctx)
ast.NameConstant(??)
ast.NamedExpr(expr target, expr value)
ast.NodeTransformer(??)
ast.NodeVisitor(??)
ast.Nonlocal(identifier* names)
ast.Not(??)
ast.NotEq(??)
ast.NotIn(??)
ast.Num(??)
ast.Or(??)
ast.Param(??)
ast.Pass(??)
ast.Pow(??)
ast.PyCF_ALLOW_TOP_LEVEL_AWAIT
ast.PyCF_ONLY_AST
ast.PyCF_TYPE_COMMENTS
ast.RShift(??)
ast.Raise(expr? exc, expr? cause)
ast.Return(expr? value)
ast.Set(expr* elts)
ast.SetComp(expr elt, comprehension* generators)
ast.Slice(expr? lower, expr? upper, expr? step)
ast.Starred(expr value, expr_context ctx)
ast.Store(??)
ast.Str(??)
ast.Sub(??)
ast.Subscript(expr value, expr slice, expr_context ctx)
ast.Suite(??)
ast.Try(stmt* body, excepthandler* handlers, stmt* orelse, stmt* finalbody)
ast.TryStar(stmt* body, excepthandler* handlers, stmt* orelse, stmt* finalbody)
ast.Tuple(expr* elts, expr_context ctx)
ast.TypeIgnore(int lineno, string tag)
ast.UAdd(??)
ast.USub(??)
ast.UnaryOp(unaryop op, expr operand)
ast.While(expr test, stmt* body, stmt* orelse)
ast.With(withitem* items, stmt* body, string? type_comment)
ast.Yield(expr? value)
ast.YieldFrom(expr value)
ast.alias(identifier name, identifier? asname)
ast.arg(identifier arg, expr? annotation, string? type_comment)
ast.arguments(arg* posonlyargs, arg* args, arg? vararg, arg* kwonlyargs, expr* kw_defaults, arg? kwarg, expr* defaults)
ast.auto(??)
ast.boolop(??)
ast.cmpop(??)
ast.comprehension(expr target, expr iter, expr* ifs, int is_async)
ast.contextmanager(??)
ast.copy_location(??)
ast.dump(??)
ast.excepthandler(??)
ast.expr(??)
ast.expr_context(??)
ast.fix_missing_locations(??)
ast.get_docstring(??)
ast.get_source_segment(??)
ast.increment_lineno(??)
ast.iter_child_nodes(??)
ast.iter_fields(??)
ast.keyword(identifier? arg, expr value)
ast.literal_eval(??)
ast.main(??)
ast.match_case(pattern pattern, expr? guard, stmt* body)
ast.mod(??)
ast.nullcontext(??)
ast.operator(??)
ast.parse(??)
ast.pattern(??)
ast.slice(??)
ast.stmt(??)
ast.sys(??)
ast.type_ignore(??)
ast.unaryop(??)
ast.unparse(??)
ast.walk(??)
ast.withitem(expr context_expr, expr? optional_vars)
asynchat.async_chat(??)
asynchat.asyncore(??)
asynchat.deque([iterable[, maxlen]]) --> deque object
asynchat.find_prefix_at_end(??)
asynchat.simple_producer(??)
asyncio.ALL_COMPLETED
asyncio.AbstractChildWatcher(??)
asyncio.AbstractEventLoop(??)
asyncio.AbstractEventLoopPolicy(??)
asyncio.AbstractServer(??)
asyncio.Barrier(??)
asyncio.BaseEventLoop(??)
asyncio.BaseProtocol(??)
asyncio.BaseTransport(??)
asyncio.BoundedSemaphore(??)
asyncio.BrokenBarrierError(??)
asyncio.BufferedProtocol(??)
asyncio.CancelledError(??)
asyncio.Condition(??)
asyncio.DatagramProtocol(??)
asyncio.DatagramTransport(??)
asyncio.DefaultEventLoopPolicy(??)
asyncio.Event(??)
asyncio.FIRST_COMPLETED
asyncio.FIRST_EXCEPTION
asyncio.FastChildWatcher(??)
asyncio.Future(??)
asyncio.Handle(??)
asyncio.IncompleteReadError(??)
asyncio.InvalidStateError(??)
asyncio.LifoQueue(??)
asyncio.LimitOverrunError(??)
asyncio.Lock(??)
asyncio.MultiLoopChildWatcher(??)
asyncio.PidfdChildWatcher(??)
asyncio.PriorityQueue(??)
asyncio.Protocol(??)
asyncio.Queue(??)
asyncio.QueueEmpty(??)
asyncio.QueueFull(??)
asyncio.ReadTransport(??)
asyncio.Runner(??)
asyncio.SafeChildWatcher(??)
asyncio.SelectorEventLoop(??)
asyncio.Semaphore(??)
asyncio.SendfileNotAvailableError(??)
asyncio.Server(??)
asyncio.StreamReader(??)
asyncio.StreamReaderProtocol(??)
asyncio.StreamWriter(??)
asyncio.SubprocessProtocol(??)
asyncio.SubprocessTransport(??)
asyncio.Task(??)
asyncio.TaskGroup(??)
asyncio.ThreadedChildWatcher(??)
asyncio.Timeout(??)
asyncio.TimeoutError(??)
asyncio.TimerHandle(??)
asyncio.Transport(??)
asyncio.WriteTransport(??)
asyncio.all_tasks(??)
asyncio.as_completed(??)
asyncio.base_events(??)
asyncio.base_events.BaseEventLoop(??)
asyncio.base_events.MAXIMUM_SELECT_TIMEOUT
asyncio.base_events.Server(??)
asyncio.base_events.collections(??)
asyncio.base_events.concurrent(??)
asyncio.base_events.constants(??)
asyncio.base_events.coroutines(??)
asyncio.base_events.events(??)
asyncio.base_events.exceptions(??)
asyncio.base_events.functools(??)
asyncio.base_events.futures(??)
asyncio.base_events.heapq(??)
asyncio.base_events.itertools(??)
asyncio.base_events.logger(??)
asyncio.base_events.os(??)
asyncio.base_events.protocols(??)
asyncio.base_events.socket(??)
asyncio.base_events.ssl(??)
asyncio.base_events.sslproto(??)
asyncio.base_events.staggered(??)
asyncio.base_events.stat(??)
asyncio.base_events.subprocess(??)
asyncio.base_events.sys(??)
asyncio.base_events.tasks(??)
asyncio.base_events.threading(??)
asyncio.base_events.time(??)
asyncio.base_events.traceback(??)
asyncio.base_events.transports(??)
asyncio.base_events.trsock(??)
asyncio.base_events.warnings(??)
asyncio.base_events.weakref(??)
asyncio.base_futures(??)
asyncio.base_futures.format_helpers(??)
asyncio.base_futures.get_ident() -> integer
asyncio.base_futures.isfuture(??)
asyncio.base_futures.reprlib(??)
asyncio.base_subprocess(??)
asyncio.base_subprocess.BaseSubprocessTransport(??)
asyncio.base_subprocess.ReadSubprocessPipeProto(??)
asyncio.base_subprocess.WriteSubprocessPipeProto(??)
asyncio.base_subprocess.collections(??)
asyncio.base_subprocess.logger(??)
asyncio.base_subprocess.protocols(??)
asyncio.base_subprocess.subprocess(??)
asyncio.base_subprocess.transports(??)
asyncio.base_subprocess.warnings(??)
asyncio.base_tasks(??)
asyncio.base_tasks.base_futures(??)
asyncio.base_tasks.coroutines(??)
asyncio.base_tasks.linecache(??)
asyncio.base_tasks.reprlib(??)
asyncio.base_tasks.traceback(??)
asyncio.constants(??)
asyncio.constants.ACCEPT_RETRY_DELAY
asyncio.constants.DEBUG_STACK_DEPTH
asyncio.constants.FLOW_CONTROL_HIGH_WATER_SSL_READ
asyncio.constants.FLOW_CONTROL_HIGH_WATER_SSL_WRITE
asyncio.constants.LOG_THRESHOLD_FOR_CONNLOST_WRITES
asyncio.constants.SENDFILE_FALLBACK_READBUFFER_SIZE
asyncio.constants.SSL_HANDSHAKE_TIMEOUT
asyncio.constants.SSL_SHUTDOWN_TIMEOUT
asyncio.constants.enum(??)
asyncio.coroutines(??)
asyncio.coroutines.collections(??)
asyncio.coroutines.inspect(??)
asyncio.coroutines.iscoroutine(??)
asyncio.coroutines.iscoroutinefunction(??)
asyncio.coroutines.os(??)
asyncio.coroutines.sys(??)
asyncio.coroutines.traceback(??)
asyncio.coroutines.types(??)
asyncio.create_subprocess_exec(??)
asyncio.create_subprocess_shell(??)
asyncio.create_task(??)
asyncio.current_task(??)
asyncio.ensure_future(??)
asyncio.events(??)
asyncio.events.AbstractEventLoop(??)
asyncio.events.AbstractEventLoopPolicy(??)
asyncio.events.AbstractServer(??)
asyncio.events.BaseDefaultEventLoopPolicy(??)
asyncio.events.Handle(??)
asyncio.events.TimerHandle(??)
asyncio.events.contextvars(??)
asyncio.events.format_helpers(??)
asyncio.events.get_child_watcher(??)
asyncio.events.get_event_loop(??)
asyncio.events.get_event_loop_policy(??)
asyncio.events.get_running_loop(??)
asyncio.events.new_event_loop(??)
asyncio.events.os(??)
asyncio.events.set_child_watcher(??)
asyncio.events.set_event_loop(??)
asyncio.events.set_event_loop_policy(??)
asyncio.events.socket(??)
asyncio.events.subprocess(??)
asyncio.events.sys(??)
asyncio.events.threading(??)
asyncio.exceptions(??)
asyncio.exceptions.BrokenBarrierError(??)
asyncio.exceptions.CancelledError(??)
asyncio.exceptions.IncompleteReadError(??)
asyncio.exceptions.InvalidStateError(??)
asyncio.exceptions.LimitOverrunError(??)
asyncio.exceptions.SendfileNotAvailableError(??)
asyncio.exceptions.TimeoutError(??)
asyncio.format_helpers(??)
asyncio.format_helpers.constants(??)
asyncio.format_helpers.extract_stack(??)
asyncio.format_helpers.functools(??)
asyncio.format_helpers.inspect(??)
asyncio.format_helpers.reprlib(??)
asyncio.format_helpers.sys(??)
asyncio.format_helpers.traceback(??)
asyncio.futures(??)
asyncio.futures.Future(??)
asyncio.futures.GenericAlias(??)
asyncio.futures.STACK_DEBUG
asyncio.futures.base_futures(??)
asyncio.futures.concurrent(??)
asyncio.futures.contextvars(??)
asyncio.futures.events(??)
asyncio.futures.exceptions(??)
asyncio.futures.format_helpers(??)
asyncio.futures.isfuture(??)
asyncio.futures.logging(??)
asyncio.futures.sys(??)
asyncio.futures.warnings(??)
asyncio.futures.wrap_future(??)
asyncio.gather(??)
asyncio.get_child_watcher(??)
asyncio.get_event_loop(??)
asyncio.get_event_loop_policy(??)
asyncio.get_running_loop(??)
asyncio.iscoroutine(??)
asyncio.iscoroutinefunction(??)
asyncio.isfuture(??)
asyncio.locks(??)
asyncio.locks.Barrier(??)
asyncio.locks.BoundedSemaphore(??)
asyncio.locks.Condition(??)
asyncio.locks.Event(??)
asyncio.locks.Lock(??)
asyncio.locks.Semaphore(??)
asyncio.locks.collections(??)
asyncio.locks.enum(??)
asyncio.locks.exceptions(??)
asyncio.locks.mixins(??)
asyncio.locks.tasks(??)
asyncio.log(??)
asyncio.log.logger(??)
asyncio.log.logging(??)
asyncio.mixins(??)
asyncio.mixins.events(??)
asyncio.mixins.threading(??)
asyncio.new_event_loop(??)
asyncio.open_connection(??)
asyncio.open_unix_connection(??)
asyncio.proactor_events(??)
asyncio.proactor_events.BaseProactorEventLoop(??)
asyncio.proactor_events.base_events(??)
asyncio.proactor_events.collections(??)
asyncio.proactor_events.constants(??)
asyncio.proactor_events.exceptions(??)
asyncio.proactor_events.futures(??)
asyncio.proactor_events.io(??)
asyncio.proactor_events.logger(??)
asyncio.proactor_events.os(??)
asyncio.proactor_events.protocols(??)
asyncio.proactor_events.signal(??)
asyncio.proactor_events.socket(??)
asyncio.proactor_events.sslproto(??)
asyncio.proactor_events.threading(??)
asyncio.proactor_events.transports(??)
asyncio.proactor_events.trsock(??)
asyncio.proactor_events.warnings(??)
asyncio.protocols(??)
asyncio.protocols.BaseProtocol(??)
asyncio.protocols.BufferedProtocol(??)
asyncio.protocols.DatagramProtocol(??)
asyncio.protocols.Protocol(??)
asyncio.protocols.SubprocessProtocol(??)
asyncio.queues(??)
asyncio.queues.GenericAlias(??)
asyncio.queues.LifoQueue(??)
asyncio.queues.PriorityQueue(??)
asyncio.queues.Queue(??)
asyncio.queues.QueueEmpty(??)
asyncio.queues.QueueFull(??)
asyncio.queues.collections(??)
asyncio.queues.heapq(??)
asyncio.queues.locks(??)
asyncio.queues.mixins(??)
asyncio.run(??)
asyncio.run_coroutine_threadsafe(??)
asyncio.runners(??)
asyncio.runners.Runner(??)
asyncio.runners.contextvars(??)
asyncio.runners.coroutines(??)
asyncio.runners.enum(??)
asyncio.runners.events(??)
asyncio.runners.exceptions(??)
asyncio.runners.functools(??)
asyncio.runners.run(??)
asyncio.runners.signal(??)
asyncio.runners.sys(??)
asyncio.runners.tasks(??)
asyncio.runners.threading(??)
asyncio.selector_events(??)
asyncio.selector_events.BaseSelectorEventLoop(??)
asyncio.selector_events.base_events(??)
asyncio.selector_events.collections(??)
asyncio.selector_events.constants(??)
asyncio.selector_events.errno(??)
asyncio.selector_events.events(??)
asyncio.selector_events.functools(??)
asyncio.selector_events.futures(??)
asyncio.selector_events.logger(??)
asyncio.selector_events.protocols(??)
asyncio.selector_events.selectors(??)
asyncio.selector_events.socket(??)
asyncio.selector_events.ssl(??)
asyncio.selector_events.sslproto(??)
asyncio.selector_events.transports(??)
asyncio.selector_events.trsock(??)
asyncio.selector_events.warnings(??)
asyncio.selector_events.weakref(??)
asyncio.set_child_watcher(??)
asyncio.set_event_loop(??)
asyncio.set_event_loop_policy(??)
asyncio.shield(??)
asyncio.sleep(??)
asyncio.sslproto(??)
asyncio.sslproto.AppProtocolState(??)
asyncio.sslproto.SSLAgainErrors(??)
asyncio.sslproto.SSLProtocol(??)
asyncio.sslproto.SSLProtocolState(??)
asyncio.sslproto.add_flowcontrol_defaults(??)
asyncio.sslproto.collections(??)
asyncio.sslproto.constants(??)
asyncio.sslproto.enum(??)
asyncio.sslproto.exceptions(??)
asyncio.sslproto.logger(??)
asyncio.sslproto.protocols(??)
asyncio.sslproto.ssl(??)
asyncio.sslproto.transports(??)
asyncio.sslproto.warnings(??)
asyncio.staggered(??)
asyncio.staggered.contextlib(??)
asyncio.staggered.events(??)
asyncio.staggered.exceptions_mod(??)
asyncio.staggered.locks(??)
asyncio.staggered.staggered_race(??)
asyncio.staggered.tasks(??)
asyncio.staggered.typing(??)
asyncio.start_server(??)
asyncio.start_unix_server(??)
asyncio.streams(??)
asyncio.streams.FlowControlMixin(??)
asyncio.streams.StreamReader(??)
asyncio.streams.StreamReaderProtocol(??)
asyncio.streams.StreamWriter(??)
asyncio.streams.coroutines(??)
asyncio.streams.events(??)
asyncio.streams.exceptions(??)
asyncio.streams.format_helpers(??)
asyncio.streams.logger(??)
asyncio.streams.open_connection(??)
asyncio.streams.open_unix_connection(??)
asyncio.streams.protocols(??)
asyncio.streams.sleep(??)
asyncio.streams.socket(??)
asyncio.streams.start_server(??)
asyncio.streams.start_unix_server(??)
asyncio.streams.sys(??)
asyncio.streams.warnings(??)
asyncio.streams.weakref(??)
asyncio.subprocess(??)
asyncio.subprocess.DEVNULL
asyncio.subprocess.PIPE
asyncio.subprocess.Process(??)
asyncio.subprocess.STDOUT
asyncio.subprocess.SubprocessStreamProtocol(??)
asyncio.subprocess.create_subprocess_exec(??)
asyncio.subprocess.create_subprocess_shell(??)
asyncio.subprocess.events(??)
asyncio.subprocess.logger(??)
asyncio.subprocess.protocols(??)
asyncio.subprocess.streams(??)
asyncio.subprocess.subprocess(??)
asyncio.subprocess.tasks(??)
asyncio.sys(??)
asyncio.taskgroups(??)
asyncio.taskgroups.TaskGroup(??)
asyncio.taskgroups.events(??)
asyncio.taskgroups.exceptions(??)
asyncio.taskgroups.tasks(??)
asyncio.tasks(??)
asyncio.tasks.ALL_COMPLETED
asyncio.tasks.FIRST_COMPLETED
asyncio.tasks.FIRST_EXCEPTION
asyncio.tasks.GenericAlias(??)
asyncio.tasks.Task(??)
asyncio.tasks.all_tasks(??)
asyncio.tasks.as_completed(??)
asyncio.tasks.base_tasks(??)
asyncio.tasks.concurrent(??)
asyncio.tasks.contextvars(??)
asyncio.tasks.coroutines(??)
asyncio.tasks.create_task(??)
asyncio.tasks.current_task(??)
asyncio.tasks.ensure_future(??)
asyncio.tasks.events(??)
asyncio.tasks.exceptions(??)
asyncio.tasks.functools(??)
asyncio.tasks.futures(??)
asyncio.tasks.gather(??)
asyncio.tasks.inspect(??)
asyncio.tasks.itertools(??)
asyncio.tasks.run_coroutine_threadsafe(??)
asyncio.tasks.shield(??)
asyncio.tasks.sleep(??)
asyncio.tasks.types(??)
asyncio.tasks.wait(??)
asyncio.tasks.wait_for(??)
asyncio.tasks.warnings(??)
asyncio.tasks.weakref(??)
asyncio.threads(??)
asyncio.threads.contextvars(??)
asyncio.threads.events(??)
asyncio.threads.functools(??)
asyncio.threads.to_thread(??)
asyncio.timeout(??)
asyncio.timeout_at(??)
asyncio.timeouts(??)
asyncio.timeouts.Optional(??)
asyncio.timeouts.Timeout(??)
asyncio.timeouts.TracebackType(tb_next, tb_frame, tb_lasti, tb_lineno)
asyncio.timeouts.Type(??)
asyncio.timeouts.enum(??)
asyncio.timeouts.events(??)
asyncio.timeouts.exceptions(??)
asyncio.timeouts.final(??)
asyncio.timeouts.tasks(??)
asyncio.timeouts.timeout(??)
asyncio.timeouts.timeout_at(??)
asyncio.to_thread(??)
asyncio.transports(??)
asyncio.transports.BaseTransport(??)
asyncio.transports.DatagramTransport(??)
asyncio.transports.ReadTransport(??)
asyncio.transports.SubprocessTransport(??)
asyncio.transports.Transport(??)
asyncio.transports.WriteTransport(??)
asyncio.trsock(??)
asyncio.trsock.TransportSocket(??)
asyncio.trsock.socket(??)
asyncio.unix_events(??)
asyncio.unix_events.AbstractChildWatcher(??)
asyncio.unix_events.BaseChildWatcher(??)
asyncio.unix_events.DefaultEventLoopPolicy(??)
asyncio.unix_events.FastChildWatcher(??)
asyncio.unix_events.MultiLoopChildWatcher(??)
asyncio.unix_events.PidfdChildWatcher(??)
asyncio.unix_events.SafeChildWatcher(??)
asyncio.unix_events.SelectorEventLoop(??)
asyncio.unix_events.ThreadedChildWatcher(??)
asyncio.unix_events.base_events(??)
asyncio.unix_events.base_subprocess(??)
asyncio.unix_events.constants(??)
asyncio.unix_events.coroutines(??)
asyncio.unix_events.errno(??)
asyncio.unix_events.events(??)
asyncio.unix_events.exceptions(??)
asyncio.unix_events.futures(??)
asyncio.unix_events.io(??)
asyncio.unix_events.itertools(??)
asyncio.unix_events.logger(??)
asyncio.unix_events.os(??)
asyncio.unix_events.selector_events(??)
asyncio.unix_events.selectors(??)
asyncio.unix_events.signal(??)
asyncio.unix_events.socket(??)
asyncio.unix_events.stat(??)
asyncio.unix_events.subprocess(??)
asyncio.unix_events.sys(??)
asyncio.unix_events.tasks(??)
asyncio.unix_events.threading(??)
asyncio.unix_events.transports(??)
asyncio.unix_events.waitstatus_to_exitcode(??)
asyncio.unix_events.warnings(??)
asyncio.wait(??)
asyncio.wait_for(??)
asyncio.wrap_future(??)
asyncore.EAGAIN
asyncore.EALREADY
asyncore.EBADF
asyncore.ECONNABORTED
asyncore.ECONNRESET
asyncore.EINPROGRESS
asyncore.EINVAL
asyncore.EISCONN
asyncore.ENOTCONN
asyncore.EPIPE
asyncore.ESHUTDOWN
asyncore.EWOULDBLOCK
asyncore.ExitNow(??)
asyncore.close_all(??)
asyncore.compact_traceback(??)
asyncore.dispatcher(??)
asyncore.dispatcher_with_send(??)
asyncore.errorcode(??)
asyncore.file_dispatcher(??)
asyncore.file_wrapper(??)
asyncore.loop(??)
asyncore.os(??)
asyncore.poll(??)
asyncore.poll2(??)
asyncore.poll3(??)
asyncore.read(??)
asyncore.readwrite(??)
asyncore.select(??)
asyncore.socket(??)
asyncore.socket_map(??)
asyncore.sys(??)
asyncore.time(??)
asyncore.warnings(??)
asyncore.write(??)
atexit.register(func, *args, **kwargs) -> func
atexit.unregister(func) -> None
base64.MAXBINSIZE
base64.MAXLINESIZE
base64.a85decode(??)
base64.a85encode(??)
base64.b16decode(??)
base64.b16encode(??)
base64.b32decode(??)
base64.b32encode(??)
base64.b32hexdecode(??)
base64.b32hexencode(??)
base64.b64decode(??)
base64.b64encode(??)
base64.b85decode(??)
base64.b85encode(??)
base64.binascii(??)
base64.bytes_types(??)
base64.decode(??)
base64.decodebytes(??)
base64.encode(??)
base64.encodebytes(??)
base64.main(??)
base64.re(??)
base64.standard_b64decode(??)
base64.standard_b64encode(??)
base64.struct(??)
base64.test(??)
base64.urlsafe_b64decode(??)
base64.urlsafe_b64encode(??)
bdb.Bdb(??)
bdb.BdbQuit(??)
bdb.Breakpoint(??)
bdb.CO_ASYNC_GENERATOR
bdb.CO_COROUTINE
bdb.CO_GENERATOR
bdb.GENERATOR_AND_COROUTINE_FLAGS
bdb.Tdb(??)
bdb.bar(??)
bdb.checkfuncname(??)
bdb.effective(??)
bdb.fnmatch(??)
bdb.foo(??)
bdb.os(??)
bdb.set_trace(??)
bdb.sys(??)
bdb.test(??)
bin(??)
bisect.bisect(??)
bisect.bisect_left(??)
bisect.bisect_right(??)
bisect.insort(??)
bisect.insort_left(??)
bisect.insort_right(??)
bool(x) -> bool
breakpoint(*args, **kws)
builtins.ArithmeticError(??)
builtins.AssertionError(??)
builtins.AttributeError(??)
builtins.BaseException(??)
builtins.BaseExceptionGroup(??)
builtins.BlockingIOError(??)
builtins.BrokenPipeError(??)
builtins.BufferError(??)
builtins.BytesWarning(??)
builtins.ChildProcessError(??)
builtins.ConnectionAbortedError(??)
builtins.ConnectionError(??)
builtins.ConnectionRefusedError(??)
builtins.ConnectionResetError(??)
builtins.DeprecationWarning(??)
builtins.EOFError(??)
builtins.Ellipsis(??)
builtins.EncodingWarning(??)
builtins.EnvironmentError(??)
builtins.Exception(??)
builtins.ExceptionGroup(??)
builtins.False
builtins.FileExistsError(??)
builtins.FileNotFoundError(??)
builtins.FloatingPointError(??)
builtins.FutureWarning(??)
builtins.GeneratorExit(??)
builtins.IOError(??)
builtins.ImportError(??)
builtins.ImportWarning(??)
builtins.IndentationError(??)
builtins.IndexError(??)
builtins.InterruptedError(??)
builtins.IsADirectoryError(??)
builtins.KeyError(??)
builtins.KeyboardInterrupt(??)
builtins.LookupError(??)
builtins.MemoryError(??)
builtins.ModuleNotFoundError(??)
builtins.NameError(??)
builtins.None
builtins.NotADirectoryError(??)
builtins.NotImplemented(??)
builtins.NotImplementedError(??)
builtins.OSError(??)
builtins.OverflowError(??)
builtins.PendingDeprecationWarning(??)
builtins.PermissionError(??)
builtins.ProcessLookupError(??)
builtins.RecursionError(??)
builtins.ReferenceError(??)
builtins.ResourceWarning(??)
builtins.RuntimeError(??)
builtins.RuntimeWarning(??)
builtins.StopAsyncIteration(??)
builtins.StopIteration(??)
builtins.SyntaxError(??)
builtins.SyntaxWarning(??)
builtins.SystemError(??)
builtins.SystemExit(??)
builtins.TabError(??)
builtins.TimeoutError(??)
builtins.True
builtins.TypeError(??)
builtins.UnboundLocalError(??)
builtins.UnicodeDecodeError(??)
builtins.UnicodeEncodeError(??)
builtins.UnicodeError(??)
builtins.UnicodeTranslateError(??)
builtins.UnicodeWarning(??)
builtins.UserWarning(??)
builtins.ValueError(??)
builtins.Warning(??)
builtins.ZeroDivisionError(??)
builtins.abs(??)
builtins.aiter(??)
builtins.all(??)
builtins.anext(??)
builtins.any(??)
builtins.ascii(??)
builtins.bin(??)
builtins.bool(x) -> bool
builtins.breakpoint(*args, **kws)
builtins.bytearray(iterable_of_ints) -> bytearray
builtins.bytes(iterable_of_ints) -> bytes
builtins.callable(??)
builtins.chr(??)
builtins.classmethod(function) -> method
builtins.compile(??)
builtins.complex(??)
builtins.copyright(??)
builtins.credits(??)
builtins.delattr(??)
builtins.dict() -> new empty dictionary
builtins.dir([object]) -> list of strings
builtins.divmod(??)
builtins.enumerate(??)
builtins.eval(??)
builtins.exec(??)
builtins.exit(??)
builtins.filter(function or None, iterable) --> filter object
builtins.float(??)
builtins.format(??)
builtins.frozenset() -> empty frozenset object
builtins.getattr(object, name[, default]) -> value
builtins.globals(??)
builtins.hasattr(??)
builtins.hash(??)
builtins.help(??)
builtins.hex(??)
builtins.id(??)
builtins.input(??)
builtins.int([x]) -> integer
builtins.isinstance(??)
builtins.issubclass(??)
builtins.iter(iterable) -> iterator
builtins.len(??)
builtins.license(??)
builtins.list(??)
builtins.locals(??)
builtins.map(func, *iterables) --> map object
builtins.max(iterable, *[, default=obj, key=func]) -> value
builtins.memoryview(??)
builtins.min(iterable, *[, default=obj, key=func]) -> value
builtins.next(iterator[, default])
builtins.object(??)
builtins.oct(??)
builtins.open(??)
builtins.ord(??)
builtins.pow(??)
builtins.print(??)
builtins.property(??)
builtins.quit(??)
builtins.range(stop) -> range object
builtins.repr(??)
builtins.reversed(??)
builtins.round(??)
builtins.set() -> new empty set object
builtins.setattr(??)
builtins.slice(stop)
builtins.sorted(??)
builtins.staticmethod(function) -> method
builtins.str(object='') -> str
builtins.sum(??)
builtins.super() -> same as super(__class__, <first argument>)
builtins.tuple(??)
builtins.type(object) -> the object's type
builtins.vars([object]) -> dictionary
builtins.zip(*iterables, strict=False) --> Yield tuples until an input is exhausted.
bytearray(iterable_of_ints) -> bytearray
bytes(iterable_of_ints) -> bytes
bz2.BZ2Compressor(??)
bz2.BZ2Decompressor(??)
bz2.BZ2File(??)
bz2.compress(??)
bz2.decompress(??)
bz2.io(??)
bz2.open(??)
bz2.os(??)
cProfile.Profile(timer=None, timeunit=None, subcalls=True, builtins=True)
cProfile.label(??)
cProfile.main(??)
cProfile.run(??)
cProfile.runctx(??)
calendar.Calendar(??)
calendar.EPOCH
calendar.FRIDAY
calendar.February
calendar.HTMLCalendar(??)
calendar.IllegalMonthError(??)
calendar.IllegalWeekdayError(??)
calendar.January
calendar.LocaleHTMLCalendar(??)
calendar.LocaleTextCalendar(??)
calendar.MONDAY
calendar.SATURDAY
calendar.SUNDAY
calendar.THURSDAY
calendar.TUESDAY
calendar.TextCalendar(??)
calendar.WEDNESDAY
calendar.c(??)
calendar.calendar(??)
calendar.datetime(??)
calendar.day_abbr(??)
calendar.day_name(??)
calendar.different_locale(??)
calendar.error(??)
calendar.firstweekday(??)
calendar.format(??)
calendar.formatstring(??)
calendar.isleap(??)
calendar.leapdays(??)
calendar.main(??)
calendar.mdays(??)
calendar.month(??)
calendar.month_abbr(??)
calendar.month_name(??)
calendar.monthcalendar(??)
calendar.monthrange(??)
calendar.prcal(??)
calendar.prmonth(??)
calendar.prweek(??)
calendar.repeat(object [,times]) -> create an iterator which returns the object
calendar.setfirstweekday(??)
calendar.sys(??)
calendar.timegm(??)
calendar.week(??)
calendar.weekday(??)
calendar.weekheader(??)
callable(??)
cgi.BytesIO(??)
cgi.FeedParser(??)
cgi.FieldStorage(??)
cgi.Mapping(??)
cgi.Message(??)
cgi.MiniFieldStorage(??)
cgi.StringIO(??)
cgi.TextIOWrapper(??)
cgi.closelog(??)
cgi.dolog(??)
cgi.html(??)
cgi.initlog(??)
cgi.locale(??)
cgi.log(??)
cgi.logfile
cgi.logfp(??)
cgi.maxlen
cgi.nolog(??)
cgi.os(??)
cgi.parse(??)
cgi.parse_header(??)
cgi.parse_multipart(??)
cgi.print_arguments(??)
cgi.print_directory(??)
cgi.print_environ(??)
cgi.print_environ_usage(??)
cgi.print_exception(??)
cgi.print_form(??)
cgi.sys(??)
cgi.tempfile(??)
cgi.test(??)
cgi.urllib(??)
cgi.valid_boundary(??)
cgi.warnings(??)
cgitb.Hook(??)
cgitb.enable(??)
cgitb.grey(??)
cgitb.handler(??)
cgitb.html(??)
cgitb.html_escape(??)
cgitb.inspect(??)
cgitb.keyword(??)
cgitb.linecache(??)
cgitb.lookup(??)
cgitb.os(??)
cgitb.pydoc(??)
cgitb.reset(??)
cgitb.scanvars(??)
cgitb.small(??)
cgitb.strong(??)
cgitb.sys(??)
cgitb.tempfile(??)
cgitb.text(??)
cgitb.time(??)
cgitb.tokenize(??)
cgitb.traceback(??)
cgitb.warnings(??)
chr(??)
chunk.Chunk(??)
chunk.warnings(??)
classmethod(function) -> method
cmd.Cmd(??)
cmd.IDENTCHARS
cmd.PROMPT
cmd.string(??)
cmd.sys(??)
code.CommandCompiler(??)
code.InteractiveConsole(??)
code.InteractiveInterpreter(??)
code.compile_command(??)
code.interact(??)
code.sys(??)
code.traceback(??)
codecs.BOM
codecs.BOM32_BE
codecs.BOM32_LE
codecs.BOM64_BE
codecs.BOM64_LE
codecs.BOM_BE
codecs.BOM_LE
codecs.BOM_UTF16
codecs.BOM_UTF16_BE
codecs.BOM_UTF16_LE
codecs.BOM_UTF32
codecs.BOM_UTF32_BE
codecs.BOM_UTF32_LE
codecs.BOM_UTF8
codecs.BufferedIncrementalDecoder(??)
codecs.BufferedIncrementalEncoder(??)
codecs.Codec(??)
codecs.CodecInfo(??)
codecs.EncodedFile(??)
codecs.IncrementalDecoder(??)
codecs.IncrementalEncoder(??)
codecs.StreamReader(??)
codecs.StreamReaderWriter(??)
codecs.StreamRecoder(??)
codecs.StreamWriter(??)
codecs.ascii_decode(??)
codecs.ascii_encode(??)
codecs.backslashreplace_errors(??)
codecs.builtins(??)
codecs.charmap_build(??)
codecs.charmap_decode(??)
codecs.charmap_encode(??)
codecs.decode(??)
codecs.encode(??)
codecs.escape_decode(??)
codecs.escape_encode(??)
codecs.getdecoder(??)
codecs.getencoder(??)
codecs.getincrementaldecoder(??)
codecs.getincrementalencoder(??)
codecs.getreader(??)
codecs.getwriter(??)
codecs.ignore_errors(??)
codecs.iterdecode(??)
codecs.iterencode(??)
codecs.latin_1_decode(??)
codecs.latin_1_encode(??)
codecs.lookup(??)
codecs.lookup_error(errors) -> handler
codecs.make_encoding_map(??)
codecs.make_identity_dict(rng) -> dict
codecs.namereplace_errors(??)
codecs.open(??)
codecs.raw_unicode_escape_decode(??)
codecs.raw_unicode_escape_encode(??)
codecs.readbuffer_encode(??)
codecs.register(??)
codecs.register_error(??)
codecs.replace_errors(??)
codecs.strict_errors(??)
codecs.sys(??)
codecs.unicode_escape_decode(??)
codecs.unicode_escape_encode(??)
codecs.unregister(??)
codecs.utf_16_be_decode(??)
codecs.utf_16_be_encode(??)
codecs.utf_16_decode(??)
codecs.utf_16_encode(??)
codecs.utf_16_ex_decode(??)
codecs.utf_16_le_decode(??)
codecs.utf_16_le_encode(??)
codecs.utf_32_be_decode(??)
codecs.utf_32_be_encode(??)
codecs.utf_32_decode(??)
codecs.utf_32_encode(??)
codecs.utf_32_ex_decode(??)
codecs.utf_32_le_decode(??)
codecs.utf_32_le_encode(??)
codecs.utf_7_decode(??)
codecs.utf_7_encode(??)
codecs.utf_8_decode(??)
codecs.utf_8_encode(??)
codecs.xmlcharrefreplace_errors(??)
codeop.CommandCompiler(??)
codeop.Compile(??)
codeop.PyCF_ALLOW_INCOMPLETE_INPUT
codeop.PyCF_DONT_IMPLY_DEDENT
codeop.compile_command(??)
codeop.warnings(??)
collections.ChainMap(??)
collections.Counter(??)
collections.OrderedDict(??)
collections.UserDict(??)
collections.UserList(??)
collections.UserString(??)
collections.abc(??)
collections.abc.AsyncGenerator(??)
collections.abc.AsyncIterable(??)
collections.abc.AsyncIterator(??)
collections.abc.Awaitable(??)
collections.abc.ByteString(??)
collections.abc.Callable(??)
collections.abc.Collection(??)
collections.abc.Container(??)
collections.abc.Coroutine(??)
collections.abc.Generator(??)
collections.abc.Hashable(??)
collections.abc.ItemsView(??)
collections.abc.Iterable(??)
collections.abc.Iterator(??)
collections.abc.KeysView(??)
collections.abc.Mapping(??)
collections.abc.MappingView(??)
collections.abc.MutableMapping(??)
collections.abc.MutableSequence(??)
collections.abc.MutableSet(??)
collections.abc.Reversible(??)
collections.abc.Sequence(??)
collections.abc.Set(??)
collections.abc.Sized(??)
collections.abc.ValuesView(??)
collections.defaultdict(default_factory=None, /, [...]) --> dict with default factory
collections.deque([iterable[, maxlen]]) --> deque object
collections.namedtuple(??)
colorsys.ONE_SIXTH
colorsys.ONE_THIRD
colorsys.TWO_THIRD
colorsys.hls_to_rgb(??)
colorsys.hsv_to_rgb(??)
colorsys.rgb_to_hls(??)
colorsys.rgb_to_hsv(??)
colorsys.rgb_to_yiq(??)
colorsys.yiq_to_rgb(??)
compile(??)
compileall.Path(??)
compileall.compile_dir(??)
compileall.compile_file(??)
compileall.compile_path(??)
compileall.filecmp(??)
compileall.importlib(??)
compileall.main(??)
compileall.os(??)
compileall.partial(func, *args, **keywords) - new function with partial application
compileall.py_compile(??)
compileall.struct(??)
compileall.sys(??)
complex(??)
concurrent.futures(??)
concurrent.futures.ALL_COMPLETED
concurrent.futures.BrokenExecutor(??)
concurrent.futures.CancelledError(??)
concurrent.futures.Executor(??)
concurrent.futures.FIRST_COMPLETED
concurrent.futures.FIRST_EXCEPTION
concurrent.futures.Future(??)
concurrent.futures.ProcessPoolExecutor(??)
concurrent.futures.ThreadPoolExecutor(??)
concurrent.futures.TimeoutError(??)
concurrent.futures.as_completed(??)
concurrent.futures.process.BrokenProcessPool(??)
concurrent.futures.process.EXTRA_QUEUED_CALLS
concurrent.futures.process.ProcessPoolExecutor(??)
concurrent.futures.process.Queue(??)
concurrent.futures.process.format_exception(??)
concurrent.futures.process.itertools(??)
concurrent.futures.process.mp(??)
concurrent.futures.process.multiprocessing(??)
concurrent.futures.process.os(??)
concurrent.futures.process.partial(func, *args, **keywords) - new function with partial application
concurrent.futures.process.queue(??)
concurrent.futures.process.sys(??)
concurrent.futures.process.threading(??)
concurrent.futures.process.weakref(??)
concurrent.futures.thread.BrokenThreadPool(??)
concurrent.futures.thread.ThreadPoolExecutor(??)
concurrent.futures.thread.itertools(??)
concurrent.futures.thread.os(??)
concurrent.futures.thread.queue(??)
concurrent.futures.thread.threading(??)
concurrent.futures.thread.types(??)
concurrent.futures.thread.weakref(??)
concurrent.futures.wait(??)
configparser.BasicInterpolation(??)
configparser.ConfigParser(??)
configparser.ConverterMapping(??)
configparser.DEFAULTSECT
configparser.DuplicateOptionError(??)
configparser.DuplicateSectionError(??)
configparser.Error(??)
configparser.ExtendedInterpolation(??)
configparser.Interpolation(??)
configparser.InterpolationDepthError(??)
configparser.InterpolationError(??)
configparser.InterpolationMissingOptionError(??)
configparser.InterpolationSyntaxError(??)
configparser.LegacyInterpolation(??)
configparser.MAX_INTERPOLATION_DEPTH
configparser.MissingSectionHeaderError(??)
configparser.MutableMapping(??)
configparser.NoOptionError(??)
configparser.NoSectionError(??)
configparser.ParsingError(??)
configparser.RawConfigParser(??)
configparser.SafeConfigParser(??)
configparser.SectionProxy(??)
configparser.functools(??)
configparser.io(??)
configparser.itertools(??)
configparser.os(??)
configparser.re(??)
configparser.sys(??)
configparser.warnings(??)
contextlib.AbstractAsyncContextManager(??)
contextlib.AbstractContextManager(??)
contextlib.AsyncContextDecorator(??)
contextlib.AsyncExitStack(??)
contextlib.ContextDecorator(??)
contextlib.ExitStack(??)
contextlib.GenericAlias(??)
contextlib.MethodType(??)
contextlib.abc(??)
contextlib.aclosing(??)
contextlib.asynccontextmanager(??)
contextlib.chdir(??)
contextlib.closing(??)
contextlib.contextmanager(??)
contextlib.deque([iterable[, maxlen]]) --> deque object
contextlib.nullcontext(??)
contextlib.os(??)
contextlib.redirect_stderr(??)
contextlib.redirect_stdout(??)
contextlib.suppress(??)
contextlib.sys(??)
contextlib.wraps(??)
contextvars.Context(??)
contextvars.ContextVar(??)
contextvars.Token(??)
contextvars.copy_context(??)
copy.Error(??)
copy.copy(??)
copy.deepcopy(??)
copy.dispatch_table(??)
copy.error(??)
copyreg.add_extension(??)
copyreg.clear_extension_cache(??)
copyreg.constructor(??)
copyreg.dispatch_table(??)
copyreg.pickle(??)
copyreg.pickle_complex(??)
copyreg.pickle_union(??)
copyreg.remove_extension(??)
copyright(??)
credits(??)
crypt.METHOD_BLOWFISH(??)
crypt.METHOD_CRYPT(??)
crypt.METHOD_MD5(??)
crypt.METHOD_SHA256(??)
crypt.METHOD_SHA512(??)
crypt.crypt(??)
crypt.errno(??)
crypt.methods(??)
crypt.mksalt(??)
crypt.warnings(??)
csv.Dialect(??)
csv.DictReader(??)
csv.DictWriter(??)
csv.Error(??)
csv.QUOTE_ALL
csv.QUOTE_MINIMAL
csv.QUOTE_NONE
csv.QUOTE_NONNUMERIC
csv.Sniffer(??)
csv.StringIO(??)
csv.excel(??)
csv.excel_tab(??)
csv.field_size_limit(??)
csv.get_dialect(??)
csv.list_dialects(??)
csv.re(??)
csv.reader(??)
csv.register_dialect(??)
csv.unix_dialect(??)
csv.unregister_dialect(??)
csv.writer(??)
ctypes.ARRAY(??)
ctypes.ArgumentError(??)
ctypes.Array(??)
ctypes.BigEndianStructure(??)
ctypes.BigEndianUnion(??)
ctypes.CDLL(??)
ctypes.CFUNCTYPE(??)
ctypes.DEFAULT_MODE
ctypes.LibraryLoader(??)
ctypes.LittleEndianStructure(??)
ctypes.LittleEndianUnion(??)
ctypes.POINTER(??)
ctypes.PYFUNCTYPE(??)
ctypes.PyDLL(??)
ctypes.RTLD_GLOBAL
ctypes.RTLD_LOCAL
ctypes.SetPointerType(??)
ctypes.Structure(??)
ctypes.Union(??)
ctypes.addressof(C instance) -> integer
ctypes.alignment(C type) -> integer
ctypes.byref(C instance[, offset=0]) -> byref-object
ctypes.c_bool(??)
ctypes.c_buffer(??)
ctypes.c_byte(??)
ctypes.c_char(??)
ctypes.c_char_p(??)
ctypes.c_double(??)
ctypes.c_float(??)
ctypes.c_int(??)
ctypes.c_int16(??)
ctypes.c_int32(??)
ctypes.c_int64(??)
ctypes.c_int8(??)
ctypes.c_long(??)
ctypes.c_longdouble(??)
ctypes.c_longlong(??)
ctypes.c_short(??)
ctypes.c_size_t(??)
ctypes.c_ssize_t(??)
ctypes.c_ubyte(??)
ctypes.c_uint(??)
ctypes.c_uint16(??)
ctypes.c_uint32(??)
ctypes.c_uint64(??)
ctypes.c_uint8(??)
ctypes.c_ulong(??)
ctypes.c_ulonglong(??)
ctypes.c_ushort(??)
ctypes.c_void_p(??)
ctypes.c_voidp(??)
ctypes.c_wchar(??)
ctypes.c_wchar_p(??)
ctypes.cast(??)
ctypes.cdll(??)
ctypes.create_string_buffer(aBytes) -> character array
ctypes.create_unicode_buffer(aString) -> character array
ctypes.get_errno(??)
ctypes.macholib(??)
ctypes.macholib.dyld(??)
ctypes.macholib.dyld.DEFAULT_FRAMEWORK_FALLBACK(??)
ctypes.macholib.dyld.DEFAULT_LIBRARY_FALLBACK(??)
ctypes.macholib.dyld.accumulate(??)
ctypes.macholib.dyld.chain(*iterables) --> chain object
ctypes.macholib.dyld.combinations(??)
ctypes.macholib.dyld.combinations_with_replacement(??)
ctypes.macholib.dyld.compress(??)
ctypes.macholib.dyld.count(??)
ctypes.macholib.dyld.cycle(??)
ctypes.macholib.dyld.dropwhile(??)
ctypes.macholib.dyld.dyld_default_search(??)
ctypes.macholib.dyld.dyld_env(??)
ctypes.macholib.dyld.dyld_executable_path_search(??)
ctypes.macholib.dyld.dyld_fallback_framework_path(??)
ctypes.macholib.dyld.dyld_fallback_library_path(??)
ctypes.macholib.dyld.dyld_find(??)
ctypes.macholib.dyld.dyld_framework_path(??)
ctypes.macholib.dyld.dyld_image_suffix(??)
ctypes.macholib.dyld.dyld_image_suffix_search(??)
ctypes.macholib.dyld.dyld_library_path(??)
ctypes.macholib.dyld.dyld_override_search(??)
ctypes.macholib.dyld.dylib_info(??)
ctypes.macholib.dyld.filterfalse(??)
ctypes.macholib.dyld.framework_find(??)
ctypes.macholib.dyld.framework_info(??)
ctypes.macholib.dyld.groupby(??)
ctypes.macholib.dyld.islice(iterable, stop) --> islice object
ctypes.macholib.dyld.os(??)
ctypes.macholib.dyld.pairwise(??)
ctypes.macholib.dyld.permutations(??)
ctypes.macholib.dyld.product(*iterables, repeat=1) --> product object
ctypes.macholib.dyld.repeat(object [,times]) -> create an iterator which returns the object
ctypes.macholib.dyld.starmap(??)
ctypes.macholib.dyld.takewhile(??)
ctypes.macholib.dyld.tee(??)
ctypes.macholib.dyld.zip_longest(iter1 [,iter2 [...]], [fillvalue=None]) --> zip_longest object
ctypes.macholib.dylib(??)
ctypes.macholib.dylib.DYLIB_RE(??)
ctypes.macholib.dylib.dylib_info(??)
ctypes.macholib.dylib.re(??)
ctypes.macholib.framework(??)
ctypes.macholib.framework.STRICT_FRAMEWORK_RE(??)
ctypes.macholib.framework.framework_info(??)
ctypes.macholib.framework.re(??)
ctypes.memmove(??)
ctypes.memset(??)
ctypes.pointer(??)
ctypes.py_object(??)
ctypes.pydll(??)
ctypes.pythonapi(??)
ctypes.resize(??)
ctypes.set_errno(??)
ctypes.sizeof(C type) -> integer
ctypes.string_at(addr[, size]) -> string
ctypes.util(??)
ctypes.util.find_library(??)
ctypes.util.os(??)
ctypes.util.shutil(??)
ctypes.util.subprocess(??)
ctypes.util.sys(??)
ctypes.util.test(??)
ctypes.wintypes(??)
ctypes.wintypes.ATOM(??)
ctypes.wintypes.BOOL(??)
ctypes.wintypes.BOOLEAN(??)
ctypes.wintypes.BYTE(??)
ctypes.wintypes.CHAR(??)
ctypes.wintypes.COLORREF(??)
ctypes.wintypes.DOUBLE(??)
ctypes.wintypes.DWORD(??)
ctypes.wintypes.FILETIME(??)
ctypes.wintypes.FLOAT(??)
ctypes.wintypes.HACCEL(??)
ctypes.wintypes.HANDLE(??)
ctypes.wintypes.HBITMAP(??)
ctypes.wintypes.HBRUSH(??)
ctypes.wintypes.HCOLORSPACE(??)
ctypes.wintypes.HDC(??)
ctypes.wintypes.HDESK(??)
ctypes.wintypes.HDWP(??)
ctypes.wintypes.HENHMETAFILE(??)
ctypes.wintypes.HFONT(??)
ctypes.wintypes.HGDIOBJ(??)
ctypes.wintypes.HGLOBAL(??)
ctypes.wintypes.HHOOK(??)
ctypes.wintypes.HICON(??)
ctypes.wintypes.HINSTANCE(??)
ctypes.wintypes.HKEY(??)
ctypes.wintypes.HKL(??)
ctypes.wintypes.HLOCAL(??)
ctypes.wintypes.HMENU(??)
ctypes.wintypes.HMETAFILE(??)
ctypes.wintypes.HMODULE(??)
ctypes.wintypes.HMONITOR(??)
ctypes.wintypes.HPALETTE(??)
ctypes.wintypes.HPEN(??)
ctypes.wintypes.HRGN(??)
ctypes.wintypes.HRSRC(??)
ctypes.wintypes.HSTR(??)
ctypes.wintypes.HTASK(??)
ctypes.wintypes.HWINSTA(??)
ctypes.wintypes.HWND(??)
ctypes.wintypes.INT(??)
ctypes.wintypes.LANGID(??)
ctypes.wintypes.LARGE_INTEGER(??)
ctypes.wintypes.LCID(??)
ctypes.wintypes.LCTYPE(??)
ctypes.wintypes.LGRPID(??)
ctypes.wintypes.LONG(??)
ctypes.wintypes.LPARAM(??)
ctypes.wintypes.LPBOOL(??)
ctypes.wintypes.LPBYTE(??)
ctypes.wintypes.LPCOLESTR(??)
ctypes.wintypes.LPCOLORREF(??)
ctypes.wintypes.LPCSTR(??)
ctypes.wintypes.LPCVOID(??)
ctypes.wintypes.LPCWSTR(??)
ctypes.wintypes.LPDWORD(??)
ctypes.wintypes.LPFILETIME(??)
ctypes.wintypes.LPHANDLE(??)
ctypes.wintypes.LPHKL(??)
ctypes.wintypes.LPINT(??)
ctypes.wintypes.LPLONG(??)
ctypes.wintypes.LPMSG(??)
ctypes.wintypes.LPOLESTR(??)
ctypes.wintypes.LPPOINT(??)
ctypes.wintypes.LPRECT(??)
ctypes.wintypes.LPRECTL(??)
ctypes.wintypes.LPSC_HANDLE(??)
ctypes.wintypes.LPSIZE(??)
ctypes.wintypes.LPSIZEL(??)
ctypes.wintypes.LPSTR(??)
ctypes.wintypes.LPUINT(??)
ctypes.wintypes.LPVOID(??)
ctypes.wintypes.LPWIN32_FIND_DATAA(??)
ctypes.wintypes.LPWIN32_FIND_DATAW(??)
ctypes.wintypes.LPWORD(??)
ctypes.wintypes.LPWSTR(??)
ctypes.wintypes.MAX_PATH
ctypes.wintypes.MSG(??)
ctypes.wintypes.OLESTR(??)
ctypes.wintypes.PBOOL(??)
ctypes.wintypes.PBOOLEAN(??)
ctypes.wintypes.PBYTE(??)
ctypes.wintypes.PCHAR(??)
ctypes.wintypes.PDWORD(??)
ctypes.wintypes.PFILETIME(??)
ctypes.wintypes.PFLOAT(??)
ctypes.wintypes.PHANDLE(??)
ctypes.wintypes.PHKEY(??)
ctypes.wintypes.PINT(??)
ctypes.wintypes.PLARGE_INTEGER(??)
ctypes.wintypes.PLCID(??)
ctypes.wintypes.PLONG(??)
ctypes.wintypes.PMSG(??)
ctypes.wintypes.POINT(??)
ctypes.wintypes.POINTL(??)
ctypes.wintypes.PPOINT(??)
ctypes.wintypes.PPOINTL(??)
ctypes.wintypes.PRECT(??)
ctypes.wintypes.PRECTL(??)
ctypes.wintypes.PSHORT(??)
ctypes.wintypes.PSIZE(??)
ctypes.wintypes.PSIZEL(??)
ctypes.wintypes.PSMALL_RECT(??)
ctypes.wintypes.PUINT(??)
ctypes.wintypes.PULARGE_INTEGER(??)
ctypes.wintypes.PULONG(??)
ctypes.wintypes.PUSHORT(??)
ctypes.wintypes.PWCHAR(??)
ctypes.wintypes.PWIN32_FIND_DATAA(??)
ctypes.wintypes.PWIN32_FIND_DATAW(??)
ctypes.wintypes.PWORD(??)
ctypes.wintypes.RECT(??)
ctypes.wintypes.RECTL(??)
ctypes.wintypes.RGB(??)
ctypes.wintypes.SC_HANDLE(??)
ctypes.wintypes.SERVICE_STATUS_HANDLE(??)
ctypes.wintypes.SHORT(??)
ctypes.wintypes.SIZE(??)
ctypes.wintypes.SIZEL(??)
ctypes.wintypes.SMALL_RECT(??)
ctypes.wintypes.UINT(??)
ctypes.wintypes.ULARGE_INTEGER(??)
ctypes.wintypes.ULONG(??)
ctypes.wintypes.USHORT(??)
ctypes.wintypes.VARIANT_BOOL(??)
ctypes.wintypes.WCHAR(??)
ctypes.wintypes.WIN32_FIND_DATAA(??)
ctypes.wintypes.WIN32_FIND_DATAW(??)
ctypes.wintypes.WORD(??)
ctypes.wintypes.WPARAM(??)
ctypes.wintypes.ctypes(??)
ctypes.wintypes.tagMSG(??)
ctypes.wintypes.tagPOINT(??)
ctypes.wintypes.tagRECT(??)
ctypes.wintypes.tagSIZE(??)
ctypes.wstring_at(addr[, size]) -> string
curses.ALL_MOUSE_EVENTS
curses.A_ALTCHARSET
curses.A_ATTRIBUTES
curses.A_BLINK
curses.A_BOLD
curses.A_CHARTEXT
curses.A_COLOR
curses.A_DIM
curses.A_HORIZONTAL
curses.A_INVIS
curses.A_LEFT
curses.A_LOW
curses.A_NORMAL
curses.A_PROTECT
curses.A_REVERSE
curses.A_RIGHT
curses.A_STANDOUT
curses.A_TOP
curses.A_UNDERLINE
curses.A_VERTICAL
curses.BUTTON1_CLICKED
curses.BUTTON1_DOUBLE_CLICKED
curses.BUTTON1_PRESSED
curses.BUTTON1_RELEASED
curses.BUTTON1_TRIPLE_CLICKED
curses.BUTTON2_CLICKED
curses.BUTTON2_DOUBLE_CLICKED
curses.BUTTON2_PRESSED
curses.BUTTON2_RELEASED
curses.BUTTON2_TRIPLE_CLICKED
curses.BUTTON3_CLICKED
curses.BUTTON3_DOUBLE_CLICKED
curses.BUTTON3_PRESSED
curses.BUTTON3_RELEASED
curses.BUTTON3_TRIPLE_CLICKED
curses.BUTTON4_CLICKED
curses.BUTTON4_DOUBLE_CLICKED
curses.BUTTON4_PRESSED
curses.BUTTON4_RELEASED
curses.BUTTON4_TRIPLE_CLICKED
curses.BUTTON_ALT
curses.BUTTON_CTRL
curses.BUTTON_SHIFT
curses.COLOR_BLACK
curses.COLOR_BLUE
curses.COLOR_CYAN
curses.COLOR_GREEN
curses.COLOR_MAGENTA
curses.COLOR_RED
curses.COLOR_WHITE
curses.COLOR_YELLOW
curses.ERR
curses.KEY_A1
curses.KEY_A3
curses.KEY_B2
curses.KEY_BACKSPACE
curses.KEY_BEG
curses.KEY_BREAK
curses.KEY_BTAB
curses.KEY_C1
curses.KEY_C3
curses.KEY_CANCEL
curses.KEY_CATAB
curses.KEY_CLEAR
curses.KEY_CLOSE
curses.KEY_COMMAND
curses.KEY_COPY
curses.KEY_CREATE
curses.KEY_CTAB
curses.KEY_DC
curses.KEY_DL
curses.KEY_DOWN
curses.KEY_EIC
curses.KEY_END
curses.KEY_ENTER
curses.KEY_EOL
curses.KEY_EOS
curses.KEY_EXIT
curses.KEY_F0
curses.KEY_F1
curses.KEY_F10
curses.KEY_F11
curses.KEY_F12
curses.KEY_F13
curses.KEY_F14
curses.KEY_F15
curses.KEY_F16
curses.KEY_F17
curses.KEY_F18
curses.KEY_F19
curses.KEY_F2
curses.KEY_F20
curses.KEY_F21
curses.KEY_F22
curses.KEY_F23
curses.KEY_F24
curses.KEY_F25
curses.KEY_F26
curses.KEY_F27
curses.KEY_F28
curses.KEY_F29
curses.KEY_F3
curses.KEY_F30
curses.KEY_F31
curses.KEY_F32
curses.KEY_F33
curses.KEY_F34
curses.KEY_F35
curses.KEY_F36
curses.KEY_F37
curses.KEY_F38
curses.KEY_F39
curses.KEY_F4
curses.KEY_F40
curses.KEY_F41
curses.KEY_F42
curses.KEY_F43
curses.KEY_F44
curses.KEY_F45
curses.KEY_F46
curses.KEY_F47
curses.KEY_F48
curses.KEY_F49
curses.KEY_F5
curses.KEY_F50
curses.KEY_F51
curses.KEY_F52
curses.KEY_F53
curses.KEY_F54
curses.KEY_F55
curses.KEY_F56
curses.KEY_F57
curses.KEY_F58
curses.KEY_F59
curses.KEY_F6
curses.KEY_F60
curses.KEY_F61
curses.KEY_F62
curses.KEY_F63
curses.KEY_F7
curses.KEY_F8
curses.KEY_F9
curses.KEY_FIND
curses.KEY_HELP
curses.KEY_HOME
curses.KEY_IC
curses.KEY_IL
curses.KEY_LEFT
curses.KEY_LL
curses.KEY_MARK
curses.KEY_MAX
curses.KEY_MESSAGE
curses.KEY_MIN
curses.KEY_MOUSE
curses.KEY_MOVE
curses.KEY_NEXT
curses.KEY_NPAGE
curses.KEY_OPEN
curses.KEY_OPTIONS
curses.KEY_PPAGE
curses.KEY_PREVIOUS
curses.KEY_PRINT
curses.KEY_REDO
curses.KEY_REFERENCE
curses.KEY_REFRESH
curses.KEY_REPLACE
curses.KEY_RESET
curses.KEY_RESIZE
curses.KEY_RESTART
curses.KEY_RESUME
curses.KEY_RIGHT
curses.KEY_SAVE
curses.KEY_SBEG
curses.KEY_SCANCEL
curses.KEY_SCOMMAND
curses.KEY_SCOPY
curses.KEY_SCREATE
curses.KEY_SDC
curses.KEY_SDL
curses.KEY_SELECT
curses.KEY_SEND
curses.KEY_SEOL
curses.KEY_SEXIT
curses.KEY_SF
curses.KEY_SFIND
curses.KEY_SHELP
curses.KEY_SHOME
curses.KEY_SIC
curses.KEY_SLEFT
curses.KEY_SMESSAGE
curses.KEY_SMOVE
curses.KEY_SNEXT
curses.KEY_SOPTIONS
curses.KEY_SPREVIOUS
curses.KEY_SPRINT
curses.KEY_SR
curses.KEY_SREDO
curses.KEY_SREPLACE
curses.KEY_SRESET
curses.KEY_SRIGHT
curses.KEY_SRSUME
curses.KEY_SSAVE
curses.KEY_SSUSPEND
curses.KEY_STAB
curses.KEY_SUNDO
curses.KEY_SUSPEND
curses.KEY_UNDO
curses.KEY_UP
curses.OK
curses.REPORT_MOUSE_POSITION
curses.ascii(??)
curses.ascii.ACK
curses.ascii.BEL
curses.ascii.BS
curses.ascii.CAN
curses.ascii.CR
curses.ascii.DC1
curses.ascii.DC2
curses.ascii.DC3
curses.ascii.DC4
curses.ascii.DEL
curses.ascii.DLE
curses.ascii.EM
curses.ascii.ENQ
curses.ascii.EOT
curses.ascii.ESC
curses.ascii.ETB
curses.ascii.ETX
curses.ascii.FF
curses.ascii.FS
curses.ascii.GS
curses.ascii.HT
curses.ascii.LF
curses.ascii.NAK
curses.ascii.NL
curses.ascii.NUL
curses.ascii.RS
curses.ascii.SI
curses.ascii.SO
curses.ascii.SOH
curses.ascii.SP
curses.ascii.STX
curses.ascii.SUB
curses.ascii.SYN
curses.ascii.TAB
curses.ascii.US
curses.ascii.VT
curses.ascii.alt(??)
curses.ascii.ascii(??)
curses.ascii.controlnames(??)
curses.ascii.ctrl(??)
curses.ascii.isalnum(??)
curses.ascii.isalpha(??)
curses.ascii.isascii(??)
curses.ascii.isblank(??)
curses.ascii.iscntrl(??)
curses.ascii.isctrl(??)
curses.ascii.isdigit(??)
curses.ascii.isgraph(??)
curses.ascii.islower(??)
curses.ascii.ismeta(??)
curses.ascii.isprint(??)
curses.ascii.ispunct(??)
curses.ascii.isspace(??)
curses.ascii.isupper(??)
curses.ascii.isxdigit(??)
curses.ascii.unctrl(??)
curses.baudrate(??)
curses.beep(??)
curses.can_change_color(??)
curses.cbreak(??)
curses.color_content(??)
curses.color_pair(??)
curses.curs_set(??)
curses.def_prog_mode(??)
curses.def_shell_mode(??)
curses.delay_output(??)
curses.doupdate(??)
curses.echo(??)
curses.endwin(??)
curses.erasechar(??)
curses.error(??)
curses.filter(??)
curses.flash(??)
curses.flushinp(??)
curses.get_escdelay(??)
curses.get_tabsize(??)
curses.getmouse(??)
curses.getsyx(??)
curses.getwin(??)
curses.halfdelay(??)
curses.has_colors(??)
curses.has_extended_color_support(??)
curses.has_ic(??)
curses.has_il(??)
curses.has_key(??)
curses.has_key.has_key(??)
curses.init_color(??)
curses.init_pair(??)
curses.initscr(??)
curses.intrflush(??)
curses.is_term_resized(??)
curses.isendwin(??)
curses.keyname(??)
curses.killchar(??)
curses.longname(??)
curses.meta(??)
curses.mouseinterval(??)
curses.mousemask(??)
curses.napms(??)
curses.ncurses_version(??)
curses.newpad(??)
curses.newwin(nlines, ncols, [begin_y=0, begin_x=0])
curses.nl(??)
curses.nocbreak(??)
curses.noecho(??)
curses.nonl(??)
curses.noqiflush(??)
curses.noraw(??)
curses.pair_content(??)
curses.pair_number(??)
curses.panel.bottom_panel(??)
curses.panel.error(??)
curses.panel.new_panel(??)
curses.panel.panel(??)
curses.panel.top_panel(??)
curses.panel.update_panels(??)
curses.panel.version
curses.putp(??)
curses.qiflush(??)
curses.raw(??)
curses.reset_prog_mode(??)
curses.reset_shell_mode(??)
curses.resetty(??)
curses.resize_term(??)
curses.resizeterm(??)
curses.savetty(??)
curses.set_escdelay(??)
curses.set_tabsize(??)
curses.setsyx(??)
curses.setupterm(??)
curses.start_color(??)
curses.termattrs(??)
curses.termname(??)
curses.textpad(??)
curses.textpad.Textbox(??)
curses.textpad.curses(??)
curses.textpad.rectangle(??)
curses.tigetflag(??)
curses.tigetnum(??)
curses.tigetstr(??)
curses.tparm(??)
curses.typeahead(??)
curses.unctrl(??)
curses.unget_wch(??)
curses.ungetch(??)
curses.ungetmouse(??)
curses.update_lines_cols(??)
curses.use_default_colors(??)
curses.use_env(??)
curses.version
curses.window(??)
curses.wrapper(??)
dataclasses.Field(??)
dataclasses.FrozenInstanceError(??)
dataclasses.FunctionType(??)
dataclasses.GenericAlias(??)
dataclasses.InitVar(??)
dataclasses.KW_ONLY(??)
dataclasses.MISSING(??)
dataclasses.abc(??)
dataclasses.asdict(??)
dataclasses.astuple(??)
dataclasses.builtins(??)
dataclasses.copy(??)
dataclasses.dataclass(??)
dataclasses.field(??)
dataclasses.fields(??)
dataclasses.functools(??)
dataclasses.inspect(??)
dataclasses.is_dataclass(??)
dataclasses.itertools(??)
dataclasses.keyword(??)
dataclasses.make_dataclass(??)
dataclasses.re(??)
dataclasses.replace(??)
dataclasses.sys(??)
dataclasses.types(??)
datetime.MAXYEAR
datetime.MINYEAR
datetime.UTC(??)
datetime.date(year, month, day) --> date object
datetime.datetime(year, month, day[, hour[, minute[, second[, microsecond[,tzinfo]]]]])
datetime.datetime_CAPI(??)
datetime.sys(??)
datetime.time([hour[, minute[, second[, microsecond[, tzinfo]]]]]) --> a time object
datetime.timedelta(??)
datetime.timezone(??)
datetime.tzinfo(??)
dbm.dumb.collections(??)
dbm.dumb.error(??)
dbm.dumb.open(??)
dbm.error(??)
dbm.io(??)
dbm.ndbm(??)
dbm.ndbm.error(??)
dbm.ndbm.library
dbm.ndbm.open(??)
dbm.open(??)
dbm.os(??)
dbm.struct(??)
dbm.sys(??)
dbm.whichdb(??)
decimal.BasicContext(??)
decimal.Clamped(??)
decimal.Context(??)
decimal.ConversionSyntax(??)
decimal.Decimal(??)
decimal.DecimalException(??)
decimal.DecimalTuple(sign, digits, exponent)
decimal.DefaultContext(??)
decimal.DivisionByZero(??)
decimal.DivisionImpossible(??)
decimal.DivisionUndefined(??)
decimal.ExtendedContext(??)
decimal.FloatOperation(??)
decimal.HAVE_CONTEXTVAR(??)
decimal.HAVE_THREADS(??)
decimal.Inexact(??)
decimal.InvalidContext(??)
decimal.InvalidOperation(??)
decimal.MAX_EMAX
decimal.MAX_PREC
decimal.MIN_EMIN
decimal.MIN_ETINY
decimal.Overflow(??)
decimal.ROUND_05UP
decimal.ROUND_CEILING
decimal.ROUND_DOWN
decimal.ROUND_FLOOR
decimal.ROUND_HALF_DOWN
decimal.ROUND_HALF_EVEN
decimal.ROUND_HALF_UP
decimal.ROUND_UP
decimal.Rounded(??)
decimal.Subnormal(??)
decimal.Underflow(??)
decimal.getcontext(??)
decimal.localcontext(??)
decimal.setcontext(??)
delattr(??)
dict() -> new empty dictionary
difflib.Differ(??)
difflib.GenericAlias(??)
difflib.HtmlDiff(??)
difflib.IS_CHARACTER_JUNK(??)
difflib.IS_LINE_JUNK(??)
difflib.Match(a, b, size)
difflib.SequenceMatcher(??)
difflib.context_diff(??)
difflib.diff_bytes(??)
difflib.get_close_matches(??)
difflib.ndiff(??)
difflib.restore(??)
difflib.unified_diff(??)
dir([object]) -> list of strings
dis.BINARY_OP
dis.Bytecode(??)
dis.CACHE
dis.COMPILER_FLAG_NAMES(??)
dis.EXTENDED_ARG
dis.FORMAT_VALUE
dis.FORMAT_VALUE_CONVERTERS(??)
dis.HAVE_ARGUMENT
dis.Instruction(??)
dis.JUMP_BACKWARD
dis.LOAD_CONST
dis.LOAD_GLOBAL
dis.MAKE_FUNCTION
dis.MAKE_FUNCTION_FLAGS(??)
dis.Positions(lineno, end_lineno, col_offset, end_col_offset)
dis.UNKNOWN(??)
dis.cmp_op(??)
dis.code_info(??)
dis.collections(??)
dis.deoptmap(??)
dis.dis(??)
dis.disassemble(??)
dis.disco(??)
dis.distb(??)
dis.findlabels(??)
dis.findlinestarts(??)
dis.get_instructions(??)
dis.hascompare(??)
dis.hasconst(??)
dis.hasfree(??)
dis.hasjabs(??)
dis.hasjrel(??)
dis.haslocal(??)
dis.hasname(??)
dis.hasnargs(??)
dis.io(??)
dis.opmap(??)
dis.opname(??)
dis.parse_exception_table(??)
dis.parse_varint(??)
dis.pretty_flags(??)
dis.show_code(??)
dis.spec_op
dis.specialized
dis.stack_effect(??)
dis.sys(??)
dis.types(??)
divmod(??)
doctest.BLANKLINE_MARKER
doctest.COMPARISON_FLAGS
doctest.DONT_ACCEPT_BLANKLINE
doctest.DONT_ACCEPT_TRUE_FOR_1
doctest.DebugRunner(??)
doctest.DocFileCase(??)
doctest.DocFileSuite(??)
doctest.DocFileTest(??)
doctest.DocTest(??)
doctest.DocTestCase(??)
doctest.DocTestFailure(??)
doctest.DocTestFinder(??)
doctest.DocTestParser(??)
doctest.DocTestRunner(??)
doctest.DocTestSuite(??)
doctest.ELLIPSIS
doctest.ELLIPSIS_MARKER
doctest.Example(??)
doctest.FAIL_FAST
doctest.IGNORE_EXCEPTION_DETAIL
doctest.IncrementalNewlineDecoder(??)
doctest.NORMALIZE_WHITESPACE
doctest.OPTIONFLAGS_BY_NAME(??)
doctest.OutputChecker(??)
doctest.REPORTING_FLAGS
doctest.REPORT_CDIFF
doctest.REPORT_NDIFF
doctest.REPORT_ONLY_FIRST_FAILURE
doctest.REPORT_UDIFF
doctest.SKIP
doctest.SkipDocTestCase(??)
doctest.StringIO(??)
doctest.TestResults(failed, attempted)
doctest.UnexpectedException(??)
doctest.debug(??)
doctest.debug_script(??)
doctest.debug_src(??)
doctest.difflib(??)
doctest.inspect(??)
doctest.linecache(??)
doctest.master(??)
doctest.namedtuple(??)
doctest.os(??)
doctest.pdb(??)
doctest.re(??)
doctest.register_optionflag(??)
doctest.run_docstring_examples(??)
doctest.script_from_examples(??)
doctest.set_unittest_reportflags(??)
doctest.sys(??)
doctest.testfile(??)
doctest.testmod(??)
doctest.testsource(??)
doctest.traceback(??)
doctest.unittest(??)
email.base64mime(??)
email.base64mime.CRLF
email.base64mime.EMPTYSTRING
email.base64mime.MISC_LEN
email.base64mime.NL
email.base64mime.a2b_base64(??)
email.base64mime.b2a_base64(??)
email.base64mime.b64encode(??)
email.base64mime.body_decode(??)
email.base64mime.body_encode(??)
email.base64mime.decode(??)
email.base64mime.decodestring(??)
email.base64mime.header_encode(??)
email.base64mime.header_length(??)
email.charset(??)
email.charset.ALIASES(??)
email.charset.BASE64
email.charset.CHARSETS(??)
email.charset.CODEC_MAP(??)
email.charset.Charset(??)
email.charset.DEFAULT_CHARSET
email.charset.EMPTYSTRING
email.charset.QP
email.charset.RFC2047_CHROME_LEN
email.charset.SHORTEST
email.charset.UNKNOWN8BIT
email.charset.add_alias(??)
email.charset.add_charset(??)
email.charset.add_codec(??)
email.charset.email(??)
email.charset.encode_7or8bit(??)
email.charset.errors(??)
email.charset.partial(func, *args, **keywords) - new function with partial application
email.contentmanager(??)
email.contentmanager.ContentManager(??)
email.contentmanager.binascii(??)
email.contentmanager.email(??)
email.contentmanager.get_and_fixup_unknown_message_content(??)
email.contentmanager.get_message_content(??)
email.contentmanager.get_non_text_content(??)
email.contentmanager.get_text_content(??)
email.contentmanager.quoprimime(??)
email.contentmanager.raw_data_manager(??)
email.contentmanager.set_bytes_content(??)
email.contentmanager.set_message_content(??)
email.contentmanager.set_text_content(??)
email.encoders(??)
email.encoders.encode_7or8bit(??)
email.encoders.encode_base64(??)
email.encoders.encode_noop(??)
email.encoders.encode_quopri(??)
email.errors(??)
email.errors.BoundaryError(??)
email.errors.CharsetError(??)
email.errors.CloseBoundaryNotFoundDefect(??)
email.errors.FirstHeaderLineIsContinuationDefect(??)
email.errors.HeaderDefect(??)
email.errors.HeaderMissingRequiredValue(??)
email.errors.HeaderParseError(??)
email.errors.InvalidBase64CharactersDefect(??)
email.errors.InvalidBase64LengthDefect(??)
email.errors.InvalidBase64PaddingDefect(??)
email.errors.InvalidDateDefect(??)
email.errors.InvalidHeaderDefect(??)
email.errors.InvalidMultipartContentTransferEncodingDefect(??)
email.errors.MalformedHeaderDefect(??)
email.errors.MessageDefect(??)
email.errors.MessageError(??)
email.errors.MessageParseError(??)
email.errors.MisplacedEnvelopeHeaderDefect(??)
email.errors.MissingHeaderBodySeparatorDefect(??)
email.errors.MultipartConversionError(??)
email.errors.MultipartInvariantViolationDefect(??)
email.errors.NoBoundaryInMultipartDefect(??)
email.errors.NonASCIILocalPartDefect(??)
email.errors.NonPrintableDefect(??)
email.errors.ObsoleteHeaderDefect(??)
email.errors.StartBoundaryNotFoundDefect(??)
email.errors.UndecodableBytesDefect(??)
email.feedparser(??)
email.feedparser.BufferedSubFile(??)
email.feedparser.BytesFeedParser(??)
email.feedparser.EMPTYSTRING
email.feedparser.FeedParser(??)
email.feedparser.NL
email.feedparser.NLCRE(??)
email.feedparser.NLCRE_bol(??)
email.feedparser.NLCRE_crack(??)
email.feedparser.NLCRE_eol(??)
email.feedparser.NeedMoreData(??)
email.feedparser.StringIO(??)
email.feedparser.compat32(??)
email.feedparser.deque([iterable[, maxlen]]) --> deque object
email.feedparser.errors(??)
email.feedparser.headerRE(??)
email.feedparser.re(??)
email.generator(??)
email.generator.BytesGenerator(??)
email.generator.BytesIO(??)
email.generator.DecodedGenerator(??)
email.generator.Generator(??)
email.generator.NL
email.generator.NLCRE(??)
email.generator.StringIO(??)
email.generator.UNDERSCORE
email.generator.deepcopy(??)
email.generator.fcre(??)
email.generator.random(??)
email.generator.re(??)
email.generator.sys(??)
email.generator.time(??)
email.header(??)
email.header.BSPACE
email.header.Charset(??)
email.header.EMPTYSTRING
email.header.FWS
email.header.Header(??)
email.header.HeaderParseError(??)
email.header.MAXLINELEN
email.header.NL
email.header.SPACE
email.header.SPACE8
email.header.USASCII(??)
email.header.UTF8(??)
email.header.binascii(??)
email.header.decode_header(??)
email.header.ecre(??)
email.header.email(??)
email.header.fcre(??)
email.header.make_header(??)
email.header.re(??)
email.headerregistry(??)
email.headerregistry.Address(??)
email.headerregistry.AddressHeader(??)
email.headerregistry.BaseHeader(??)
email.headerregistry.ContentDispositionHeader(??)
email.headerregistry.ContentTransferEncodingHeader(??)
email.headerregistry.ContentTypeHeader(??)
email.headerregistry.DateHeader(??)
email.headerregistry.Group(??)
email.headerregistry.HeaderRegistry(??)
email.headerregistry.MIMEVersionHeader(??)
email.headerregistry.MappingProxyType(??)
email.headerregistry.MessageIDHeader(??)
email.headerregistry.ParameterizedMIMEHeader(??)
email.headerregistry.SingleAddressHeader(??)
email.headerregistry.UniqueAddressHeader(??)
email.headerregistry.UniqueDateHeader(??)
email.headerregistry.UniqueSingleAddressHeader(??)
email.headerregistry.UniqueUnstructuredHeader(??)
email.headerregistry.UnstructuredHeader(??)
email.headerregistry.errors(??)
email.headerregistry.parser(??)
email.headerregistry.utils(??)
email.iterators(??)
email.iterators.StringIO(??)
email.iterators.body_line_iterator(??)
email.iterators.sys(??)
email.iterators.typed_subpart_iterator(??)
email.iterators.walk(??)
email.message(??)
email.message.BytesIO(??)
email.message.Charset(??)
email.message.EmailMessage(??)
email.message.MIMEPart(??)
email.message.Message(??)
email.message.Policy(??)
email.message.SEMISPACE
email.message.StringIO(??)
email.message.binascii(??)
email.message.compat32(??)
email.message.decode_b(??)
email.message.errors(??)
email.message.quopri(??)
email.message.re(??)
email.message.tspecials(??)
email.message.utils(??)
email.message_from_binary_file(??)
email.message_from_bytes(??)
email.message_from_file(??)
email.message_from_string(??)
email.mime.application.MIMEApplication(??)
email.mime.application.MIMENonMultipart(??)
email.mime.application.encoders(??)
email.mime.audio.BytesIO(??)
email.mime.audio.MIMEAudio(??)
email.mime.audio.MIMENonMultipart(??)
email.mime.audio.encoders(??)
email.mime.audio.rule(??)
email.mime.base(??)
email.mime.base.MIMEBase(??)
email.mime.base.email(??)
email.mime.base.message(??)
email.mime.image.MIMEImage(??)
email.mime.image.MIMENonMultipart(??)
email.mime.image.encoders(??)
email.mime.image.rule(??)
email.mime.message.MIMEMessage(??)
email.mime.message.MIMENonMultipart(??)
email.mime.message.message(??)
email.mime.multipart(??)
email.mime.multipart.MIMEBase(??)
email.mime.multipart.MIMEMultipart(??)
email.mime.nonmultipart.MIMEBase(??)
email.mime.nonmultipart.MIMENonMultipart(??)
email.mime.nonmultipart.errors(??)
email.mime.text.Charset(??)
email.mime.text.MIMENonMultipart(??)
email.mime.text.MIMEText(??)
email.parser(??)
email.parser.BytesFeedParser(??)
email.parser.BytesHeaderParser(??)
email.parser.BytesParser(??)
email.parser.FeedParser(??)
email.parser.HeaderParser(??)
email.parser.Parser(??)
email.parser.StringIO(??)
email.parser.TextIOWrapper(??)
email.parser.compat32(??)
email.policy(??)
email.policy.Compat32(??)
email.policy.EmailMessage(??)
email.policy.EmailPolicy(??)
email.policy.HTTP(??)
email.policy.HeaderRegistry(??)
email.policy.Policy(??)
email.policy.SMTP(??)
email.policy.SMTPUTF8(??)
email.policy.compat32(??)
email.policy.default(??)
email.policy.linesep_splitter(??)
email.policy.raw_data_manager(??)
email.policy.re(??)
email.policy.strict(??)
email.policy.sys(??)
email.quoprimime(??)
email.quoprimime.CRLF
email.quoprimime.EMPTYSTRING
email.quoprimime.NL
email.quoprimime.ascii_letters
email.quoprimime.body_check(??)
email.quoprimime.body_decode(??)
email.quoprimime.body_encode(??)
email.quoprimime.body_length(??)
email.quoprimime.decode(??)
email.quoprimime.decodestring(??)
email.quoprimime.digits
email.quoprimime.header_check(??)
email.quoprimime.header_decode(??)
email.quoprimime.header_encode(??)
email.quoprimime.header_length(??)
email.quoprimime.hexdigits
email.quoprimime.quote(??)
email.quoprimime.re(??)
email.quoprimime.unquote(??)
email.utils(??)
email.utils.COMMASPACE
email.utils.CRLF
email.utils.Charset(??)
email.utils.EMPTYSTRING
email.utils.TICK
email.utils.UEMPTYSTRING
email.utils.collapse_rfc2231_value(??)
email.utils.datetime(??)
email.utils.decode_params(??)
email.utils.decode_rfc2231(??)
email.utils.encode_rfc2231(??)
email.utils.escapesre(??)
email.utils.format_datetime(??)
email.utils.formataddr(??)
email.utils.formatdate(??)
email.utils.getaddresses(??)
email.utils.localtime(??)
email.utils.make_msgid(??)
email.utils.mktime_tz(??)
email.utils.os(??)
email.utils.parseaddr(??)
email.utils.parsedate(??)
email.utils.parsedate_to_datetime(??)
email.utils.parsedate_tz(??)
email.utils.quote(??)
email.utils.random(??)
email.utils.re(??)
email.utils.rfc2231_continuation(??)
email.utils.socket(??)
email.utils.specialsre(??)
email.utils.time(??)
email.utils.unquote(??)
email.utils.urllib(??)
ensurepip.bootstrap(??)
ensurepip.collections(??)
ensurepip.os(??)
ensurepip.resources(??)
ensurepip.subprocess(??)
ensurepip.sys(??)
ensurepip.sysconfig(??)
ensurepip.tempfile(??)
ensurepip.version(??)
enum.CONFORM(??)
enum.CONTINUOUS(??)
enum.DynamicClassAttribute(??)
enum.EJECT(??)
enum.Enum(??)
enum.EnumCheck(??)
enum.EnumMeta(??)
enum.EnumType(??)
enum.Flag(??)
enum.FlagBoundary(??)
enum.IntEnum(??)
enum.IntFlag(??)
enum.KEEP(??)
enum.MappingProxyType(??)
enum.NAMED_FLAGS(??)
enum.ReprEnum(??)
enum.STRICT(??)
enum.StrEnum(??)
enum.UNIQUE(??)
enum.auto(??)
enum.bin(??)
enum.bltns(??)
enum.global_enum(??)
enum.global_enum_repr(??)
enum.global_flag_repr(??)
enum.global_str(??)
enum.member(??)
enum.nonmember(??)
enum.property(??)
enum.reduce(function, iterable[, initial]) -> value
enum.show_flag_values(??)
enum.sys(??)
enum.unique(??)
enum.verify(??)
enumerate(??)
errno.E2BIG
errno.EACCES
errno.EADDRINUSE
errno.EADDRNOTAVAIL
errno.EAFNOSUPPORT
errno.EAGAIN
errno.EALREADY
errno.EAUTH
errno.EBADARCH
errno.EBADEXEC
errno.EBADF
errno.EBADMACHO
errno.EBADMSG
errno.EBADRPC
errno.EBUSY
errno.ECANCELED
errno.ECHILD
errno.ECONNABORTED
errno.ECONNREFUSED
errno.ECONNRESET
errno.EDEADLK
errno.EDESTADDRREQ
errno.EDEVERR
errno.EDOM
errno.EDQUOT
errno.EEXIST
errno.EFAULT
errno.EFBIG
errno.EFTYPE
errno.EHOSTDOWN
errno.EHOSTUNREACH
errno.EIDRM
errno.EILSEQ
errno.EINPROGRESS
errno.EINTR
errno.EINVAL
errno.EIO
errno.EISCONN
errno.EISDIR
errno.ELOOP
errno.EMFILE
errno.EMLINK
errno.EMSGSIZE
errno.EMULTIHOP
errno.ENAMETOOLONG
errno.ENEEDAUTH
errno.ENETDOWN
errno.ENETRESET
errno.ENETUNREACH
errno.ENFILE
errno.ENOATTR
errno.ENOBUFS
errno.ENODATA
errno.ENODEV
errno.ENOENT
errno.ENOEXEC
errno.ENOLCK
errno.ENOLINK
errno.ENOMEM
errno.ENOMSG
errno.ENOPOLICY
errno.ENOPROTOOPT
errno.ENOSPC
errno.ENOSR
errno.ENOSTR
errno.ENOSYS
errno.ENOTBLK
errno.ENOTCONN
errno.ENOTDIR
errno.ENOTEMPTY
errno.ENOTRECOVERABLE
errno.ENOTSOCK
errno.ENOTSUP
errno.ENOTTY
errno.ENXIO
errno.EOPNOTSUPP
errno.EOVERFLOW
errno.EOWNERDEAD
errno.EPERM
errno.EPFNOSUPPORT
errno.EPIPE
errno.EPROCLIM
errno.EPROCUNAVAIL
errno.EPROGMISMATCH
errno.EPROGUNAVAIL
errno.EPROTO
errno.EPROTONOSUPPORT
errno.EPROTOTYPE
errno.EPWROFF
errno.EQFULL
errno.ERANGE
errno.EREMOTE
errno.EROFS
errno.ERPCMISMATCH
errno.ESHLIBVERS
errno.ESHUTDOWN
errno.ESOCKTNOSUPPORT
errno.ESPIPE
errno.ESRCH
errno.ESTALE
errno.ETIME
errno.ETIMEDOUT
errno.ETOOMANYREFS
errno.ETXTBSY
errno.EUSERS
errno.EWOULDBLOCK
errno.EXDEV
errno.errorcode(??)
eval(??)
exec(??)
exit(??)
faulthandler.cancel_dump_traceback_later():
faulthandler.disable(): disable the fault handler
faulthandler.dump_traceback(file=sys.stderr, all_threads=True): dump the traceback of the current thread, or of all threads if all_threads is True, into file
faulthandler.dump_traceback_later(timeout, repeat=False, file=sys.stderrn, exit=False):
faulthandler.enable(file=sys.stderr, all_threads=True): enable the fault handler
faulthandler.is_enabled()->bool: check if the handler is enabled
faulthandler.register(signum, file=sys.stderr, all_threads=True, chain=False): register a handler for the signal 'signum': dump the traceback of the current thread, or of all threads if all_threads is True, into file
faulthandler.unregister(signum): unregister the handler of the signal 'signum' registered by register()
filecmp.BUFSIZE
filecmp.DEFAULT_IGNORES(??)
filecmp.GenericAlias(??)
filecmp.clear_cache(??)
filecmp.cmp(??)
filecmp.cmpfiles(??)
filecmp.demo(??)
filecmp.dircmp(??)
filecmp.filterfalse(??)
filecmp.os(??)
filecmp.stat(??)
fileinput.FileInput([files[, inplace[, backup]]], *, mode=None, openhook=None)
fileinput.GenericAlias(??)
fileinput.close(??)
fileinput.filelineno(??)
fileinput.filename(??)
fileinput.fileno(??)
fileinput.hook_compressed(??)
fileinput.hook_encoded(??)
fileinput.input(??)
fileinput.io(??)
fileinput.isfirstline(??)
fileinput.isstdin(??)
fileinput.lineno(??)
fileinput.nextfile(??)
fileinput.os(??)
fileinput.sys(??)
filter(function or None, iterable) --> filter object
float(??)
fnmatch.filter(??)
fnmatch.fnmatch(??)
fnmatch.fnmatchcase(??)
fnmatch.functools(??)
fnmatch.os(??)
fnmatch.posixpath(??)
fnmatch.re(??)
fnmatch.translate(??)
format(??)
fractions.Decimal(??)
fractions.Fraction(??)
fractions.math(??)
fractions.numbers(??)
fractions.operator(??)
fractions.re(??)
fractions.sys(??)
frozenset() -> empty frozenset object
ftplib.B_CRLF
ftplib.CRLF
ftplib.Error(??)
ftplib.FTP(??)
ftplib.FTP_PORT
ftplib.FTP_TLS(??)
ftplib.MAXLINE
ftplib.MSG_OOB
ftplib.all_errors(??)
ftplib.error_perm(??)
ftplib.error_proto(??)
ftplib.error_reply(??)
ftplib.error_temp(??)
ftplib.ftpcp(??)
ftplib.parse150(??)
ftplib.parse227(??)
ftplib.parse229(??)
ftplib.parse257(??)
ftplib.print_line(??)
ftplib.socket(??)
ftplib.ssl(??)
ftplib.sys(??)
ftplib.test(??)
functools.GenericAlias(??)
functools.RLock(??)
functools.WRAPPER_ASSIGNMENTS(??)
functools.WRAPPER_UPDATES(??)
functools.cache(??)
functools.cached_property(??)
functools.cmp_to_key(??)
functools.get_cache_token(??)
functools.lru_cache(??)
functools.namedtuple(??)
functools.partial(func, *args, **keywords) - new function with partial application
functools.partialmethod(??)
functools.recursive_repr(??)
functools.reduce(function, iterable[, initial]) -> value
functools.singledispatch(??)
functools.singledispatchmethod(??)
functools.total_ordering(??)
functools.update_wrapper(??)
functools.wraps(??)
gc.DEBUG_COLLECTABLE
gc.DEBUG_LEAK
gc.DEBUG_SAVEALL
gc.DEBUG_STATS
gc.DEBUG_UNCOLLECTABLE
gc.callbacks(??)
gc.collect(??)
gc.disable(??)
gc.enable(??)
gc.freeze(??)
gc.garbage(??)
gc.get_count(??)
gc.get_debug(??)
gc.get_freeze_count(??)
gc.get_objects(??)
gc.get_referents(*objs) -> list
gc.get_referrers(*objs) -> list
gc.get_stats(??)
gc.get_threshold(??)
gc.is_finalized(??)
gc.is_tracked(??)
gc.isenabled(??)
gc.set_debug(??)
gc.set_threshold(threshold0, [threshold1, threshold2]) -> None
gc.unfreeze(??)
genericpath.commonprefix(??)
genericpath.exists(??)
genericpath.getatime(??)
genericpath.getctime(??)
genericpath.getmtime(??)
genericpath.getsize(??)
genericpath.isdir(??)
genericpath.isfile(??)
genericpath.os(??)
genericpath.samefile(??)
genericpath.sameopenfile(??)
genericpath.samestat(??)
genericpath.stat(??)
getattr(object, name[, default]) -> value
getopt.GetoptError(??)
getopt.do_longs(??)
getopt.do_shorts(??)
getopt.error(??)
getopt.getopt(args, options[, long_options]) -> opts, args
getopt.gnu_getopt(??)
getopt.long_has_args(??)
getopt.os(??)
getopt.short_has_arg(??)
getpass.GetPassWarning(??)
getpass.contextlib(??)
getpass.fallback_getpass(??)
getpass.getpass(??)
getpass.getuser(??)
getpass.io(??)
getpass.os(??)
getpass.sys(??)
getpass.termios(??)
getpass.unix_getpass(??)
getpass.warnings(??)
getpass.win_getpass(??)
gettext.Catalog(??)
gettext.GNUTranslations(??)
gettext.NullTranslations(??)
gettext.bindtextdomain(??)
gettext.c2py(??)
gettext.dgettext(??)
gettext.dngettext(??)
gettext.dnpgettext(??)
gettext.dpgettext(??)
gettext.find(??)
gettext.gettext(??)
gettext.install(??)
gettext.ngettext(??)
gettext.npgettext(??)
gettext.os(??)
gettext.pgettext(??)
gettext.re(??)
gettext.sys(??)
gettext.textdomain(??)
gettext.translation(??)
glob.contextlib(??)
glob.escape(??)
glob.fnmatch(??)
glob.glob(??)
glob.glob0(??)
glob.glob1(??)
glob.has_magic(??)
glob.iglob(??)
glob.itertools(??)
glob.magic_check(??)
glob.magic_check_bytes(??)
glob.os(??)
glob.re(??)
glob.stat(??)
glob.sys(??)
globals(??)
graphlib.CycleError(??)
graphlib.GenericAlias(??)
graphlib.TopologicalSorter(??)
gzip.BadGzipFile(??)
gzip.FCOMMENT
gzip.FEXTRA
gzip.FHCRC
gzip.FNAME
gzip.FTEXT
gzip.GzipFile(??)
gzip.READ
gzip.WRITE
gzip.builtins(??)
gzip.compress(??)
gzip.decompress(??)
gzip.io(??)
gzip.main(??)
gzip.open(??)
gzip.os(??)
gzip.struct(??)
gzip.sys(??)
gzip.time(??)
gzip.write32u(??)
gzip.zlib(??)
hasattr(??)
hash(??)
hashlib.algorithms_available(??)
hashlib.algorithms_guaranteed(??)
hashlib.blake2b(??)
hashlib.blake2s(??)
hashlib.file_digest(??)
hashlib.md5(??)
hashlib.new(name, data=b'') - Return a new hashing object using the named algorithm;
hashlib.pbkdf2_hmac(??)
hashlib.scrypt(??)
hashlib.sha1(??)
hashlib.sha224(??)
hashlib.sha256(??)
hashlib.sha384(??)
hashlib.sha3_224(??)
hashlib.sha3_256(??)
hashlib.sha3_384(??)
hashlib.sha3_512(??)
hashlib.sha512(??)
hashlib.shake_128(??)
hashlib.shake_256(??)
heapq.heapify(??)
heapq.heappop(??)
heapq.heappush(??)
heapq.heappushpop(??)
heapq.heapreplace(??)
heapq.merge(??)
heapq.nlargest(??)
heapq.nsmallest(??)
help(??)
hex(??)
hmac.HMAC(??)
hmac.compare_digest(??)
hmac.digest(??)
hmac.digest_size(??)
hmac.new(??)
hmac.trans_36
hmac.trans_5C
html.entities(??)
html.entities.codepoint2name(??)
html.entities.entitydefs(??)
html.entities.html5(??)
html.entities.name2codepoint(??)
html.escape(??)
html.parser.HTMLParser(??)
html.parser.attrfind_tolerant(??)
html.parser.charref(??)
html.parser.commentclose(??)
html.parser.endendtag(??)
html.parser.endtagfind(??)
html.parser.entityref(??)
html.parser.incomplete(??)
html.parser.interesting_normal(??)
html.parser.locatestarttagend_tolerant(??)
html.parser.piclose(??)
html.parser.re(??)
html.parser.starttagopen(??)
html.parser.tagfind_tolerant(??)
html.parser.unescape(??)
html.unescape(??)
http.HTTPMethod(??)
http.HTTPStatus(??)
http.IntEnum(??)
http.StrEnum(??)
http.client(??)
http.client.ACCEPTED(??)
http.client.ALREADY_REPORTED(??)
http.client.BAD_GATEWAY(??)
http.client.BAD_REQUEST(??)
http.client.BadStatusLine(??)
http.client.CONFLICT(??)
http.client.CONTINUE(??)
http.client.CREATED(??)
http.client.CannotSendHeader(??)
http.client.CannotSendRequest(??)
http.client.EARLY_HINTS(??)
http.client.EXPECTATION_FAILED(??)
http.client.FAILED_DEPENDENCY(??)
http.client.FORBIDDEN(??)
http.client.FOUND(??)
http.client.GATEWAY_TIMEOUT(??)
http.client.GONE(??)
http.client.HTTPConnection(??)
http.client.HTTPException(??)
http.client.HTTPMessage(??)
http.client.HTTPResponse(??)
http.client.HTTPSConnection(??)
http.client.HTTPS_PORT
http.client.HTTP_PORT
http.client.HTTP_VERSION_NOT_SUPPORTED(??)
http.client.IM_A_TEAPOT(??)
http.client.IM_USED(??)
http.client.INSUFFICIENT_STORAGE(??)
http.client.INTERNAL_SERVER_ERROR(??)
http.client.ImproperConnectionState(??)
http.client.IncompleteRead(??)
http.client.InvalidURL(??)
http.client.LENGTH_REQUIRED(??)
http.client.LOCKED(??)
http.client.LOOP_DETECTED(??)
http.client.LineTooLong(??)
http.client.METHOD_NOT_ALLOWED(??)
http.client.MISDIRECTED_REQUEST(??)
http.client.MOVED_PERMANENTLY(??)
http.client.MULTIPLE_CHOICES(??)
http.client.MULTI_STATUS(??)
http.client.NETWORK_AUTHENTICATION_REQUIRED(??)
http.client.NON_AUTHORITATIVE_INFORMATION(??)
http.client.NOT_ACCEPTABLE(??)
http.client.NOT_EXTENDED(??)
http.client.NOT_FOUND(??)
http.client.NOT_IMPLEMENTED(??)
http.client.NOT_MODIFIED(??)
http.client.NO_CONTENT(??)
http.client.NotConnected(??)
http.client.OK(??)
http.client.PARTIAL_CONTENT(??)
http.client.PAYMENT_REQUIRED(??)
http.client.PERMANENT_REDIRECT(??)
http.client.PRECONDITION_FAILED(??)
http.client.PRECONDITION_REQUIRED(??)
http.client.PROCESSING(??)
http.client.PROXY_AUTHENTICATION_REQUIRED(??)
http.client.REQUESTED_RANGE_NOT_SATISFIABLE(??)
http.client.REQUEST_ENTITY_TOO_LARGE(??)
http.client.REQUEST_HEADER_FIELDS_TOO_LARGE(??)
http.client.REQUEST_TIMEOUT(??)
http.client.REQUEST_URI_TOO_LONG(??)
http.client.RESET_CONTENT(??)
http.client.RemoteDisconnected(??)
http.client.ResponseNotReady(??)
http.client.SEE_OTHER(??)
http.client.SERVICE_UNAVAILABLE(??)
http.client.SWITCHING_PROTOCOLS(??)
http.client.TEMPORARY_REDIRECT(??)
http.client.TOO_EARLY(??)
http.client.TOO_MANY_REQUESTS(??)
http.client.UNAUTHORIZED(??)
http.client.UNAVAILABLE_FOR_LEGAL_REASONS(??)
http.client.UNPROCESSABLE_ENTITY(??)
http.client.UNSUPPORTED_MEDIA_TYPE(??)
http.client.UPGRADE_REQUIRED(??)
http.client.USE_PROXY(??)
http.client.UnimplementedFileMode(??)
http.client.UnknownProtocol(??)
http.client.UnknownTransferEncoding(??)
http.client.VARIANT_ALSO_NEGOTIATES(??)
http.client.collections(??)
http.client.email(??)
http.client.errno(??)
http.client.error(??)
http.client.http(??)
http.client.io(??)
http.client.parse_headers(??)
http.client.re(??)
http.client.responses(??)
http.client.socket(??)
http.client.ssl(??)
http.client.sys(??)
http.client.urlsplit(??)
http.cookiejar.Absent(??)
http.cookiejar.Cookie(??)
http.cookiejar.CookieJar(??)
http.cookiejar.CookiePolicy(??)
http.cookiejar.DAYS(??)
http.cookiejar.DEFAULT_HTTP_PORT
http.cookiejar.DefaultCookiePolicy(??)
http.cookiejar.EPOCH_YEAR
http.cookiejar.ESCAPED_CHAR_RE(??)
http.cookiejar.FileCookieJar(??)
http.cookiejar.HEADER_ESCAPE_RE(??)
http.cookiejar.HEADER_JOIN_ESCAPE_RE(??)
http.cookiejar.HEADER_QUOTED_VALUE_RE(??)
http.cookiejar.HEADER_TOKEN_RE(??)
http.cookiejar.HEADER_VALUE_RE(??)
http.cookiejar.HTTPONLY_ATTR
http.cookiejar.HTTPONLY_PREFIX
http.cookiejar.HTTP_PATH_SAFE
http.cookiejar.IPV4_RE(??)
http.cookiejar.ISO_DATE_RE(??)
http.cookiejar.LOOSE_HTTP_DATE_RE(??)
http.cookiejar.LWPCookieJar(??)
http.cookiejar.LoadError(??)
http.cookiejar.MISSING_FILENAME_TEXT
http.cookiejar.MONTHS(??)
http.cookiejar.MONTHS_LOWER(??)
http.cookiejar.MozillaCookieJar(??)
http.cookiejar.NETSCAPE_HEADER_TEXT
http.cookiejar.NETSCAPE_MAGIC_RGX(??)
http.cookiejar.STRICT_DATE_RE(??)
http.cookiejar.TIMEZONE_RE(??)
http.cookiejar.UTC_ZONES(??)
http.cookiejar.WEEKDAY_RE(??)
http.cookiejar.copy(??)
http.cookiejar.cut_port_re(??)
http.cookiejar.datetime(??)
http.cookiejar.debug(??)
http.cookiejar.deepvalues(??)
http.cookiejar.domain_match(??)
http.cookiejar.eff_request_host(??)
http.cookiejar.escape_path(??)
http.cookiejar.http(??)
http.cookiejar.http2time(??)
http.cookiejar.is_HDN(??)
http.cookiejar.is_third_party(??)
http.cookiejar.iso2time(??)
http.cookiejar.join_header_words(??)
http.cookiejar.liberal_is_HDN(??)
http.cookiejar.logger(??)
http.cookiejar.lwp_cookie_str(??)
http.cookiejar.offset_from_tz_string(??)
http.cookiejar.os(??)
http.cookiejar.parse_ns_headers(??)
http.cookiejar.re(??)
http.cookiejar.reach(??)
http.cookiejar.request_host(??)
http.cookiejar.request_path(??)
http.cookiejar.request_port(??)
http.cookiejar.split_header_words(??)
http.cookiejar.strip_quotes(??)
http.cookiejar.time(??)
http.cookiejar.time2isoz(??)
http.cookiejar.time2netscape(??)
http.cookiejar.timegm(??)
http.cookiejar.unmatched(??)
http.cookiejar.uppercase_escaped_char(??)
http.cookiejar.urllib(??)
http.cookiejar.user_domain_match(??)
http.cookies(??)
http.cookies.BaseCookie(??)
http.cookies.CookieError(??)
http.cookies.Morsel(??)
http.cookies.SimpleCookie(??)
http.cookies.re(??)
http.cookies.string(??)
http.cookies.types(??)
http.server(??)
http.server.BaseHTTPRequestHandler(??)
http.server.CGIHTTPRequestHandler(??)
http.server.DEFAULT_ERROR_CONTENT_TYPE
http.server.DEFAULT_ERROR_MESSAGE
http.server.HTTPServer(??)
http.server.HTTPStatus(??)
http.server.SimpleHTTPRequestHandler(??)
http.server.ThreadingHTTPServer(??)
http.server.copy(??)
http.server.datetime(??)
http.server.email(??)
http.server.executable(??)
http.server.html(??)
http.server.http(??)
http.server.io(??)
http.server.mimetypes(??)
http.server.nobody(??)
http.server.nobody_uid(??)
http.server.os(??)
http.server.posixpath(??)
http.server.select(??)
http.server.shutil(??)
http.server.socket(??)
http.server.socketserver(??)
http.server.sys(??)
http.server.test(??)
http.server.time(??)
http.server.urllib(??)
id(??)
imaplib.AllowedVersions(??)
imaplib.CRLF
imaplib.Commands(??)
imaplib.Continuation(??)
imaplib.DEFAULT_BUFFER_SIZE
imaplib.Debug
imaplib.Flags(??)
imaplib.HAVE_SSL(??)
imaplib.IMAP4(??)
imaplib.IMAP4_PORT
imaplib.IMAP4_SSL(??)
imaplib.IMAP4_SSL_PORT
imaplib.IMAP4_stream(??)
imaplib.Int2AP(??)
imaplib.InternalDate(??)
imaplib.Internaldate2tuple(??)
imaplib.Literal(??)
imaplib.MapCRLF(??)
imaplib.Mon2num(??)
imaplib.Months(??)
imaplib.ParseFlags(??)
imaplib.Response_code(??)
imaplib.Time2Internaldate(??)
imaplib.Untagged_response(??)
imaplib.Untagged_status(??)
imaplib.binascii(??)
imaplib.calendar(??)
imaplib.datetime(year, month, day[, hour[, minute[, second[, microsecond[,tzinfo]]]]])
imaplib.errno(??)
imaplib.random(??)
imaplib.re(??)
imaplib.socket(??)
imaplib.ssl(??)
imaplib.subprocess(??)
imaplib.sys(??)
imaplib.time(??)
imaplib.timedelta(??)
imaplib.timezone(??)
imghdr.PathLike(??)
imghdr.test(??)
imghdr.test_bmp(??)
imghdr.test_exr(??)
imghdr.test_gif(??)
imghdr.test_jpeg(??)
imghdr.test_pbm(??)
imghdr.test_pgm(??)
imghdr.test_png(??)
imghdr.test_ppm(??)
imghdr.test_rast(??)
imghdr.test_rgb(??)
imghdr.test_tiff(??)
imghdr.test_webp(??)
imghdr.test_xbm(??)
imghdr.testall(??)
imghdr.tests(??)
imghdr.warnings(??)
imghdr.what(??)
imp.C_BUILTIN
imp.C_EXTENSION
imp.IMP_HOOK
imp.NullImporter(??)
imp.PKG_DIRECTORY
imp.PY_CODERESOURCE
imp.PY_COMPILED
imp.PY_FROZEN
imp.PY_RESOURCE
imp.PY_SOURCE
imp.SEARCH_ERROR
imp.SourcelessFileLoader(??)
imp.acquire_lock(??)
imp.cache_from_source(??)
imp.create_dynamic(??)
imp.find_module(??)
imp.get_frozen_object(??)
imp.get_magic(??)
imp.get_suffixes(??)
imp.get_tag(??)
imp.importlib(??)
imp.init_builtin(??)
imp.init_frozen(??)
imp.is_builtin(??)
imp.is_frozen(??)
imp.is_frozen_package(??)
imp.load_compiled(??)
imp.load_dynamic(??)
imp.load_module(??)
imp.load_package(??)
imp.load_source(??)
imp.lock_held(??)
imp.machinery(??)
imp.new_module(??)
imp.os(??)
imp.release_lock(??)
imp.reload(??)
imp.source_from_cache(??)
imp.sys(??)
imp.tokenize(??)
imp.types(??)
imp.util(??)
imp.warnings(??)
importlib.abc.ExecutionLoader(??)
importlib.abc.FileLoader(??)
importlib.abc.Finder(??)
importlib.abc.InspectLoader(??)
importlib.abc.Loader(??)
importlib.abc.MetaPathFinder(??)
importlib.abc.PathEntryFinder(??)
importlib.abc.ResourceLoader(??)
importlib.abc.ResourceReader(??)
importlib.abc.SourceLoader(??)
importlib.abc.Traversable(??)
importlib.abc.TraversableResources(??)
importlib.abc.abc(??)
importlib.abc.machinery(??)
importlib.abc.warnings(??)
importlib.find_loader(??)
importlib.import_module(??)
importlib.invalidate_caches(??)
importlib.machinery(??)
importlib.machinery.BYTECODE_SUFFIXES(??)
importlib.machinery.BuiltinImporter(??)
importlib.machinery.DEBUG_BYTECODE_SUFFIXES(??)
importlib.machinery.EXTENSION_SUFFIXES(??)
importlib.machinery.ExtensionFileLoader(??)
importlib.machinery.FileFinder(??)
importlib.machinery.FrozenImporter(??)
importlib.machinery.ModuleSpec(??)
importlib.machinery.NamespaceLoader(??)
importlib.machinery.OPTIMIZED_BYTECODE_SUFFIXES(??)
importlib.machinery.PathFinder(??)
importlib.machinery.SOURCE_SUFFIXES(??)
importlib.machinery.SourceFileLoader(??)
importlib.machinery.SourcelessFileLoader(??)
importlib.machinery.WindowsRegistryFinder(??)
importlib.machinery.all_suffixes(??)
importlib.metadata.Deprecated(??)
importlib.metadata.DeprecatedList(??)
importlib.metadata.DeprecatedTuple(??)
importlib.metadata.Distribution(??)
importlib.metadata.DistributionFinder(??)
importlib.metadata.EntryPoint(??)
importlib.metadata.EntryPoints(??)
importlib.metadata.FastPath(??)
importlib.metadata.FileHash(??)
importlib.metadata.FreezableDefaultDict(??)
importlib.metadata.List(??)
importlib.metadata.Lookup(??)
importlib.metadata.Mapping(??)
importlib.metadata.MetaPathFinder(??)
importlib.metadata.MetadataPathFinder(??)
importlib.metadata.Optional(??)
importlib.metadata.PackageMetadata(??)
importlib.metadata.PackageNotFoundError(??)
importlib.metadata.PackagePath(??)
importlib.metadata.Pair(??)
importlib.metadata.PathDistribution(??)
importlib.metadata.Prepared(??)
importlib.metadata.Sectioned(??)
importlib.metadata.SelectableGroups(??)
importlib.metadata.SimplePath(??)
importlib.metadata.Union(??)
importlib.metadata.abc(??)
importlib.metadata.always_iterable(??)
importlib.metadata.collections(??)
importlib.metadata.csv(??)
importlib.metadata.distribution(??)
importlib.metadata.distributions(??)
importlib.metadata.email(??)
importlib.metadata.entry_points(??)
importlib.metadata.files(??)
importlib.metadata.functools(??)
importlib.metadata.import_module(??)
importlib.metadata.itertools(??)
importlib.metadata.metadata(??)
importlib.metadata.method_cache(??)
importlib.metadata.operator(??)
importlib.metadata.os(??)
importlib.metadata.packages_distributions(??)
importlib.metadata.pass_none(??)
importlib.metadata.pathlib(??)
importlib.metadata.posixpath(??)
importlib.metadata.re(??)
importlib.metadata.requires(??)
importlib.metadata.starmap(??)
importlib.metadata.suppress(??)
importlib.metadata.sys(??)
importlib.metadata.textwrap(??)
importlib.metadata.unique_everseen(??)
importlib.metadata.version(??)
importlib.metadata.warnings(??)
importlib.metadata.zipfile(??)
importlib.readers(??)
importlib.readers.FileReader(??)
importlib.readers.MultiplexedPath(??)
importlib.readers.NamespaceReader(??)
importlib.readers.ZipReader(??)
importlib.reload(??)
importlib.resources(??)
importlib.resources.Package(??)
importlib.resources.Resource(??)
importlib.resources.ResourceReader(??)
importlib.resources.abc(??)
importlib.resources.abc.Any(??)
importlib.resources.abc.BinaryIO(??)
importlib.resources.abc.Iterable(??)
importlib.resources.abc.Iterator(??)
importlib.resources.abc.NoReturn(??)
importlib.resources.abc.Optional(??)
importlib.resources.abc.Protocol(??)
importlib.resources.abc.ResourceReader(??)
importlib.resources.abc.StrPath(??)
importlib.resources.abc.Text(??)
importlib.resources.abc.Traversable(??)
importlib.resources.abc.TraversableResources(??)
importlib.resources.abc.Union(??)
importlib.resources.abc.abc(??)
importlib.resources.abc.io(??)
importlib.resources.abc.os(??)
importlib.resources.abc.runtime_checkable(??)
importlib.resources.as_file(??)
importlib.resources.contents(??)
importlib.resources.files(??)
importlib.resources.is_resource(??)
importlib.resources.open_binary(??)
importlib.resources.open_text(??)
importlib.resources.path(??)
importlib.resources.read_binary(??)
importlib.resources.read_text(??)
importlib.resources.readers(??)
importlib.resources.readers.FileReader(??)
importlib.resources.readers.MultiplexedPath(??)
importlib.resources.readers.NamespaceReader(??)
importlib.resources.readers.ZipReader(??)
importlib.resources.readers.abc(??)
importlib.resources.readers.collections(??)
importlib.resources.readers.operator(??)
importlib.resources.readers.pathlib(??)
importlib.resources.readers.remove_duplicates(??)
importlib.resources.readers.unique_everseen(??)
importlib.resources.readers.zipfile(??)
importlib.resources.simple.BinaryIO(??)
importlib.resources.simple.List(??)
importlib.resources.simple.ResourceContainer(??)
importlib.resources.simple.ResourceHandle(??)
importlib.resources.simple.SimpleReader(??)
importlib.resources.simple.Traversable(??)
importlib.resources.simple.TraversableReader(??)
importlib.resources.simple.TraversableResources(??)
importlib.resources.simple.abc(??)
importlib.resources.simple.io(??)
importlib.resources.simple.itertools(??)
importlib.simple.ResourceContainer(??)
importlib.simple.ResourceHandle(??)
importlib.simple.SimpleReader(??)
importlib.simple.TraversableReader(??)
importlib.sys(??)
importlib.util(??)
importlib.util.LazyLoader(??)
importlib.util.Loader(??)
importlib.util.MAGIC_NUMBER
importlib.util.cache_from_source(??)
importlib.util.contextmanager(??)
importlib.util.decode_source(??)
importlib.util.find_spec(??)
importlib.util.functools(??)
importlib.util.module_for_loader(??)
importlib.util.module_from_spec(??)
importlib.util.resolve_name(??)
importlib.util.set_loader(??)
importlib.util.set_package(??)
importlib.util.source_from_cache(??)
importlib.util.source_hash(??)
importlib.util.spec_from_file_location(??)
importlib.util.spec_from_loader(??)
importlib.util.sys(??)
importlib.util.types(??)
importlib.util.warnings(??)
importlib.warnings(??)
int(??)
input(??)
inspect.ArgInfo(args, varargs, keywords, locals)
inspect.Arguments(args, varargs, varkw)
inspect.Attribute(name, kind, defining_class, object)
inspect.BlockFinder(??)
inspect.BoundArguments(??)
inspect.CORO_CLOSED
inspect.CORO_CREATED
inspect.CORO_RUNNING
inspect.CORO_SUSPENDED
inspect.CO_ASYNC_GENERATOR
inspect.CO_COROUTINE
inspect.CO_GENERATOR
inspect.CO_ITERABLE_COROUTINE
inspect.CO_NESTED
inspect.CO_NEWLOCALS
inspect.CO_NOFREE
inspect.CO_OPTIMIZED
inspect.CO_VARARGS
inspect.CO_VARKEYWORDS
inspect.ClassFoundException(??)
inspect.ClosureVars(nonlocals, globals, builtins, unbound)
inspect.EndOfBlock(??)
inspect.FrameInfo(??)
inspect.FullArgSpec(args, varargs, varkw, defaults, kwonlyargs, kwonlydefaults, annotations)
inspect.GEN_CLOSED
inspect.GEN_CREATED
inspect.GEN_RUNNING
inspect.GEN_SUSPENDED
inspect.OrderedDict(??)
inspect.Parameter(??)
inspect.Signature(??)
inspect.TPFLAGS_IS_ABSTRACT
inspect.Traceback(??)
inspect.abc(??)
inspect.ast(??)
inspect.attrgetter(attr, ...) --> attrgetter object
inspect.builtins(??)
inspect.classify_class_attrs(??)
inspect.cleandoc(??)
inspect.collections(??)
inspect.currentframe(??)
inspect.dis(??)
inspect.enum(??)
inspect.findsource(??)
inspect.formatannotation(??)
inspect.formatannotationrelativeto(??)
inspect.formatargvalues(??)
inspect.functools(??)
inspect.get_annotations(??)
inspect.getabsfile(??)
inspect.getargs(??)
inspect.getargvalues(??)
inspect.getattr_static(??)
inspect.getblock(??)
inspect.getcallargs(??)
inspect.getclasstree(??)
inspect.getclosurevars(??)
inspect.getcomments(??)
inspect.getcoroutinelocals(??)
inspect.getcoroutinestate(??)
inspect.getdoc(??)
inspect.getfile(??)
inspect.getframeinfo(??)
inspect.getfullargspec(??)
inspect.getgeneratorlocals(??)
inspect.getgeneratorstate(??)
inspect.getinnerframes(??)
inspect.getlineno(??)
inspect.getmembers(??)
inspect.getmembers_static(??)
inspect.getmodule(??)
inspect.getmodulename(??)
inspect.getmro(??)
inspect.getouterframes(??)
inspect.getsource(??)
inspect.getsourcefile(??)
inspect.getsourcelines(??)
inspect.importlib(??)
inspect.indentsize(??)
inspect.isabstract(??)
inspect.isasyncgen(??)
inspect.isasyncgenfunction(??)
inspect.isawaitable(??)
inspect.isbuiltin(??)
inspect.isclass(??)
inspect.iscode(??)
inspect.iscoroutine(??)
inspect.iscoroutinefunction(??)
inspect.isdatadescriptor(??)
inspect.isframe(??)
inspect.isfunction(??)
inspect.isgenerator(??)
inspect.isgeneratorfunction(??)
inspect.isgetsetdescriptor(??)
inspect.iskeyword(??)
inspect.ismemberdescriptor(??)
inspect.ismethod(??)
inspect.ismethoddescriptor(??)
inspect.ismethodwrapper(??)
inspect.ismodule(??)
inspect.isroutine(??)
inspect.istraceback(??)
inspect.itertools(??)
inspect.linecache(??)
inspect.modulesbyfile(??)
inspect.namedtuple(??)
inspect.os(??)
inspect.re(??)
inspect.signature(??)
inspect.stack(??)
inspect.sys(??)
inspect.token(??)
inspect.tokenize(??)
inspect.trace(??)
inspect.types(??)
inspect.unwrap(??)
inspect.walktree(??)
int([x]) -> integer
io.BlockingIOError(??)
io.BufferedIOBase(??)
io.BufferedRWPair(??)
io.BufferedRandom(??)
io.BufferedReader(??)
io.BufferedWriter(??)
io.BytesIO(??)
io.DEFAULT_BUFFER_SIZE
io.FileIO(??)
io.IOBase(??)
io.IncrementalNewlineDecoder(??)
io.RawIOBase(??)
io.SEEK_CUR
io.SEEK_END
io.SEEK_SET
io.StringIO(??)
io.TextIOBase(??)
io.TextIOWrapper(??)
io.UnsupportedOperation(??)
io.abc(??)
io.open(??)
io.open_code(??)
io.text_encoding(??)
ipaddress.AddressValueError(??)
ipaddress.IPV4LENGTH
ipaddress.IPV6LENGTH
ipaddress.IPv4Address(??)
ipaddress.IPv4Interface(??)
ipaddress.IPv4Network(??)
ipaddress.IPv6Address(??)
ipaddress.IPv6Interface(??)
ipaddress.IPv6Network(??)
ipaddress.NetmaskValueError(??)
ipaddress.collapse_addresses(??)
ipaddress.functools(??)
ipaddress.get_mixed_type_key(??)
ipaddress.ip_address(??)
ipaddress.ip_interface(??)
ipaddress.ip_network(??)
ipaddress.summarize_address_range(??)
ipaddress.v4_int_to_packed(??)
ipaddress.v6_int_to_packed(??)
isinstance(??)
issubclass(??)
iter(iterable) -> iterator
itertools.accumulate(??)
itertools.chain(*iterables) --> chain object
itertools.combinations(??)
itertools.combinations_with_replacement(??)
itertools.compress(??)
itertools.count(??)
itertools.cycle(??)
itertools.dropwhile(??)
itertools.filterfalse(??)
itertools.groupby(??)
itertools.islice(iterable, stop) --> islice object
itertools.pairwise(??)
itertools.permutations(??)
itertools.product(*iterables, repeat=1) --> product object
itertools.repeat(object [,times]) -> create an iterator which returns the object
itertools.starmap(??)
itertools.takewhile(??)
itertools.tee(??)
itertools.zip_longest(iter1 [,iter2 [...]], [fillvalue=None]) --> zip_longest object
json.JSONDecodeError(??)
json.JSONDecoder(??)
json.JSONEncoder(??)
json.codecs(??)
json.decoder(??)
json.decoder.BACKSLASH(??)
json.decoder.FLAGS(??)
json.decoder.JSONArray(??)
json.decoder.JSONDecodeError(??)
json.decoder.JSONDecoder(??)
json.decoder.JSONObject(??)
json.decoder.NaN
json.decoder.NegInf
json.decoder.PosInf
json.decoder.STRINGCHUNK(??)
json.decoder.WHITESPACE(??)
json.decoder.WHITESPACE_STR
json.decoder.c_scanstring(??)
json.decoder.py_scanstring(??)
json.decoder.re(??)
json.decoder.scanner(??)
json.decoder.scanstring(string, end, strict=True) -> (string, end)
json.detect_encoding(??)
json.dump(??)
json.dumps(??)
json.encoder(??)
json.encoder.ESCAPE(??)
json.encoder.ESCAPE_ASCII(??)
json.encoder.ESCAPE_DCT(??)
json.encoder.HAS_UTF8(??)
json.encoder.INFINITY
json.encoder.JSONEncoder(??)
json.encoder.c_encode_basestring(??)
json.encoder.c_encode_basestring_ascii(??)
json.encoder.c_make_encoder(??)
json.encoder.encode_basestring(string) -> string
json.encoder.encode_basestring_ascii(string) -> string
json.encoder.py_encode_basestring(??)
json.encoder.py_encode_basestring_ascii(??)
json.encoder.re(??)
json.load(??)
json.loads(??)
json.scanner(??)
json.scanner.NUMBER_RE(??)
json.scanner.c_make_scanner(??)
json.scanner.make_scanner(??)
json.scanner.py_make_scanner(??)
json.scanner.re(??)
json.tool.Path(??)
json.tool.argparse(??)
json.tool.json(??)
json.tool.main(??)
json.tool.sys(??)
keyword.iskeyword(??)
keyword.issoftkeyword(??)
keyword.kwlist(??)
keyword.softkwlist(??)
len(??)
license(??)
linecache.cache(??)
linecache.checkcache(??)
linecache.clearcache(??)
linecache.functools(??)
linecache.getline(??)
linecache.getlines(??)
linecache.lazycache(??)
linecache.os(??)
linecache.sys(??)
linecache.tokenize(??)
linecache.updatecache(??)
list(??)
locale.ABDAY_1
locale.ABDAY_2
locale.ABDAY_3
locale.ABDAY_4
locale.ABDAY_5
locale.ABDAY_6
locale.ABDAY_7
locale.ABMON_1
locale.ABMON_10
locale.ABMON_11
locale.ABMON_12
locale.ABMON_2
locale.ABMON_3
locale.ABMON_4
locale.ABMON_5
locale.ABMON_6
locale.ABMON_7
locale.ABMON_8
locale.ABMON_9
locale.ALT_DIGITS
locale.AM_STR
locale.CHAR_MAX
locale.CODESET
locale.CRNCYSTR
locale.DAY_1
locale.DAY_2
locale.DAY_3
locale.DAY_4
locale.DAY_5
locale.DAY_6
locale.DAY_7
locale.D_FMT
locale.D_T_FMT
locale.ERA
locale.ERA_D_FMT
locale.ERA_D_T_FMT
locale.ERA_T_FMT
locale.Error(??)
locale.LC_ALL
locale.LC_COLLATE
locale.LC_CTYPE
locale.LC_MESSAGES
locale.LC_MONETARY
locale.LC_NUMERIC
locale.LC_TIME
locale.MON_1
locale.MON_10
locale.MON_11
locale.MON_12
locale.MON_2
locale.MON_3
locale.MON_4
locale.MON_5
locale.MON_6
locale.MON_7
locale.MON_8
locale.MON_9
locale.NOEXPR
locale.PM_STR
locale.RADIXCHAR
locale.THOUSEP
locale.T_FMT
locale.T_FMT_AMPM
locale.YESEXPR
locale.atof(??)
locale.atoi(??)
locale.currency(??)
locale.delocalize(??)
locale.encodings(??)
locale.format(??)
locale.format_string(??)
locale.functools(??)
locale.getdefaultlocale(??)
locale.getencoding(??)
locale.getlocale(??)
locale.getpreferredencoding(??)
locale.locale_alias(??)
locale.locale_encoding_alias(??)
locale.localeconv(??)
locale.localize(??)
locale.nl_langinfo(??)
locale.normalize(??)
locale.re(??)
locale.resetlocale(??)
locale.setlocale(??)
locale.str(??)
locale.strcoll(??)
locale.strxfrm(??)
locale.sys(??)
locale.windows_locale(??)
locals(??)
logging.BASIC_FORMAT
logging.BufferingFormatter(??)
logging.CRITICAL
logging.DEBUG
logging.ERROR
logging.FATAL
logging.FileHandler(??)
logging.Filter(??)
logging.Filterer(??)
logging.Formatter(??)
logging.GenericAlias(??)
logging.Handler(??)
logging.INFO
logging.LogRecord(??)
logging.Logger(??)
logging.LoggerAdapter(??)
logging.Manager(??)
logging.NOTSET
logging.NullHandler(??)
logging.PercentStyle(??)
logging.PlaceHolder(??)
logging.RootLogger(??)
logging.StrFormatStyle(??)
logging.StreamHandler(??)
logging.StringTemplateStyle(??)
logging.Template(??)
logging.WARN
logging.WARNING
logging.addLevelName(??)
logging.atexit(??)
logging.basicConfig(??)
logging.captureWarnings(??)
logging.collections(??)
logging.config(??)
logging.config.BaseConfigurator(??)
logging.config.ConvertingDict(??)
logging.config.ConvertingList(??)
logging.config.ConvertingMixin(??)
logging.config.ConvertingTuple(??)
logging.config.DEFAULT_LOGGING_CONFIG_PORT
logging.config.DictConfigurator(??)
logging.config.IDENTIFIER(??)
logging.config.RESET_ERROR
logging.config.StreamRequestHandler(??)
logging.config.ThreadingTCPServer(??)
logging.config.dictConfig(??)
logging.config.dictConfigClass(??)
logging.config.errno(??)
logging.config.fileConfig(??)
logging.config.io(??)
logging.config.listen(??)
logging.config.logging(??)
logging.config.re(??)
logging.config.stopListening(??)
logging.config.struct(??)
logging.config.threading(??)
logging.config.traceback(??)
logging.config.valid_ident(??)
logging.critical(??)
logging.currentframe(??)
logging.debug(??)
logging.disable(??)
logging.error(??)
logging.exception(??)
logging.fatal(??)
logging.getLevelName(??)
logging.getLevelNamesMapping(??)
logging.getLogRecordFactory(??)
logging.getLogger(??)
logging.getLoggerClass(??)
logging.handlers(??)
logging.handlers.BaseRotatingHandler(??)
logging.handlers.BufferingHandler(??)
logging.handlers.DEFAULT_HTTP_LOGGING_PORT
logging.handlers.DEFAULT_SOAP_LOGGING_PORT
logging.handlers.DEFAULT_TCP_LOGGING_PORT
logging.handlers.DEFAULT_UDP_LOGGING_PORT
logging.handlers.DatagramHandler(??)
logging.handlers.HTTPHandler(??)
logging.handlers.MemoryHandler(??)
logging.handlers.NTEventLogHandler(??)
logging.handlers.QueueHandler(??)
logging.handlers.QueueListener(??)
logging.handlers.RotatingFileHandler(??)
logging.handlers.SMTPHandler(??)
logging.handlers.ST_DEV
logging.handlers.ST_INO
logging.handlers.ST_MTIME
logging.handlers.SYSLOG_TCP_PORT
logging.handlers.SYSLOG_UDP_PORT
logging.handlers.SocketHandler(??)
logging.handlers.SysLogHandler(??)
logging.handlers.TimedRotatingFileHandler(??)
logging.handlers.WatchedFileHandler(??)
logging.handlers.copy(??)
logging.handlers.io(??)
logging.handlers.logging(??)
logging.handlers.os(??)
logging.handlers.pickle(??)
logging.handlers.queue(??)
logging.handlers.re(??)
logging.handlers.socket(??)
logging.handlers.struct(??)
logging.handlers.threading(??)
logging.handlers.time(??)
logging.info(??)
logging.io(??)
logging.lastResort(??)
logging.log(??)
logging.logMultiprocessing(??)
logging.logProcesses(??)
logging.logThreads(??)
logging.makeLogRecord(??)
logging.os(??)
logging.raiseExceptions(??)
logging.re(??)
logging.root(??)
logging.setLogRecordFactory(??)
logging.setLoggerClass(??)
logging.shutdown(??)
logging.sys(??)
logging.threading(??)
logging.time(??)
logging.traceback(??)
logging.warn(??)
logging.warning(??)
logging.warnings(??)
logging.weakref(??)
lzma.CHECK_CRC32
lzma.CHECK_CRC64
lzma.CHECK_ID_MAX
lzma.CHECK_NONE
lzma.CHECK_SHA256
lzma.CHECK_UNKNOWN
lzma.FILTER_ARM
lzma.FILTER_ARMTHUMB
lzma.FILTER_DELTA
lzma.FILTER_IA64
lzma.FILTER_LZMA1
lzma.FILTER_LZMA2
lzma.FILTER_POWERPC
lzma.FILTER_SPARC
lzma.FILTER_X86
lzma.FORMAT_ALONE
lzma.FORMAT_AUTO
lzma.FORMAT_RAW
lzma.FORMAT_XZ
lzma.LZMACompressor(format=FORMAT_XZ, check=-1, preset=None, filters=None)
lzma.LZMADecompressor(??)
lzma.LZMAError(??)
lzma.LZMAFile(??)
lzma.MF_BT2
lzma.MF_BT3
lzma.MF_BT4
lzma.MF_HC3
lzma.MF_HC4
lzma.MODE_FAST
lzma.MODE_NORMAL
lzma.PRESET_DEFAULT
lzma.PRESET_EXTREME
lzma.builtins(??)
lzma.compress(??)
lzma.decompress(??)
lzma.io(??)
lzma.is_check_supported(??)
lzma.open(??)
lzma.os(??)
mailbox.Babyl(??)
mailbox.BabylMessage(??)
mailbox.Error(??)
mailbox.ExternalClashError(??)
mailbox.FormatError(??)
mailbox.GenericAlias(??)
mailbox.MH(??)
mailbox.MHMessage(??)
mailbox.MMDF(??)
mailbox.MMDFMessage(??)
mailbox.Mailbox(??)
mailbox.Maildir(??)
mailbox.MaildirMessage(??)
mailbox.Message(??)
mailbox.NoSuchMailboxError(??)
mailbox.NotEmptyError(??)
mailbox.calendar(??)
mailbox.contextlib(??)
mailbox.copy(??)
mailbox.email(??)
mailbox.errno(??)
mailbox.fcntl(??)
mailbox.io(??)
mailbox.linesep
mailbox.mbox(??)
mailbox.mboxMessage(??)
mailbox.os(??)
mailbox.socket(??)
mailbox.time(??)
mailbox.warnings(??)
mailcap.findmatch(??)
mailcap.findparam(??)
mailcap.getcaps(??)
mailcap.lineno_sort_key(??)
mailcap.listmailcapfiles(??)
mailcap.lookup(??)
mailcap.os(??)
mailcap.parsefield(??)
mailcap.parseline(??)
mailcap.readmailcapfile(??)
mailcap.show(??)
mailcap.subst(??)
mailcap.test(??)
mailcap.warnings(??)
map(func, *iterables) --> map object
marshal.dump(??)
marshal.dumps(??)
marshal.load(??)
marshal.loads(??)
marshal.version
max(iterable, *[, default=obj, key=func]) -> value
memoryview(??)
mimetypes.MimeTypes(??)
mimetypes.add_type(??)
mimetypes.common_types(??)
mimetypes.encodings_map(??)
mimetypes.guess_all_extensions(??)
mimetypes.guess_extension(??)
mimetypes.guess_type(??)
mimetypes.init(??)
mimetypes.inited(??)
mimetypes.knownfiles(??)
mimetypes.os(??)
mimetypes.posixpath(??)
mimetypes.read_mime_types(??)
mimetypes.suffix_map(??)
mimetypes.sys(??)
mimetypes.types_map(??)
mimetypes.urllib(??)
min(iterable, *[, default=obj, key=func]) -> value
modulefinder.AddPackagePath(??)
modulefinder.Module(??)
modulefinder.ModuleFinder(??)
modulefinder.ReplacePackage(??)
modulefinder.dis(??)
modulefinder.importlib(??)
modulefinder.io(??)
modulefinder.marshal(??)
modulefinder.os(??)
modulefinder.packagePathMap(??)
modulefinder.replacePackageMap(??)
modulefinder.sys(??)
modulefinder.test(??)
multiprocessing.Array(??)
multiprocessing.AuthenticationError(??)
multiprocessing.Barrier(??)
multiprocessing.BoundedSemaphore(??)
multiprocessing.BufferTooShort(??)
multiprocessing.Condition(??)
multiprocessing.Event(??)
multiprocessing.JoinableQueue(??)
multiprocessing.Lock(??)
multiprocessing.Manager(??)
multiprocessing.Pipe(??)
multiprocessing.Pool(??)
multiprocessing.Process(??)
multiprocessing.ProcessError(??)
multiprocessing.Queue(??)
multiprocessing.RLock(??)
multiprocessing.RawArray(??)
multiprocessing.RawValue(??)
multiprocessing.SUBDEBUG
multiprocessing.SUBWARNING
multiprocessing.Semaphore(??)
multiprocessing.SimpleQueue(??)
multiprocessing.TimeoutError(??)
multiprocessing.Value(??)
multiprocessing.active_children(??)
multiprocessing.allow_connection_pickling(??)
multiprocessing.connection(??)
multiprocessing.connection.AuthenticationError(??)
multiprocessing.connection.BUFSIZE
multiprocessing.connection.BufferTooShort(??)
multiprocessing.connection.CHALLENGE
multiprocessing.connection.CONNECTION_TIMEOUT
multiprocessing.connection.Client(??)
multiprocessing.connection.Connection(??)
multiprocessing.connection.ConnectionWrapper(??)
multiprocessing.connection.FAILURE
multiprocessing.connection.Listener(??)
multiprocessing.connection.MESSAGE_LENGTH
multiprocessing.connection.Pipe(??)
multiprocessing.connection.SocketClient(??)
multiprocessing.connection.SocketListener(??)
multiprocessing.connection.WELCOME
multiprocessing.connection.XmlClient(??)
multiprocessing.connection.XmlListener(??)
multiprocessing.connection.address_type(??)
multiprocessing.connection.answer_challenge(??)
multiprocessing.connection.arbitrary_address(??)
multiprocessing.connection.default_family
multiprocessing.connection.deliver_challenge(??)
multiprocessing.connection.families(??)
multiprocessing.connection.io(??)
multiprocessing.connection.itertools(??)
multiprocessing.connection.os(??)
multiprocessing.connection.rebuild_connection(??)
multiprocessing.connection.reduce_connection(??)
multiprocessing.connection.reduction(??)
multiprocessing.connection.selectors(??)
multiprocessing.connection.socket(??)
multiprocessing.connection.struct(??)
multiprocessing.connection.sys(??)
multiprocessing.connection.tempfile(??)
multiprocessing.connection.time(??)
multiprocessing.connection.util(??)
multiprocessing.connection.wait(??)
multiprocessing.context(??)
multiprocessing.context.AuthenticationError(??)
multiprocessing.context.BaseContext(??)
multiprocessing.context.BufferTooShort(??)
multiprocessing.context.DefaultContext(??)
multiprocessing.context.ForkContext(??)
multiprocessing.context.ForkProcess(??)
multiprocessing.context.ForkServerContext(??)
multiprocessing.context.ForkServerProcess(??)
multiprocessing.context.Process(??)
multiprocessing.context.ProcessError(??)
multiprocessing.context.SpawnContext(??)
multiprocessing.context.SpawnProcess(??)
multiprocessing.context.TimeoutError(??)
multiprocessing.context.assert_spawning(??)
multiprocessing.context.get_spawning_popen(??)
multiprocessing.context.os(??)
multiprocessing.context.process(??)
multiprocessing.context.reduction(??)
multiprocessing.context.set_spawning_popen(??)
multiprocessing.context.sys(??)
multiprocessing.context.threading(??)
multiprocessing.cpu_count(??)
multiprocessing.current_process(??)
multiprocessing.dummy(??)
multiprocessing.dummy.Array(??)
multiprocessing.dummy.Barrier(??)
multiprocessing.dummy.BoundedSemaphore(??)
multiprocessing.dummy.Condition(??)
multiprocessing.dummy.DummyProcess(??)
multiprocessing.dummy.Event(??)
multiprocessing.dummy.JoinableQueue(??)
multiprocessing.dummy.Lock(??)
multiprocessing.dummy.Manager(??)
multiprocessing.dummy.Namespace(??)
multiprocessing.dummy.Pipe(??)
multiprocessing.dummy.Pool(??)
multiprocessing.dummy.Process(??)
multiprocessing.dummy.Queue(??)
multiprocessing.dummy.RLock(??)
multiprocessing.dummy.Semaphore(??)
multiprocessing.dummy.Value(??)
multiprocessing.dummy.active_children(??)
multiprocessing.dummy.array(??)
multiprocessing.dummy.connection(??)
multiprocessing.dummy.connection.Client(??)
multiprocessing.dummy.connection.Connection(??)
multiprocessing.dummy.connection.Listener(??)
multiprocessing.dummy.connection.Pipe(??)
multiprocessing.dummy.connection.Queue(??)
multiprocessing.dummy.connection.families(??)
multiprocessing.dummy.current_process(??)
multiprocessing.dummy.dict() -> new empty dictionary
multiprocessing.dummy.freeze_support(??)
multiprocessing.dummy.list(??)
multiprocessing.dummy.shutdown(??)
multiprocessing.dummy.sys(??)
multiprocessing.dummy.threading(??)
multiprocessing.dummy.weakref(??)
multiprocessing.forkserver.ForkServer(??)
multiprocessing.forkserver.MAXFDS_TO_SEND
multiprocessing.forkserver.SIGNED_STRUCT(??)
multiprocessing.forkserver.connect_to_new_process(??)
multiprocessing.forkserver.connection(??)
multiprocessing.forkserver.ensure_running(??)
multiprocessing.forkserver.errno(??)
multiprocessing.forkserver.get_inherited_fds(??)
multiprocessing.forkserver.main(??)
multiprocessing.forkserver.os(??)
multiprocessing.forkserver.process(??)
multiprocessing.forkserver.read_signed(??)
multiprocessing.forkserver.reduction(??)
multiprocessing.forkserver.resource_tracker(??)
multiprocessing.forkserver.selectors(??)
multiprocessing.forkserver.set_forkserver_preload(??)
multiprocessing.forkserver.signal(??)
multiprocessing.forkserver.socket(??)
multiprocessing.forkserver.spawn(??)
multiprocessing.forkserver.struct(??)
multiprocessing.forkserver.sys(??)
multiprocessing.forkserver.threading(??)
multiprocessing.forkserver.util(??)
multiprocessing.forkserver.warnings(??)
multiprocessing.forkserver.write_signed(??)
multiprocessing.freeze_support(??)
multiprocessing.get_all_start_methods(??)
multiprocessing.get_context(??)
multiprocessing.get_logger(??)
multiprocessing.get_start_method(??)
multiprocessing.heap(??)
multiprocessing.heap.Arena(??)
multiprocessing.heap.BufferWrapper(??)
multiprocessing.heap.Heap(??)
multiprocessing.heap.assert_spawning(??)
multiprocessing.heap.bisect(??)
multiprocessing.heap.defaultdict(default_factory=None, /, [...]) --> dict with default factory
multiprocessing.heap.mmap(??)
multiprocessing.heap.os(??)
multiprocessing.heap.rebuild_arena(??)
multiprocessing.heap.reduce_arena(??)
multiprocessing.heap.reduction(??)
multiprocessing.heap.sys(??)
multiprocessing.heap.tempfile(??)
multiprocessing.heap.threading(??)
multiprocessing.heap.util(??)
multiprocessing.log_to_stderr(??)
multiprocessing.managers.AcquirerProxy(??)
multiprocessing.managers.Array(??)
multiprocessing.managers.ArrayProxy(??)
multiprocessing.managers.AutoProxy(??)
multiprocessing.managers.BarrierProxy(??)
multiprocessing.managers.BaseListProxy(??)
multiprocessing.managers.BaseManager(??)
multiprocessing.managers.BasePoolProxy(??)
multiprocessing.managers.BaseProxy(??)
multiprocessing.managers.ConditionProxy(??)
multiprocessing.managers.DictProxy(??)
multiprocessing.managers.EventProxy(??)
multiprocessing.managers.HAS_SHMEM(??)
multiprocessing.managers.IteratorProxy(??)
multiprocessing.managers.ListProxy(??)
multiprocessing.managers.MakeProxyType(??)
multiprocessing.managers.Namespace(??)
multiprocessing.managers.NamespaceProxy(??)
multiprocessing.managers.PoolProxy(??)
multiprocessing.managers.ProcessError(??)
multiprocessing.managers.ProcessLocalSet(??)
multiprocessing.managers.RebuildProxy(??)
multiprocessing.managers.RemoteError(??)
multiprocessing.managers.Server(??)
multiprocessing.managers.SharedMemoryManager(??)
multiprocessing.managers.SharedMemoryServer(??)
multiprocessing.managers.State(??)
multiprocessing.managers.SyncManager(??)
multiprocessing.managers.Token(??)
multiprocessing.managers.Value(??)
multiprocessing.managers.ValueProxy(??)
multiprocessing.managers.all_methods(??)
multiprocessing.managers.array(??)
multiprocessing.managers.connection(??)
multiprocessing.managers.convert_to_error(??)
multiprocessing.managers.dispatch(??)
multiprocessing.managers.format_exc(??)
multiprocessing.managers.get_context(??)
multiprocessing.managers.get_spawning_popen(??)
multiprocessing.managers.getpid(??)
multiprocessing.managers.listener_client(??)
multiprocessing.managers.os(??)
multiprocessing.managers.pool(??)
multiprocessing.managers.process(??)
multiprocessing.managers.public_methods(??)
multiprocessing.managers.queue(??)
multiprocessing.managers.rebuild_as_list(??)
multiprocessing.managers.reduce_array(??)
multiprocessing.managers.reduction(??)
multiprocessing.managers.shared_memory(??)
multiprocessing.managers.signal(??)
multiprocessing.managers.sys(??)
multiprocessing.managers.threading(??)
multiprocessing.managers.time(??)
multiprocessing.managers.types(??)
multiprocessing.managers.util(??)
multiprocessing.parent_process(??)
multiprocessing.pool.ApplyResult(??)
multiprocessing.pool.AsyncResult(??)
multiprocessing.pool.CLOSE
multiprocessing.pool.ExceptionWithTraceback(??)
multiprocessing.pool.IMapIterator(??)
multiprocessing.pool.IMapUnorderedIterator(??)
multiprocessing.pool.INIT
multiprocessing.pool.MapResult(??)
multiprocessing.pool.MaybeEncodingError(??)
multiprocessing.pool.Pool(??)
multiprocessing.pool.RUN
multiprocessing.pool.RemoteTraceback(??)
multiprocessing.pool.TERMINATE
multiprocessing.pool.ThreadPool(??)
multiprocessing.pool.TimeoutError(??)
multiprocessing.pool.collections(??)
multiprocessing.pool.get_context(??)
multiprocessing.pool.itertools(??)
multiprocessing.pool.job_counter(??)
multiprocessing.pool.mapstar(??)
multiprocessing.pool.os(??)
multiprocessing.pool.queue(??)
multiprocessing.pool.rebuild_exc(??)
multiprocessing.pool.starmapstar(??)
multiprocessing.pool.threading(??)
multiprocessing.pool.time(??)
multiprocessing.pool.traceback(??)
multiprocessing.pool.types(??)
multiprocessing.pool.util(??)
multiprocessing.pool.wait(??)
multiprocessing.pool.warnings(??)
multiprocessing.pool.worker(??)
multiprocessing.popen_fork.Popen(??)
multiprocessing.popen_fork.os(??)
multiprocessing.popen_fork.signal(??)
multiprocessing.popen_fork.util(??)
multiprocessing.popen_forkserver.Popen(??)
multiprocessing.popen_forkserver.forkserver(??)
multiprocessing.popen_forkserver.io(??)
multiprocessing.popen_forkserver.os(??)
multiprocessing.popen_forkserver.popen_fork(??)
multiprocessing.popen_forkserver.reduction(??)
multiprocessing.popen_forkserver.set_spawning_popen(??)
multiprocessing.popen_forkserver.spawn(??)
multiprocessing.popen_forkserver.util(??)
multiprocessing.popen_spawn_posix.Popen(??)
multiprocessing.popen_spawn_posix.io(??)
multiprocessing.popen_spawn_posix.os(??)
multiprocessing.popen_spawn_posix.popen_fork(??)
multiprocessing.popen_spawn_posix.reduction(??)
multiprocessing.popen_spawn_posix.set_spawning_popen(??)
multiprocessing.popen_spawn_posix.spawn(??)
multiprocessing.popen_spawn_posix.util(??)
multiprocessing.process(??)
multiprocessing.process.AuthenticationString(??)
multiprocessing.process.BaseProcess(??)
multiprocessing.process.ORIGINAL_DIR
multiprocessing.process.WeakSet(??)
multiprocessing.process.active_children(??)
multiprocessing.process.current_process(??)
multiprocessing.process.itertools(??)
multiprocessing.process.os(??)
multiprocessing.process.parent_process(??)
multiprocessing.process.signal(??)
multiprocessing.process.sys(??)
multiprocessing.process.threading(??)
multiprocessing.queues(??)
multiprocessing.queues.Empty(??)
multiprocessing.queues.Finalize(??)
multiprocessing.queues.Full(??)
multiprocessing.queues.JoinableQueue(??)
multiprocessing.queues.Queue(??)
multiprocessing.queues.SimpleQueue(??)
multiprocessing.queues.collections(??)
multiprocessing.queues.connection(??)
multiprocessing.queues.context(??)
multiprocessing.queues.debug(??)
multiprocessing.queues.errno(??)
multiprocessing.queues.info(??)
multiprocessing.queues.is_exiting(??)
multiprocessing.queues.os(??)
multiprocessing.queues.register_after_fork(??)
multiprocessing.queues.sys(??)
multiprocessing.queues.threading(??)
multiprocessing.queues.time(??)
multiprocessing.queues.types(??)
multiprocessing.queues.weakref(??)
multiprocessing.reducer(??)
multiprocessing.reduction(??)
multiprocessing.reduction.ABCMeta(??)
multiprocessing.reduction.ACKNOWLEDGE(??)
multiprocessing.reduction.AbstractReducer(??)
multiprocessing.reduction.DupFd(??)
multiprocessing.reduction.ForkingPickler(??)
multiprocessing.reduction.HAVE_SEND_HANDLE(??)
multiprocessing.reduction.array(??)
multiprocessing.reduction.context(??)
multiprocessing.reduction.copyreg(??)
multiprocessing.reduction.dump(??)
multiprocessing.reduction.functools(??)
multiprocessing.reduction.io(??)
multiprocessing.reduction.os(??)
multiprocessing.reduction.pickle(??)
multiprocessing.reduction.recv_handle(??)
multiprocessing.reduction.recvfds(??)
multiprocessing.reduction.register(??)
multiprocessing.reduction.send_handle(??)
multiprocessing.reduction.sendfds(??)
multiprocessing.reduction.socket(??)
multiprocessing.reduction.sys(??)
multiprocessing.resource_sharer.DupFd(??)
multiprocessing.resource_sharer.os(??)
multiprocessing.resource_sharer.process(??)
multiprocessing.resource_sharer.reduction(??)
multiprocessing.resource_sharer.signal(??)
multiprocessing.resource_sharer.socket(??)
multiprocessing.resource_sharer.stop(??)
multiprocessing.resource_sharer.sys(??)
multiprocessing.resource_sharer.threading(??)
multiprocessing.resource_sharer.util(??)
multiprocessing.resource_tracker(??)
multiprocessing.resource_tracker.ResourceTracker(??)
multiprocessing.resource_tracker.ensure_running(??)
multiprocessing.resource_tracker.getfd(??)
multiprocessing.resource_tracker.main(??)
multiprocessing.resource_tracker.os(??)
multiprocessing.resource_tracker.register(??)
multiprocessing.resource_tracker.signal(??)
multiprocessing.resource_tracker.spawn(??)
multiprocessing.resource_tracker.sys(??)
multiprocessing.resource_tracker.threading(??)
multiprocessing.resource_tracker.unregister(??)
multiprocessing.resource_tracker.util(??)
multiprocessing.resource_tracker.warnings(??)
multiprocessing.set_executable(??)
multiprocessing.set_forkserver_preload(??)
multiprocessing.set_start_method(??)
multiprocessing.shared_memory(??)
multiprocessing.shared_memory.ShareableList(??)
multiprocessing.shared_memory.SharedMemory(??)
multiprocessing.shared_memory.errno(??)
multiprocessing.shared_memory.mmap(??)
multiprocessing.shared_memory.os(??)
multiprocessing.shared_memory.partial(func, *args, **keywords) - new function with partial application
multiprocessing.shared_memory.secrets(??)
multiprocessing.shared_memory.struct(??)
multiprocessing.shared_memory.types(??)
multiprocessing.sharedctypes.Array(??)
multiprocessing.sharedctypes.RawArray(??)
multiprocessing.sharedctypes.RawValue(??)
multiprocessing.sharedctypes.Synchronized(??)
multiprocessing.sharedctypes.SynchronizedArray(??)
multiprocessing.sharedctypes.SynchronizedBase(??)
multiprocessing.sharedctypes.SynchronizedString(??)
multiprocessing.sharedctypes.Value(??)
multiprocessing.sharedctypes.assert_spawning(??)
multiprocessing.sharedctypes.class_cache(??)
multiprocessing.sharedctypes.copy(??)
multiprocessing.sharedctypes.ctypes(??)
multiprocessing.sharedctypes.get_context(??)
multiprocessing.sharedctypes.heap(??)
multiprocessing.sharedctypes.make_property(??)
multiprocessing.sharedctypes.prop_cache(??)
multiprocessing.sharedctypes.rebuild_ctype(??)
multiprocessing.sharedctypes.reduce_ctype(??)
multiprocessing.sharedctypes.reduction(??)
multiprocessing.sharedctypes.synchronized(??)
multiprocessing.sharedctypes.template
multiprocessing.sharedctypes.typecode_to_type(??)
multiprocessing.sharedctypes.weakref(??)
multiprocessing.spawn(??)
multiprocessing.spawn.WINEXE(??)
multiprocessing.spawn.WINSERVICE(??)
multiprocessing.spawn.freeze_support(??)
multiprocessing.spawn.get_command_line(??)
multiprocessing.spawn.get_executable(??)
multiprocessing.spawn.get_preparation_data(??)
multiprocessing.spawn.get_start_method(??)
multiprocessing.spawn.import_main_path(??)
multiprocessing.spawn.is_forking(??)
multiprocessing.spawn.old_main_modules(??)
multiprocessing.spawn.os(??)
multiprocessing.spawn.prepare(??)
multiprocessing.spawn.process(??)
multiprocessing.spawn.reduction(??)
multiprocessing.spawn.runpy(??)
multiprocessing.spawn.set_executable(??)
multiprocessing.spawn.set_start_method(??)
multiprocessing.spawn.spawn_main(??)
multiprocessing.spawn.sys(??)
multiprocessing.spawn.types(??)
multiprocessing.spawn.util(??)
multiprocessing.synchronize.Barrier(??)
multiprocessing.synchronize.BoundedSemaphore(??)
multiprocessing.synchronize.Condition(??)
multiprocessing.synchronize.Event(??)
multiprocessing.synchronize.Lock(??)
multiprocessing.synchronize.RECURSIVE_MUTEX
multiprocessing.synchronize.RLock(??)
multiprocessing.synchronize.SEMAPHORE
multiprocessing.synchronize.SEM_VALUE_MAX
multiprocessing.synchronize.SemLock(??)
multiprocessing.synchronize.Semaphore(??)
multiprocessing.synchronize.context(??)
multiprocessing.synchronize.process(??)
multiprocessing.synchronize.sem_unlink(??)
multiprocessing.synchronize.sys(??)
multiprocessing.synchronize.tempfile(??)
multiprocessing.synchronize.threading(??)
multiprocessing.synchronize.time(??)
multiprocessing.synchronize.util(??)
multiprocessing.sys(??)
multiprocessing.util(??)
multiprocessing.util.DEBUG
multiprocessing.util.DEFAULT_LOGGING_FORMAT
multiprocessing.util.Finalize(??)
multiprocessing.util.ForkAwareLocal(??)
multiprocessing.util.ForkAwareThreadLock(??)
multiprocessing.util.INFO
multiprocessing.util.LOGGER_NAME
multiprocessing.util.MAXFD
multiprocessing.util.NOTSET
multiprocessing.util.SUBDEBUG
multiprocessing.util.SUBWARNING
multiprocessing.util.abstract_sockets_supported(??)
multiprocessing.util.atexit(??)
multiprocessing.util.close_all_fds_except(??)
multiprocessing.util.close_fds(??)
multiprocessing.util.debug(??)
multiprocessing.util.get_logger(??)
multiprocessing.util.get_temp_dir(??)
multiprocessing.util.info(??)
multiprocessing.util.is_abstract_socket_namespace(??)
multiprocessing.util.is_exiting(??)
multiprocessing.util.itertools(??)
multiprocessing.util.log_to_stderr(??)
multiprocessing.util.os(??)
multiprocessing.util.process(??)
multiprocessing.util.register_after_fork(??)
multiprocessing.util.spawnv_passfds(??)
multiprocessing.util.sub_debug(??)
multiprocessing.util.sub_warning(??)
multiprocessing.util.sys(??)
multiprocessing.util.threading(??)
multiprocessing.util.weakref(??)
netrc.NetrcParseError(??)
netrc.netrc(??)
netrc.os(??)
netrc.shlex(??)
netrc.stat(??)
next(iterator[, default])
nntplib.ArticleInfo(number, message_id, lines)
nntplib.GroupInfo(group, last, first, flag)
nntplib.NNTP(??)
nntplib.NNTPDataError(??)
nntplib.NNTPError(??)
nntplib.NNTPPermanentError(??)
nntplib.NNTPProtocolError(??)
nntplib.NNTPReplyError(??)
nntplib.NNTPTemporaryError(??)
nntplib.NNTP_PORT
nntplib.NNTP_SSL(??)
nntplib.NNTP_SSL_PORT
nntplib.collections(??)
nntplib.datetime(??)
nntplib.decode_header(??)
nntplib.re(??)
nntplib.socket(??)
nntplib.ssl(??)
nntplib.sys(??)
nntplib.warnings(??)
numbers.ABCMeta(??)
numbers.Complex(??)
numbers.Integral(??)
numbers.Number(??)
numbers.Rational(??)
numbers.Real(??)
numbers.abstractmethod(??)
object(??)
oct(??)
open(??)
operator.abs(??)
operator.add(??)
operator.and_(??)
operator.attrgetter(attr, ...) --> attrgetter object
operator.call(??)
operator.concat(??)
operator.contains(??)
operator.countOf(??)
operator.delitem(??)
operator.eq(??)
operator.floordiv(??)
operator.ge(??)
operator.getitem(??)
operator.gt(??)
operator.iadd(??)
operator.iand(??)
operator.iconcat(??)
operator.ifloordiv(??)
operator.ilshift(??)
operator.imatmul(??)
operator.imod(??)
operator.imul(??)
operator.index(??)
operator.indexOf(??)
operator.inv(??)
operator.invert(??)
operator.ior(??)
operator.ipow(??)
operator.irshift(??)
operator.is_(??)
operator.is_not(??)
operator.isub(??)
operator.itemgetter(item, ...) --> itemgetter object
operator.itruediv(??)
operator.ixor(??)
operator.le(??)
operator.length_hint(??)
operator.lshift(??)
operator.lt(??)
operator.matmul(??)
operator.methodcaller(name, ...) --> methodcaller object
operator.mod(??)
operator.mul(??)
operator.ne(??)
operator.neg(??)
operator.not_(??)
operator.or_(??)
operator.pos(??)
operator.pow(??)
operator.rshift(??)
operator.setitem(??)
operator.sub(??)
operator.truediv(??)
operator.truth(??)
operator.xor(??)
optparse.AmbiguousOptionError(??)
optparse.BadOptionError(??)
optparse.HelpFormatter(??)
optparse.IndentedHelpFormatter(??)
optparse.NO_DEFAULT(??)
optparse.OptParseError(??)
optparse.Option(??)
optparse.OptionConflictError(??)
optparse.OptionContainer(??)
optparse.OptionError(??)
optparse.OptionGroup(??)
optparse.OptionParser(??)
optparse.OptionValueError(??)
optparse.SUPPRESS_HELP
optparse.SUPPRESS_USAGE
optparse.TitledHelpFormatter(??)
optparse.Values(??)
optparse.check_builtin(??)
optparse.check_choice(??)
optparse.gettext(??)
optparse.make_option(??)
optparse.ngettext(??)
optparse.os(??)
optparse.sys(??)
optparse.textwrap(??)
ord(??)
os.CLD_CONTINUED
os.CLD_DUMPED
os.CLD_EXITED
os.CLD_KILLED
os.CLD_STOPPED
os.CLD_TRAPPED
os.DirEntry(??)
os.EX_CANTCREAT
os.EX_CONFIG
os.EX_DATAERR
os.EX_IOERR
os.EX_NOHOST
os.EX_NOINPUT
os.EX_NOPERM
os.EX_NOUSER
os.EX_OK
os.EX_OSERR
os.EX_OSFILE
os.EX_PROTOCOL
os.EX_SOFTWARE
os.EX_TEMPFAIL
os.EX_UNAVAILABLE
os.EX_USAGE
os.F_LOCK
os.F_OK
os.F_TEST
os.F_TLOCK
os.F_ULOCK
os.GenericAlias(??)
os.Mapping(??)
os.MutableMapping(??)
os.NGROUPS_MAX
os.O_ACCMODE
os.O_APPEND
os.O_ASYNC
os.O_CLOEXEC
os.O_CREAT
os.O_DIRECTORY
os.O_DSYNC
os.O_EVTONLY
os.O_EXCL
os.O_EXLOCK
os.O_FSYNC
os.O_NDELAY
os.O_NOCTTY
os.O_NOFOLLOW
os.O_NOFOLLOW_ANY
os.O_NONBLOCK
os.O_RDONLY
os.O_RDWR
os.O_SHLOCK
os.O_SYMLINK
os.O_SYNC
os.O_TRUNC
os.O_WRONLY
os.POSIX_SPAWN_CLOSE
os.POSIX_SPAWN_DUP2
os.POSIX_SPAWN_OPEN
os.PRIO_PGRP
os.PRIO_PROCESS
os.PRIO_USER
os.P_ALL
os.P_NOWAIT
os.P_NOWAITO
os.P_PGID
os.P_PID
os.P_WAIT
os.PathLike(??)
os.RTLD_GLOBAL
os.RTLD_LAZY
os.RTLD_LOCAL
os.RTLD_NODELETE
os.RTLD_NOLOAD
os.RTLD_NOW
os.R_OK
os.SCHED_FIFO
os.SCHED_OTHER
os.SCHED_RR
os.SEEK_CUR
os.SEEK_DATA
os.SEEK_END
os.SEEK_HOLE
os.SEEK_SET
os.ST_NOSUID
os.ST_RDONLY
os.TMP_MAX
os.WCONTINUED
os.WCOREDUMP(??)
os.WEXITED
os.WEXITSTATUS(??)
os.WIFCONTINUED(??)
os.WIFEXITED(??)
os.WIFSIGNALED(??)
os.WIFSTOPPED(??)
os.WNOHANG
os.WNOWAIT
os.WSTOPPED
os.WSTOPSIG(??)
os.WTERMSIG(??)
os.WUNTRACED
os.W_OK
os.X_OK
os.abc(??)
os.abort(??)
os.access(??)
os.altsep(??)
os.chdir(??)
os.chflags(??)
os.chmod(??)
os.chown(??)
os.chroot(??)
os.close(??)
os.closerange(??)
os.confstr(??)
os.confstr_names(??)
os.cpu_count(??)
os.ctermid(??)
os.curdir
os.defpath
os.device_encoding(??)
os.devnull
os.dup(??)
os.dup2(??)
os.environ(??)
os.environb(??)
os.error(??)
os.execl(file, *args)
os.execle(file, *args, env)
os.execlp(file, *args)
os.execlpe(file, *args, env)
os.execv(??)
os.execve(??)
os.execvp(file, args)
os.execvpe(file, args, env)
os.extsep
os.fchdir(??)
os.fchmod(??)
os.fchown(??)
os.fdopen(??)
os.fork(??)
os.forkpty(??)
os.fpathconf(??)
os.fsdecode(??)
os.fsencode(??)
os.fspath(??)
os.fstat(??)
os.fstatvfs(??)
os.fsync(??)
os.ftruncate(??)
os.fwalk(??)
os.get_blocking(??)
os.get_exec_path(??)
os.get_inheritable(??)
os.get_terminal_size(??)
os.getcwd(??)
os.getcwdb(??)
os.getegid(??)
os.getenv(??)
os.getenvb(??)
os.geteuid(??)
os.getgid(??)
os.getgrouplist(??)
os.getgroups(??)
os.getloadavg(??)
os.getlogin(??)
os.getpgid(??)
os.getpgrp(??)
os.getpid(??)
os.getppid(??)
os.getpriority(??)
os.getsid(??)
os.getuid(??)
os.initgroups(??)
os.isatty(??)
os.kill(??)
os.killpg(??)
os.lchflags(??)
os.lchmod(??)
os.lchown(??)
os.linesep
os.link(??)
os.listdir(??)
os.lockf(??)
os.login_tty(??)
os.lseek(??)
os.lstat(??)
os.major(??)
os.makedev(??)
os.makedirs(name [, mode=0o777][, exist_ok=False])
os.minor(??)
os.mkdir(??)
os.mkfifo(??)
os.mknod(??)
os.name
os.nice(??)
os.open(??)
os.openpty(??)
os.pardir
os.path(??)
os.pathconf(??)
os.pathconf_names(??)
os.pathsep
os.pipe(??)
os.popen(??)
os.posix_spawn(??)
os.posix_spawnp(??)
os.pread(??)
os.preadv(??)
os.putenv(??)
os.pwrite(??)
os.pwritev(??)
os.read(??)
os.readlink(??)
os.readv(??)
os.register_at_fork(??)
os.remove(??)
os.removedirs(name)
os.rename(??)
os.renames(old, new)
os.replace(??)
os.rmdir(??)
os.scandir(??)
os.sched_get_priority_max(??)
os.sched_get_priority_min(??)
os.sched_yield(??)
os.sendfile(??)
os.sep
os.set_blocking(??)
os.set_inheritable(??)
os.setegid(??)
os.seteuid(??)
os.setgid(??)
os.setgroups(??)
os.setpgid(??)
os.setpgrp(??)
os.setpriority(??)
os.setregid(??)
os.setreuid(??)
os.setsid(??)
os.setuid(??)
os.spawnl(mode, file, *args) -> integer
os.spawnle(mode, file, *args, env) -> integer
os.spawnlp(mode, file, *args) -> integer
os.spawnlpe(mode, file, *args, env) -> integer
os.spawnv(mode, file, args) -> integer
os.spawnve(mode, file, args, env) -> integer
os.spawnvp(mode, file, args) -> integer
os.spawnvpe(mode, file, args, env) -> integer
os.st(??)
os.stat(??)
os.stat_result(??)
os.statvfs(??)
os.statvfs_result(??)
os.strerror(??)
os.supports_bytes_environ(??)
os.supports_dir_fd(??)
os.supports_effective_ids(??)
os.supports_fd(??)
os.supports_follow_symlinks(??)
os.symlink(??)
os.sync(??)
os.sys(??)
os.sysconf(??)
os.sysconf_names(??)
os.system(??)
os.tcgetpgrp(??)
os.tcsetpgrp(??)
os.terminal_size(??)
os.times(??)
os.times_result(??)
os.truncate(??)
os.ttyname(??)
os.umask(??)
os.uname(??)
os.uname_result(??)
os.unlink(??)
os.unsetenv(??)
os.urandom(??)
os.utime(??)
os.wait(??)
os.wait3(??)
os.wait4(??)
os.waitpid(??)
os.waitstatus_to_exitcode(??)
os.walk(??)
os.write(??)
os.writev(??)
pathlib.EBADF
pathlib.ELOOP
pathlib.ENOENT
pathlib.ENOTDIR
pathlib.Path(??)
pathlib.PosixPath(??)
pathlib.PurePath(??)
pathlib.PurePosixPath(??)
pathlib.PureWindowsPath(??)
pathlib.S_ISBLK(mode) -> bool
pathlib.S_ISCHR(mode) -> bool
pathlib.S_ISDIR(mode) -> bool
pathlib.S_ISFIFO(mode) -> bool
pathlib.S_ISLNK(mode) -> bool
pathlib.S_ISREG(mode) -> bool
pathlib.S_ISSOCK(mode) -> bool
pathlib.Sequence(??)
pathlib.WindowsPath(??)
pathlib.attrgetter(attr, ...) --> attrgetter object
pathlib.fnmatch(??)
pathlib.functools(??)
pathlib.io(??)
pathlib.ntpath(??)
pathlib.os(??)
pathlib.posixpath(??)
pathlib.re(??)
pathlib.sys(??)
pathlib.urlquote_from_bytes(??)
pathlib.warnings(??)
pdb.ModuleTarget(??)
pdb.Pdb(??)
pdb.Restart(??)
pdb.ScriptTarget(??)
pdb.TESTCMD
pdb.Union(??)
pdb.bdb(??)
pdb.cmd(??)
pdb.code(??)
pdb.dis(??)
pdb.find_function(??)
pdb.functools(??)
pdb.getsourcelines(??)
pdb.glob(??)
pdb.help(??)
pdb.inspect(??)
pdb.io(??)
pdb.lasti2lineno(??)
pdb.line_prefix
pdb.linecache(??)
pdb.main(??)
pdb.os(??)
pdb.pm(??)
pdb.post_mortem(??)
pdb.pprint(??)
pdb.re(??)
pdb.run(??)
pdb.runcall(??)
pdb.runctx(??)
pdb.runeval(??)
pdb.set_trace(??)
pdb.signal(??)
pdb.sys(??)
pdb.test(??)
pdb.tokenize(??)
pdb.traceback(??)
pickle.ADDITEMS
pickle.APPEND
pickle.APPENDS
pickle.BINBYTES
pickle.BINBYTES8
pickle.BINFLOAT
pickle.BINGET
pickle.BININT
pickle.BININT1
pickle.BININT2
pickle.BINPERSID
pickle.BINPUT
pickle.BINSTRING
pickle.BINUNICODE
pickle.BINUNICODE8
pickle.BUILD
pickle.BYTEARRAY8
pickle.DEFAULT_PROTOCOL
pickle.DICT
pickle.DUP
pickle.EMPTY_DICT
pickle.EMPTY_LIST
pickle.EMPTY_SET
pickle.EMPTY_TUPLE
pickle.EXT1
pickle.EXT2
pickle.EXT4
pickle.FALSE
pickle.FLOAT
pickle.FRAME
pickle.FROZENSET
pickle.FunctionType(??)
pickle.GET
pickle.GLOBAL
pickle.HIGHEST_PROTOCOL
pickle.INST
pickle.INT
pickle.LIST
pickle.LONG
pickle.LONG1
pickle.LONG4
pickle.LONG_BINGET
pickle.LONG_BINPUT
pickle.MARK
pickle.MEMOIZE
pickle.NEWFALSE
pickle.NEWOBJ
pickle.NEWOBJ_EX
pickle.NEWTRUE
pickle.NEXT_BUFFER
pickle.NONE
pickle.OBJ
pickle.PERSID
pickle.POP
pickle.POP_MARK
pickle.PROTO
pickle.PUT
pickle.PickleBuffer(??)
pickle.PickleError(??)
pickle.Pickler(??)
pickle.PicklingError(??)
pickle.PyStringMap(??)
pickle.READONLY_BUFFER
pickle.REDUCE
pickle.SETITEM
pickle.SETITEMS
pickle.SHORT_BINBYTES
pickle.SHORT_BINSTRING
pickle.SHORT_BINUNICODE
pickle.STACK_GLOBAL
pickle.STOP
pickle.STRING
pickle.TRUE
pickle.TUPLE
pickle.TUPLE1
pickle.TUPLE2
pickle.TUPLE3
pickle.UNICODE
pickle.Unpickler(??)
pickle.UnpicklingError(??)
pickle.bytes_types(??)
pickle.codecs(??)
pickle.compatible_formats(??)
pickle.decode_long(??)
pickle.dispatch_table(??)
pickle.dump(??)
pickle.dumps(??)
pickle.encode_long(??)
pickle.format_version
pickle.io(??)
pickle.islice(iterable, stop) --> islice object
pickle.load(??)
pickle.loads(??)
pickle.maxsize
pickle.pack(format, v1, v2, ...) -> bytes
pickle.partial(func, *args, **keywords) - new function with partial application
pickle.re(??)
pickle.sys(??)
pickle.unpack(??)
pickle.whichmodule(??)
pickletools.ArgumentDescriptor(??)
pickletools.OpcodeInfo(??)
pickletools.StackObject(??)
pickletools.TAKEN_FROM_ARGUMENT1
pickletools.TAKEN_FROM_ARGUMENT4
pickletools.TAKEN_FROM_ARGUMENT4U
pickletools.TAKEN_FROM_ARGUMENT8U
pickletools.UP_TO_NEWLINE
pickletools.anyobject(??)
pickletools.bytearray8(??)
pickletools.bytes1(??)
pickletools.bytes4(??)
pickletools.bytes8(??)
pickletools.bytes_types(??)
pickletools.code2op(??)
pickletools.codecs(??)
pickletools.decimalnl_long(??)
pickletools.decimalnl_short(??)
pickletools.decode_long(??)
pickletools.dis(??)
pickletools.float8(??)
pickletools.floatnl(??)
pickletools.genops(??)
pickletools.int4(??)
pickletools.io(??)
pickletools.long1(??)
pickletools.long4(??)
pickletools.markobject(??)
pickletools.opcodes(??)
pickletools.optimize(??)
pickletools.pickle(??)
pickletools.pybool(??)
pickletools.pybuffer(??)
pickletools.pybytearray(??)
pickletools.pybytes(??)
pickletools.pybytes_or_str(??)
pickletools.pydict(??)
pickletools.pyfloat(??)
pickletools.pyfrozenset(??)
pickletools.pyint(??)
pickletools.pyinteger_or_bool(??)
pickletools.pylist(??)
pickletools.pylong(??)
pickletools.pynone(??)
pickletools.pyset(??)
pickletools.pystring(??)
pickletools.pytuple(??)
pickletools.pyunicode(??)
pickletools.re(??)
pickletools.read_bytearray8(??)
pickletools.read_bytes1(??)
pickletools.read_bytes4(??)
pickletools.read_bytes8(??)
pickletools.read_decimalnl_long(??)
pickletools.read_decimalnl_short(??)
pickletools.read_float8(??)
pickletools.read_floatnl(??)
pickletools.read_int4(??)
pickletools.read_long1(??)
pickletools.read_long4(??)
pickletools.read_string1(??)
pickletools.read_string4(??)
pickletools.read_stringnl(??)
pickletools.read_stringnl_noescape(??)
pickletools.read_stringnl_noescape_pair(??)
pickletools.read_uint1(??)
pickletools.read_uint2(??)
pickletools.read_uint4(??)
pickletools.read_uint8(??)
pickletools.read_unicodestring1(??)
pickletools.read_unicodestring4(??)
pickletools.read_unicodestring8(??)
pickletools.read_unicodestringnl(??)
pickletools.stackslice(??)
pickletools.string1(??)
pickletools.string4(??)
pickletools.stringnl(??)
pickletools.stringnl_noescape(??)
pickletools.stringnl_noescape_pair(??)
pickletools.sys(??)
pickletools.uint1(??)
pickletools.uint2(??)
pickletools.uint4(??)
pickletools.uint8(??)
pickletools.unicodestring1(??)
pickletools.unicodestring4(??)
pickletools.unicodestring8(??)
pickletools.unicodestringnl(??)
pipes.FILEIN_FILEOUT
pipes.FILEIN_STDOUT
pipes.SINK
pipes.SOURCE
pipes.STDIN_FILEOUT
pipes.STDIN_STDOUT
pipes.Template(??)
pipes.makepipeline(??)
pipes.os(??)
pipes.quote(??)
pipes.re(??)
pipes.stepkinds(??)
pipes.tempfile(??)
pipes.warnings(??)
pkgutil.ImpImporter(??)
pkgutil.ImpLoader(??)
pkgutil.ModuleInfo(??)
pkgutil.ModuleType(??)
pkgutil.extend_path(??)
pkgutil.find_loader(??)
pkgutil.get_data(??)
pkgutil.get_importer(??)
pkgutil.get_loader(??)
pkgutil.importlib(??)
pkgutil.iter_importer_modules(??)
pkgutil.iter_importers(??)
pkgutil.iter_modules(??)
pkgutil.iter_zipimport_modules(??)
pkgutil.namedtuple(??)
pkgutil.os(??)
pkgutil.read_code(??)
pkgutil.resolve_name(??)
pkgutil.simplegeneric(??)
pkgutil.sys(??)
pkgutil.walk_packages(??)
pkgutil.warnings(??)
pkgutil.zipimport(??)
pkgutil.zipimporter(archivepath) -> zipimporter object
platform.architecture(??)
platform.collections(??)
platform.freedesktop_os_release(??)
platform.functools(??)
platform.itertools(??)
platform.java_ver(??)
platform.libc_ver(??)
platform.mac_ver(??)
platform.machine(??)
platform.node(??)
platform.os(??)
platform.platform(??)
platform.processor(??)
platform.python_branch(??)
platform.python_build(??)
platform.python_compiler(??)
platform.python_implementation(??)
platform.python_revision(??)
platform.python_version(??)
platform.python_version_tuple(??)
platform.re(??)
platform.release(??)
platform.sys(??)
platform.system(??)
platform.system_alias(??)
platform.uname(??)
platform.uname_result(??)
platform.version(??)
platform.win32_edition(??)
platform.win32_is_iot(??)
platform.win32_ver(??)
plistlib.BytesIO(??)
plistlib.FMT_BINARY(??)
plistlib.FMT_XML(??)
plistlib.InvalidFileException(??)
plistlib.PLISTHEADER
plistlib.ParserCreate(??)
plistlib.PlistFormat(??)
plistlib.UID(??)
plistlib.binascii(??)
plistlib.codecs(??)
plistlib.datetime(??)
plistlib.dump(??)
plistlib.dumps(??)
plistlib.enum(??)
plistlib.itertools(??)
plistlib.load(??)
plistlib.loads(??)
plistlib.os(??)
plistlib.re(??)
plistlib.struct(??)
poplib.CR
poplib.CRLF
poplib.HAVE_SSL(??)
poplib.LF
poplib.POP3(??)
poplib.POP3_PORT
poplib.POP3_SSL(??)
poplib.POP3_SSL_PORT
poplib.errno(??)
poplib.error_proto(??)
poplib.re(??)
poplib.socket(??)
poplib.ssl(??)
poplib.sys(??)
posix.CLD_CONTINUED
posix.CLD_DUMPED
posix.CLD_EXITED
posix.CLD_KILLED
posix.CLD_STOPPED
posix.CLD_TRAPPED
posix.DirEntry(??)
posix.EX_CANTCREAT
posix.EX_CONFIG
posix.EX_DATAERR
posix.EX_IOERR
posix.EX_NOHOST
posix.EX_NOINPUT
posix.EX_NOPERM
posix.EX_NOUSER
posix.EX_OK
posix.EX_OSERR
posix.EX_OSFILE
posix.EX_PROTOCOL
posix.EX_SOFTWARE
posix.EX_TEMPFAIL
posix.EX_UNAVAILABLE
posix.EX_USAGE
posix.F_LOCK
posix.F_OK
posix.F_TEST
posix.F_TLOCK
posix.F_ULOCK
posix.NGROUPS_MAX
posix.O_ACCMODE
posix.O_APPEND
posix.O_ASYNC
posix.O_CLOEXEC
posix.O_CREAT
posix.O_DIRECTORY
posix.O_DSYNC
posix.O_EVTONLY
posix.O_EXCL
posix.O_EXLOCK
posix.O_FSYNC
posix.O_NDELAY
posix.O_NOCTTY
posix.O_NOFOLLOW
posix.O_NOFOLLOW_ANY
posix.O_NONBLOCK
posix.O_RDONLY
posix.O_RDWR
posix.O_SHLOCK
posix.O_SYMLINK
posix.O_SYNC
posix.O_TRUNC
posix.O_WRONLY
posix.POSIX_SPAWN_CLOSE
posix.POSIX_SPAWN_DUP2
posix.POSIX_SPAWN_OPEN
posix.PRIO_PGRP
posix.PRIO_PROCESS
posix.PRIO_USER
posix.P_ALL
posix.P_PGID
posix.P_PID
posix.RTLD_GLOBAL
posix.RTLD_LAZY
posix.RTLD_LOCAL
posix.RTLD_NODELETE
posix.RTLD_NOLOAD
posix.RTLD_NOW
posix.R_OK
posix.SCHED_FIFO
posix.SCHED_OTHER
posix.SCHED_RR
posix.SEEK_DATA
posix.SEEK_HOLE
posix.ST_NOSUID
posix.ST_RDONLY
posix.TMP_MAX
posix.WCONTINUED
posix.WCOREDUMP(??)
posix.WEXITED
posix.WEXITSTATUS(??)
posix.WIFCONTINUED(??)
posix.WIFEXITED(??)
posix.WIFSIGNALED(??)
posix.WIFSTOPPED(??)
posix.WNOHANG
posix.WNOWAIT
posix.WSTOPPED
posix.WSTOPSIG(??)
posix.WTERMSIG(??)
posix.WUNTRACED
posix.W_OK
posix.X_OK
posix.abort(??)
posix.access(??)
posix.chdir(??)
posix.chflags(??)
posix.chmod(??)
posix.chown(??)
posix.chroot(??)
posix.close(??)
posix.closerange(??)
posix.confstr(??)
posix.confstr_names(??)
posix.cpu_count(??)
posix.ctermid(??)
posix.device_encoding(??)
posix.dup(??)
posix.dup2(??)
posix.environ(??)
posix.error(??)
posix.execv(??)
posix.execve(??)
posix.fchdir(??)
posix.fchmod(??)
posix.fchown(??)
posix.fork(??)
posix.forkpty(??)
posix.fpathconf(??)
posix.fspath(??)
posix.fstat(??)
posix.fstatvfs(??)
posix.fsync(??)
posix.ftruncate(??)
posix.get_blocking(??)
posix.get_inheritable(??)
posix.get_terminal_size(??)
posix.getcwd(??)
posix.getcwdb(??)
posix.getegid(??)
posix.geteuid(??)
posix.getgid(??)
posix.getgrouplist(??)
posix.getgroups(??)
posix.getloadavg(??)
posix.getlogin(??)
posix.getpgid(??)
posix.getpgrp(??)
posix.getpid(??)
posix.getppid(??)
posix.getpriority(??)
posix.getsid(??)
posix.getuid(??)
posix.initgroups(??)
posix.isatty(??)
posix.kill(??)
posix.killpg(??)
posix.lchflags(??)
posix.lchmod(??)
posix.lchown(??)
posix.link(??)
posix.listdir(??)
posix.lockf(??)
posix.login_tty(??)
posix.lseek(??)
posix.lstat(??)
posix.major(??)
posix.makedev(??)
posix.minor(??)
posix.mkdir(??)
posix.mkfifo(??)
posix.mknod(??)
posix.nice(??)
posix.open(??)
posix.openpty(??)
posix.pathconf(??)
posix.pathconf_names(??)
posix.pipe(??)
posix.posix_spawn(??)
posix.posix_spawnp(??)
posix.pread(??)
posix.preadv(??)
posix.putenv(??)
posix.pwrite(??)
posix.pwritev(??)
posix.read(??)
posix.readlink(??)
posix.readv(??)
posix.register_at_fork(??)
posix.remove(??)
posix.rename(??)
posix.replace(??)
posix.rmdir(??)
posix.scandir(??)
posix.sched_get_priority_max(??)
posix.sched_get_priority_min(??)
posix.sched_yield(??)
posix.sendfile(??)
posix.set_blocking(??)
posix.set_inheritable(??)
posix.setegid(??)
posix.seteuid(??)
posix.setgid(??)
posix.setgroups(??)
posix.setpgid(??)
posix.setpgrp(??)
posix.setpriority(??)
posix.setregid(??)
posix.setreuid(??)
posix.setsid(??)
posix.setuid(??)
posix.stat(??)
posix.stat_result(??)
posix.statvfs(??)
posix.statvfs_result(??)
posix.strerror(??)
posix.symlink(??)
posix.sync(??)
posix.sysconf(??)
posix.sysconf_names(??)
posix.system(??)
posix.tcgetpgrp(??)
posix.tcsetpgrp(??)
posix.terminal_size(??)
posix.times(??)
posix.times_result(??)
posix.truncate(??)
posix.ttyname(??)
posix.umask(??)
posix.uname(??)
posix.uname_result(??)
posix.unlink(??)
posix.unsetenv(??)
posix.urandom(??)
posix.utime(??)
posix.wait(??)
posix.wait3(??)
posix.wait4(??)
posix.waitpid(??)
posix.waitstatus_to_exitcode(??)
posix.write(??)
posix.writev(??)
pow(??)
pprint.PrettyPrinter(??)
pprint.isreadable(??)
pprint.isrecursive(??)
pprint.pformat(??)
pprint.pp(??)
pprint.pprint(??)
pprint.re(??)
pprint.saferepr(??)
print(??)
profile.Profile(??)
profile.main(??)
profile.marshal(??)
profile.run(??)
profile.runctx(??)
profile.sys(??)
profile.time(??)
property(??)
pstats.Dict(??)
pstats.FunctionProfile(ncalls: int, tottime: float, percall_tottime: float, cumtime: float, percall_cumtime: float, file_name: str, line_number: int)
pstats.SortKey(??)
pstats.Stats(??)
pstats.StatsProfile(??)
pstats.StrEnum(??)
pstats.TupleComp(??)
pstats.add_callers(??)
pstats.add_func_stats(??)
pstats.cmp_to_key(??)
pstats.count_calls(??)
pstats.dataclass(??)
pstats.f8(??)
pstats.func_get_function_name(??)
pstats.func_std_string(??)
pstats.func_strip_path(??)
pstats.marshal(??)
pstats.os(??)
pstats.re(??)
pstats.sys(??)
pstats.time(??)
pty.CHILD
pty.STDERR_FILENO
pty.STDIN_FILENO
pty.STDOUT_FILENO
pty.close(??)
pty.fork() -> (pid, master_fd)
pty.master_open() -> (master_fd, slave_name)
pty.openpty() -> (master_fd, slave_fd)
pty.os(??)
pty.select(??)
pty.setraw(??)
pty.slave_open(tty_name) -> slave_fd
pty.spawn(??)
pty.sys(??)
pty.tcgetattr(??)
pty.tcsetattr(??)
pty.tty(??)
pty.waitpid(??)
pwd.getpwall(??)
pwd.getpwnam(??)
pwd.getpwuid(??)
pwd.struct_passwd(??)
py_compile.PyCompileError(??)
py_compile.PycInvalidationMode(??)
py_compile.compile(??)
py_compile.enum(??)
py_compile.importlib(??)
py_compile.main(??)
py_compile.os(??)
py_compile.sys(??)
py_compile.traceback(??)
pyclbr.Class(??)
pyclbr.Function(??)
pyclbr.ast(??)
pyclbr.importlib(??)
pyclbr.readmodule(??)
pyclbr.readmodule_ex(??)
pyclbr.sys(??)
pydoc.Doc(??)
pydoc.ErrorDuringImport(??)
pydoc.HTMLDoc(??)
pydoc.HTMLRepr(??)
pydoc.Helper(??)
pydoc.ModuleScanner(??)
pydoc.Repr(??)
pydoc.TextDoc(??)
pydoc.TextRepr(??)
pydoc.allmethods(??)
pydoc.apropos(??)
pydoc.browse(??)
pydoc.builtins(??)
pydoc.classify_class_attrs(??)
pydoc.classname(??)
pydoc.cli(??)
pydoc.cram(??)
pydoc.deque([iterable[, maxlen]]) --> deque object
pydoc.describe(??)
pydoc.doc(??)
pydoc.format_exception_only(??)
pydoc.getdoc(??)
pydoc.getpager(??)
pydoc.help(??)
pydoc.html(??)
pydoc.importfile(??)
pydoc.importlib(??)
pydoc.inspect(??)
pydoc.io(??)
pydoc.isdata(??)
pydoc.ispackage(??)
pydoc.ispath(??)
pydoc.locate(??)
pydoc.os(??)
pydoc.pager(??)
pydoc.pathdirs(??)
pydoc.pipepager(??)
pydoc.pkgutil(??)
pydoc.plain(??)
pydoc.plainpager(??)
pydoc.plaintext(??)
pydoc.platform(??)
pydoc.re(??)
pydoc.render_doc(??)
pydoc.replace(??)
pydoc.resolve(??)
pydoc.safeimport(??)
pydoc.sort_attributes(??)
pydoc.source_synopsis(??)
pydoc.splitdoc(??)
pydoc.stripid(??)
pydoc.synopsis(??)
pydoc.sys(??)
pydoc.sysconfig(??)
pydoc.tempfilepager(??)
pydoc.text(??)
pydoc.time(??)
pydoc.tokenize(??)
pydoc.ttypager(??)
pydoc.types(??)
pydoc.urllib(??)
pydoc.visiblename(??)
pydoc.warnings(??)
pydoc.writedoc(??)
pydoc.writedocs(??)
pydoc_data.topics.topics(??)
queue.Empty(??)
queue.Full(??)
queue.LifoQueue(??)
queue.PriorityQueue(??)
queue.Queue(??)
queue.SimpleQueue(??)
queue.deque([iterable[, maxlen]]) --> deque object
queue.heappop(??)
queue.heappush(??)
queue.threading(??)
queue.time(??)
queue.types(??)
quit(??)
quopri.EMPTYSTRING
quopri.ESCAPE
quopri.HEX
quopri.MAXLINESIZE
quopri.a2b_qp(??)
quopri.b2a_qp(??)
quopri.decode(??)
quopri.decodestring(??)
quopri.encode(??)
quopri.encodestring(??)
quopri.ishex(??)
quopri.main(??)
quopri.needsquoting(??)
quopri.quote(??)
quopri.unhex(??)
random.BPF
random.LOG4
random.NV_MAGICCONST
random.RECIP_BPF
random.Random(??)
random.SG_MAGICCONST
random.SystemRandom(??)
random.TWOPI
random.betavariate(??)
random.choice(??)
random.choices(??)
random.expovariate(??)
random.gammavariate(??)
random.gauss(??)
random.getrandbits(k) -> x.  Generates an int with k random bits.
random.getstate(??)
random.lognormvariate(??)
random.normalvariate(??)
random.paretovariate(??)
random.randbytes(??)
random.randint(??)
random.random() -> x in the interval [0, 1).
random.randrange(??)
random.sample(??)
random.seed(??)
random.setstate(??)
random.shuffle(??)
random.triangular(??)
random.uniform(??)
random.vonmisesvariate(??)
random.weibullvariate(??)
range(stop) -> range object
re.A(??)
re.ASCII(??)
re.DEBUG(??)
re.DOTALL(??)
re.I(??)
re.IGNORECASE(??)
re.L(??)
re.LOCALE(??)
re.M(??)
re.MULTILINE(??)
re.Match(??)
re.NOFLAG(??)
re.Pattern(??)
re.RegexFlag(??)
re.S(??)
re.Scanner(??)
re.T(??)
re.TEMPLATE(??)
re.U(??)
re.UNICODE(??)
re.VERBOSE(??)
re.X(??)
re.compile(??)
re.copyreg(??)
re.enum(??)
re.error(??)
re.escape(??)
re.findall(??)
re.finditer(??)
re.fullmatch(??)
re.functools(??)
re.match(??)
re.purge(??)
re.search(??)
re.split(??)
re.sub(??)
re.subn(??)
re.template(??)
repr(??)
reprlib.Repr(??)
reprlib.aRepr(??)
reprlib.builtins(??)
reprlib.get_ident() -> integer
reprlib.islice(iterable, stop) --> islice object
reprlib.recursive_repr(??)
reprlib.repr(??)
reversed(??)
rlcompleter.Completer(??)
rlcompleter.atexit(??)
rlcompleter.builtins(??)
rlcompleter.get_class_members(??)
rlcompleter.inspect(??)
rlcompleter.keyword(??)
rlcompleter.re(??)
rlcompleter.readline(??)
round(??)
runpy.ModuleType(??)
runpy.importlib(??)
runpy.io(??)
runpy.os(??)
runpy.run_module(??)
runpy.run_path(??)
runpy.sys(??)
sched.Event(time, priority, sequence, action, argument, kwargs)
sched.count(??)
sched.heapq(??)
sched.namedtuple(??)
sched.scheduler(??)
sched.threading(??)
sched.time(??)
secrets.DEFAULT_ENTROPY
secrets.SystemRandom(??)
secrets.base64(??)
secrets.binascii(??)
secrets.choice(??)
secrets.compare_digest(??)
secrets.randbelow(??)
secrets.randbits(??)
secrets.token_bytes(??)
secrets.token_hex(??)
secrets.token_urlsafe(??)
selectors.ABCMeta(??)
selectors.BaseSelector(??)
selectors.DefaultSelector(??)
selectors.EVENT_READ
selectors.EVENT_WRITE
selectors.KqueueSelector(??)
selectors.Mapping(??)
selectors.PollSelector(??)
selectors.SelectSelector(??)
selectors.SelectorKey(fileobj, fd, events, data)
selectors.abstractmethod(??)
selectors.math(??)
selectors.namedtuple(??)
selectors.select(??)
selectors.sys(??)
set() -> new empty set object
setattr(??)
shelve.BsdDbShelf(??)
shelve.BytesIO(??)
shelve.DEFAULT_PROTOCOL
shelve.DbfilenameShelf(??)
shelve.Pickler(??)
shelve.Shelf(??)
shelve.Unpickler(??)
shelve.collections(??)
shelve.open(??)
shlex.StringIO(??)
shlex.deque([iterable[, maxlen]]) --> deque object
shlex.join(??)
shlex.os(??)
shlex.quote(??)
shlex.re(??)
shlex.shlex(??)
shlex.split(??)
shlex.sys(??)
shutil.COPY_BUFSIZE
shutil.Error(??)
shutil.ExecError(??)
shutil.ReadError(??)
shutil.RegistryError(??)
shutil.SameFileError(??)
shutil.SpecialFileError(??)
shutil.chown(??)
shutil.collections(??)
shutil.copy(??)
shutil.copy2(??)
shutil.copyfile(??)
shutil.copyfileobj(??)
shutil.copymode(??)
shutil.copystat(??)
shutil.copytree(??)
shutil.disk_usage(??)
shutil.errno(??)
shutil.fnmatch(??)
shutil.get_archive_formats(??)
shutil.get_terminal_size(??)
shutil.get_unpack_formats(??)
shutil.ignore_patterns(??)
shutil.make_archive(??)
shutil.move(??)
shutil.nt(??)
shutil.os(??)
shutil.posix(??)
shutil.register_archive_format(??)
shutil.register_unpack_format(??)
shutil.rmtree(??)
shutil.stat(??)
shutil.sys(??)
shutil.unpack_archive(??)
shutil.unregister_archive_format(??)
shutil.unregister_unpack_format(??)
shutil.which(??)
signal.Handlers(??)
signal.ITIMER_PROF
signal.ITIMER_REAL
signal.ITIMER_VIRTUAL
signal.ItimerError(??)
signal.NSIG
signal.SIGABRT(??)
signal.SIGALRM(??)
signal.SIGBUS(??)
signal.SIGCHLD(??)
signal.SIGCONT(??)
signal.SIGEMT(??)
signal.SIGFPE(??)
signal.SIGHUP(??)
signal.SIGILL(??)
signal.SIGINFO(??)
signal.SIGINT(??)
signal.SIGIO(??)
signal.SIGIOT(??)
signal.SIGKILL(??)
signal.SIGPIPE(??)
signal.SIGPROF(??)
signal.SIGQUIT(??)
signal.SIGSEGV(??)
signal.SIGSTOP(??)
signal.SIGSYS(??)
signal.SIGTERM(??)
signal.SIGTRAP(??)
signal.SIGTSTP(??)
signal.SIGTTIN(??)
signal.SIGTTOU(??)
signal.SIGURG(??)
signal.SIGUSR1(??)
signal.SIGUSR2(??)
signal.SIGVTALRM(??)
signal.SIGWINCH(??)
signal.SIGXCPU(??)
signal.SIGXFSZ(??)
signal.SIG_BLOCK(??)
signal.SIG_DFL(??)
signal.SIG_IGN(??)
signal.SIG_SETMASK(??)
signal.SIG_UNBLOCK(??)
signal.Sigmasks(??)
signal.Signals(??)
signal.alarm(??)
signal.default_int_handler(??)
signal.getitimer(??)
signal.getsignal(??)
signal.pause(??)
signal.pthread_kill(??)
signal.pthread_sigmask(??)
signal.raise_signal(??)
signal.set_wakeup_fd(fd, *, warn_on_full_buffer=True) -> fd
signal.setitimer(??)
signal.siginterrupt(??)
signal.signal(??)
signal.sigpending(??)
signal.sigwait(??)
signal.strsignal(??)
signal.valid_signals(??)
site.ENABLE_USER_SITE(??)
site.PREFIXES(??)
site.USER_BASE
site.USER_SITE
site.abs_paths(??)
site.addpackage(??)
site.addsitedir(??)
site.addsitepackages(??)
site.addusersitepackages(??)
site.builtins(??)
site.check_enableusersite(??)
site.enablerlcompleter(??)
site.execsitecustomize(??)
site.execusercustomize(??)
site.getsitepackages(??)
site.getuserbase(??)
site.getusersitepackages(??)
site.io(??)
site.main(??)
site.makepath(??)
site.os(??)
site.removeduppaths(??)
site.setcopyright(??)
site.sethelper(??)
site.setquit(??)
site.sys(??)
site.venv(??)
slice(stop)
smtpd.COMMASPACE
smtpd.DATA_SIZE_DEFAULT
smtpd.DEBUGSTREAM(??)
smtpd.DebuggingServer(??)
smtpd.Devnull(??)
smtpd.NEWLINE
smtpd.Options(??)
smtpd.PureProxy(??)
smtpd.SMTPChannel(??)
smtpd.SMTPServer(??)
smtpd.asynchat(??)
smtpd.asyncore(??)
smtpd.collections(??)
smtpd.errno(??)
smtpd.get_addr_spec(??)
smtpd.get_angle_addr(??)
smtpd.getopt(??)
smtpd.os(??)
smtpd.parseargs(??)
smtpd.program
smtpd.socket(??)
smtpd.sys(??)
smtpd.time(??)
smtpd.usage(??)
smtpd.warn(??)
smtplib.CRLF
smtplib.LMTP(??)
smtplib.LMTP_PORT
smtplib.OLDSTYLE_AUTH(??)
smtplib.SMTP(??)
smtplib.SMTPAuthenticationError(??)
smtplib.SMTPConnectError(??)
smtplib.SMTPDataError(??)
smtplib.SMTPException(??)
smtplib.SMTPHeloError(??)
smtplib.SMTPNotSupportedError(??)
smtplib.SMTPRecipientsRefused(??)
smtplib.SMTPResponseException(??)
smtplib.SMTPSenderRefused(??)
smtplib.SMTPServerDisconnected(??)
smtplib.SMTP_PORT
smtplib.SMTP_SSL(??)
smtplib.SMTP_SSL_PORT
smtplib.bCRLF
smtplib.base64(??)
smtplib.copy(??)
smtplib.datetime(??)
smtplib.email(??)
smtplib.encode_base64(??)
smtplib.hmac(??)
smtplib.io(??)
smtplib.quoteaddr(??)
smtplib.quotedata(??)
smtplib.re(??)
smtplib.socket(??)
smtplib.ssl(??)
smtplib.sys(??)
sndhdr.SndHeaders(filetype, framerate, nchannels, nframes, sampwidth)
sndhdr.get_long_be(??)
sndhdr.get_long_le(??)
sndhdr.get_short_be(??)
sndhdr.get_short_le(??)
sndhdr.namedtuple(??)
sndhdr.test(??)
sndhdr.test_8svx(??)
sndhdr.test_aifc(??)
sndhdr.test_au(??)
sndhdr.test_hcom(??)
sndhdr.test_sndr(??)
sndhdr.test_sndt(??)
sndhdr.test_voc(??)
sndhdr.test_wav(??)
sndhdr.testall(??)
sndhdr.tests(??)
sndhdr.warnings(??)
sndhdr.what(??)
sndhdr.whathdr(??)
socket.AF_APPLETALK(??)
socket.AF_DECnet
socket.AF_INET(??)
socket.AF_INET6(??)
socket.AF_IPX(??)
socket.AF_LINK(??)
socket.AF_ROUTE(??)
socket.AF_SNA(??)
socket.AF_SYSTEM(??)
socket.AF_UNIX(??)
socket.AF_UNSPEC(??)
socket.AI_ADDRCONFIG(??)
socket.AI_ALL(??)
socket.AI_CANONNAME(??)
socket.AI_DEFAULT(??)
socket.AI_MASK(??)
socket.AI_NUMERICHOST(??)
socket.AI_NUMERICSERV(??)
socket.AI_PASSIVE(??)
socket.AI_V4MAPPED(??)
socket.AI_V4MAPPED_CFG(??)
socket.AddressFamily(??)
socket.AddressInfo(??)
socket.CAPI(??)
socket.CMSG_LEN(length) -> control message length
socket.CMSG_SPACE(length) -> buffer size
socket.EAGAIN
socket.EAI_ADDRFAMILY
socket.EAI_AGAIN
socket.EAI_BADFLAGS
socket.EAI_BADHINTS
socket.EAI_FAIL
socket.EAI_FAMILY
socket.EAI_MAX
socket.EAI_MEMORY
socket.EAI_NODATA
socket.EAI_NONAME
socket.EAI_OVERFLOW
socket.EAI_PROTOCOL
socket.EAI_SERVICE
socket.EAI_SOCKTYPE
socket.EAI_SYSTEM
socket.EBADF
socket.EWOULDBLOCK
socket.INADDR_ALLHOSTS_GROUP
socket.INADDR_ANY
socket.INADDR_BROADCAST
socket.INADDR_LOOPBACK
socket.INADDR_MAX_LOCAL_GROUP
socket.INADDR_NONE
socket.INADDR_UNSPEC_GROUP
socket.IPPORT_RESERVED
socket.IPPORT_USERRESERVED
socket.IPPROTO_AH
socket.IPPROTO_DSTOPTS
socket.IPPROTO_EGP
socket.IPPROTO_EON
socket.IPPROTO_ESP
socket.IPPROTO_FRAGMENT
socket.IPPROTO_GGP
socket.IPPROTO_GRE
socket.IPPROTO_HELLO
socket.IPPROTO_HOPOPTS
socket.IPPROTO_ICMP
socket.IPPROTO_ICMPV6
socket.IPPROTO_IDP
socket.IPPROTO_IGMP
socket.IPPROTO_IP
socket.IPPROTO_IPCOMP
socket.IPPROTO_IPIP
socket.IPPROTO_IPV4
socket.IPPROTO_IPV6
socket.IPPROTO_MAX
socket.IPPROTO_ND
socket.IPPROTO_NONE
socket.IPPROTO_PIM
socket.IPPROTO_PUP
socket.IPPROTO_RAW
socket.IPPROTO_ROUTING
socket.IPPROTO_RSVP
socket.IPPROTO_SCTP
socket.IPPROTO_TCP
socket.IPPROTO_TP
socket.IPPROTO_UDP
socket.IPPROTO_XTP
socket.IPV6_CHECKSUM
socket.IPV6_DONTFRAG
socket.IPV6_DSTOPTS
socket.IPV6_HOPLIMIT
socket.IPV6_HOPOPTS
socket.IPV6_JOIN_GROUP
socket.IPV6_LEAVE_GROUP
socket.IPV6_MULTICAST_HOPS
socket.IPV6_MULTICAST_IF
socket.IPV6_MULTICAST_LOOP
socket.IPV6_NEXTHOP
socket.IPV6_PATHMTU
socket.IPV6_PKTINFO
socket.IPV6_RECVDSTOPTS
socket.IPV6_RECVHOPLIMIT
socket.IPV6_RECVHOPOPTS
socket.IPV6_RECVPATHMTU
socket.IPV6_RECVPKTINFO
socket.IPV6_RECVRTHDR
socket.IPV6_RECVTCLASS
socket.IPV6_RTHDR
socket.IPV6_RTHDRDSTOPTS
socket.IPV6_RTHDR_TYPE_0
socket.IPV6_TCLASS
socket.IPV6_UNICAST_HOPS
socket.IPV6_USE_MIN_MTU
socket.IPV6_V6ONLY
socket.IP_ADD_MEMBERSHIP
socket.IP_DEFAULT_MULTICAST_LOOP
socket.IP_DEFAULT_MULTICAST_TTL
socket.IP_DROP_MEMBERSHIP
socket.IP_HDRINCL
socket.IP_MAX_MEMBERSHIPS
socket.IP_MULTICAST_IF
socket.IP_MULTICAST_LOOP
socket.IP_MULTICAST_TTL
socket.IP_OPTIONS
socket.IP_RECVDSTADDR
socket.IP_RECVOPTS
socket.IP_RECVRETOPTS
socket.IP_RECVTOS
socket.IP_RETOPTS
socket.IP_TOS
socket.IP_TTL
socket.IntEnum(??)
socket.IntFlag(??)
socket.LOCAL_PEERCRED
socket.MSG_CTRUNC(??)
socket.MSG_DONTROUTE(??)
socket.MSG_DONTWAIT(??)
socket.MSG_EOF(??)
socket.MSG_EOR(??)
socket.MSG_NOSIGNAL(??)
socket.MSG_OOB(??)
socket.MSG_PEEK(??)
socket.MSG_TRUNC(??)
socket.MSG_WAITALL(??)
socket.MsgFlag(??)
socket.NI_DGRAM
socket.NI_MAXHOST
socket.NI_MAXSERV
socket.NI_NAMEREQD
socket.NI_NOFQDN
socket.NI_NUMERICHOST
socket.NI_NUMERICSERV
socket.PF_SYSTEM
socket.SCM_CREDS
socket.SCM_RIGHTS
socket.SHUT_RD
socket.SHUT_RDWR
socket.SHUT_WR
socket.SOCK_DGRAM(??)
socket.SOCK_RAW(??)
socket.SOCK_RDM(??)
socket.SOCK_SEQPACKET(??)
socket.SOCK_STREAM(??)
socket.SOL_IP
socket.SOL_SOCKET
socket.SOL_TCP
socket.SOL_UDP
socket.SOMAXCONN
socket.SO_ACCEPTCONN
socket.SO_BROADCAST
socket.SO_DEBUG
socket.SO_DONTROUTE
socket.SO_ERROR
socket.SO_KEEPALIVE
socket.SO_LINGER
socket.SO_OOBINLINE
socket.SO_RCVBUF
socket.SO_RCVLOWAT
socket.SO_RCVTIMEO
socket.SO_REUSEADDR
socket.SO_REUSEPORT
socket.SO_SNDBUF
socket.SO_SNDLOWAT
socket.SO_SNDTIMEO
socket.SO_TYPE
socket.SO_USELOOPBACK
socket.SYSPROTO_CONTROL
socket.SocketIO(??)
socket.SocketKind(??)
socket.SocketType(??)
socket.TCP_CONNECTION_INFO
socket.TCP_FASTOPEN
socket.TCP_KEEPALIVE
socket.TCP_KEEPCNT
socket.TCP_KEEPINTVL
socket.TCP_MAXSEG
socket.TCP_NODELAY
socket.TCP_NOTSENT_LOWAT
socket.array(??)
socket.close(integer) -> None
socket.create_connection(??)
socket.create_server(??)
socket.dup(integer) -> integer
socket.errno(??)
socket.error(??)
socket.fromfd(fd, family, type[, proto]) -> socket object
socket.gaierror(??)
socket.getaddrinfo(??)
socket.getdefaulttimeout() -> timeout
socket.getfqdn(??)
socket.gethostbyaddr(host) -> (name, aliaslist, addresslist)
socket.gethostbyname(host) -> address
socket.gethostbyname_ex(host) -> (name, aliaslist, addresslist)
socket.gethostname() -> string
socket.getnameinfo(sockaddr, flags) --> (host, port)
socket.getprotobyname(name) -> integer
socket.getservbyname(servicename[, protocolname]) -> integer
socket.getservbyport(port[, protocolname]) -> string
socket.has_dualstack_ipv6(??)
socket.has_ipv6(??)
socket.herror(??)
socket.htonl(integer) -> integer
socket.htons(integer) -> integer
socket.if_indextoname(if_index)
socket.if_nameindex()
socket.if_nametoindex(if_name)
socket.inet_aton(string) -> bytes giving packed 32-bit IP representation
socket.inet_ntoa(packed_ip) -> ip_address_string
socket.inet_ntop(af, packed_ip) -> string formatted IP address
socket.inet_pton(af, ip) -> packed IP address string
socket.io(??)
socket.ntohl(integer) -> integer
socket.ntohs(integer) -> integer
socket.os(??)
socket.recv_fds(sock, bufsize, maxfds[, flags]) -> (data, list of file
socket.selectors(??)
socket.send_fds(sock, buffers, fds[, flags[, address]]) -> integer
socket.setdefaulttimeout(timeout)
socket.sethostname(name)
socket.socket(??)
socket.socketpair([family[, type[, proto]]]) -> (socket object, socket object)
socket.sys(??)
socket.timeout(??)
socketserver.BaseRequestHandler(??)
socketserver.BaseServer(??)
socketserver.BufferedIOBase(??)
socketserver.DatagramRequestHandler(??)
socketserver.ForkingMixIn(??)
socketserver.ForkingTCPServer(??)
socketserver.ForkingUDPServer(??)
socketserver.StreamRequestHandler(??)
socketserver.TCPServer(??)
socketserver.ThreadingMixIn(??)
socketserver.ThreadingTCPServer(??)
socketserver.ThreadingUDPServer(??)
socketserver.ThreadingUnixDatagramServer(??)
socketserver.ThreadingUnixStreamServer(??)
socketserver.UDPServer(??)
socketserver.UnixDatagramServer(??)
socketserver.UnixStreamServer(??)
socketserver.os(??)
socketserver.selectors(??)
socketserver.socket(??)
socketserver.sys(??)
socketserver.threading(??)
socketserver.time(??)
sorted(??)
sqlite3.Binary(??)
sqlite3.Blob(??)
sqlite3.Connection(??)
sqlite3.Cursor(??)
sqlite3.DataError(??)
sqlite3.DatabaseError(??)
sqlite3.Date(??)
sqlite3.DateFromTicks(??)
sqlite3.Error(??)
sqlite3.IntegrityError(??)
sqlite3.InterfaceError(??)
sqlite3.InternalError(??)
sqlite3.NotSupportedError(??)
sqlite3.OperationalError(??)
sqlite3.PARSE_COLNAMES
sqlite3.PARSE_DECLTYPES
sqlite3.PrepareProtocol(??)
sqlite3.ProgrammingError(??)
sqlite3.Row(??)
sqlite3.SQLITE_ABORT
sqlite3.SQLITE_ABORT_ROLLBACK
sqlite3.SQLITE_ALTER_TABLE
sqlite3.SQLITE_ANALYZE
sqlite3.SQLITE_ATTACH
sqlite3.SQLITE_AUTH
sqlite3.SQLITE_AUTH_USER
sqlite3.SQLITE_BUSY
sqlite3.SQLITE_BUSY_RECOVERY
sqlite3.SQLITE_BUSY_SNAPSHOT
sqlite3.SQLITE_BUSY_TIMEOUT
sqlite3.SQLITE_CANTOPEN
sqlite3.SQLITE_CANTOPEN_CONVPATH
sqlite3.SQLITE_CANTOPEN_DIRTYWAL
sqlite3.SQLITE_CANTOPEN_FULLPATH
sqlite3.SQLITE_CANTOPEN_ISDIR
sqlite3.SQLITE_CANTOPEN_NOTEMPDIR
sqlite3.SQLITE_CANTOPEN_SYMLINK
sqlite3.SQLITE_CONSTRAINT
sqlite3.SQLITE_CONSTRAINT_CHECK
sqlite3.SQLITE_CONSTRAINT_COMMITHOOK
sqlite3.SQLITE_CONSTRAINT_FOREIGNKEY
sqlite3.SQLITE_CONSTRAINT_FUNCTION
sqlite3.SQLITE_CONSTRAINT_NOTNULL
sqlite3.SQLITE_CONSTRAINT_PINNED
sqlite3.SQLITE_CONSTRAINT_PRIMARYKEY
sqlite3.SQLITE_CONSTRAINT_ROWID
sqlite3.SQLITE_CONSTRAINT_TRIGGER
sqlite3.SQLITE_CONSTRAINT_UNIQUE
sqlite3.SQLITE_CONSTRAINT_VTAB
sqlite3.SQLITE_CORRUPT
sqlite3.SQLITE_CORRUPT_INDEX
sqlite3.SQLITE_CORRUPT_SEQUENCE
sqlite3.SQLITE_CORRUPT_VTAB
sqlite3.SQLITE_CREATE_INDEX
sqlite3.SQLITE_CREATE_TABLE
sqlite3.SQLITE_CREATE_TEMP_INDEX
sqlite3.SQLITE_CREATE_TEMP_TABLE
sqlite3.SQLITE_CREATE_TEMP_TRIGGER
sqlite3.SQLITE_CREATE_TEMP_VIEW
sqlite3.SQLITE_CREATE_TRIGGER
sqlite3.SQLITE_CREATE_VIEW
sqlite3.SQLITE_CREATE_VTABLE
sqlite3.SQLITE_DELETE
sqlite3.SQLITE_DENY
sqlite3.SQLITE_DETACH
sqlite3.SQLITE_DONE
sqlite3.SQLITE_DROP_INDEX
sqlite3.SQLITE_DROP_TABLE
sqlite3.SQLITE_DROP_TEMP_INDEX
sqlite3.SQLITE_DROP_TEMP_TABLE
sqlite3.SQLITE_DROP_TEMP_TRIGGER
sqlite3.SQLITE_DROP_TEMP_VIEW
sqlite3.SQLITE_DROP_TRIGGER
sqlite3.SQLITE_DROP_VIEW
sqlite3.SQLITE_DROP_VTABLE
sqlite3.SQLITE_EMPTY
sqlite3.SQLITE_ERROR
sqlite3.SQLITE_ERROR_MISSING_COLLSEQ
sqlite3.SQLITE_ERROR_RETRY
sqlite3.SQLITE_ERROR_SNAPSHOT
sqlite3.SQLITE_FORMAT
sqlite3.SQLITE_FULL
sqlite3.SQLITE_FUNCTION
sqlite3.SQLITE_IGNORE
sqlite3.SQLITE_INSERT
sqlite3.SQLITE_INTERNAL
sqlite3.SQLITE_INTERRUPT
sqlite3.SQLITE_IOERR
sqlite3.SQLITE_IOERR_ACCESS
sqlite3.SQLITE_IOERR_AUTH
sqlite3.SQLITE_IOERR_BEGIN_ATOMIC
sqlite3.SQLITE_IOERR_BLOCKED
sqlite3.SQLITE_IOERR_CHECKRESERVEDLOCK
sqlite3.SQLITE_IOERR_CLOSE
sqlite3.SQLITE_IOERR_COMMIT_ATOMIC
sqlite3.SQLITE_IOERR_CONVPATH
sqlite3.SQLITE_IOERR_CORRUPTFS
sqlite3.SQLITE_IOERR_DATA
sqlite3.SQLITE_IOERR_DELETE
sqlite3.SQLITE_IOERR_DELETE_NOENT
sqlite3.SQLITE_IOERR_DIR_CLOSE
sqlite3.SQLITE_IOERR_DIR_FSYNC
sqlite3.SQLITE_IOERR_FSTAT
sqlite3.SQLITE_IOERR_FSYNC
sqlite3.SQLITE_IOERR_GETTEMPPATH
sqlite3.SQLITE_IOERR_LOCK
sqlite3.SQLITE_IOERR_MMAP
sqlite3.SQLITE_IOERR_NOMEM
sqlite3.SQLITE_IOERR_RDLOCK
sqlite3.SQLITE_IOERR_READ
sqlite3.SQLITE_IOERR_ROLLBACK_ATOMIC
sqlite3.SQLITE_IOERR_SEEK
sqlite3.SQLITE_IOERR_SHMLOCK
sqlite3.SQLITE_IOERR_SHMMAP
sqlite3.SQLITE_IOERR_SHMOPEN
sqlite3.SQLITE_IOERR_SHMSIZE
sqlite3.SQLITE_IOERR_SHORT_READ
sqlite3.SQLITE_IOERR_TRUNCATE
sqlite3.SQLITE_IOERR_UNLOCK
sqlite3.SQLITE_IOERR_VNODE
sqlite3.SQLITE_IOERR_WRITE
sqlite3.SQLITE_LIMIT_ATTACHED
sqlite3.SQLITE_LIMIT_COLUMN
sqlite3.SQLITE_LIMIT_COMPOUND_SELECT
sqlite3.SQLITE_LIMIT_EXPR_DEPTH
sqlite3.SQLITE_LIMIT_FUNCTION_ARG
sqlite3.SQLITE_LIMIT_LENGTH
sqlite3.SQLITE_LIMIT_LIKE_PATTERN_LENGTH
sqlite3.SQLITE_LIMIT_SQL_LENGTH
sqlite3.SQLITE_LIMIT_TRIGGER_DEPTH
sqlite3.SQLITE_LIMIT_VARIABLE_NUMBER
sqlite3.SQLITE_LIMIT_VDBE_OP
sqlite3.SQLITE_LIMIT_WORKER_THREADS
sqlite3.SQLITE_LOCKED
sqlite3.SQLITE_LOCKED_SHAREDCACHE
sqlite3.SQLITE_LOCKED_VTAB
sqlite3.SQLITE_MISMATCH
sqlite3.SQLITE_MISUSE
sqlite3.SQLITE_NOLFS
sqlite3.SQLITE_NOMEM
sqlite3.SQLITE_NOTADB
sqlite3.SQLITE_NOTFOUND
sqlite3.SQLITE_NOTICE
sqlite3.SQLITE_NOTICE_RECOVER_ROLLBACK
sqlite3.SQLITE_NOTICE_RECOVER_WAL
sqlite3.SQLITE_OK
sqlite3.SQLITE_OK_LOAD_PERMANENTLY
sqlite3.SQLITE_OK_SYMLINK
sqlite3.SQLITE_PERM
sqlite3.SQLITE_PRAGMA
sqlite3.SQLITE_PROTOCOL
sqlite3.SQLITE_RANGE
sqlite3.SQLITE_READ
sqlite3.SQLITE_READONLY
sqlite3.SQLITE_READONLY_CANTINIT
sqlite3.SQLITE_READONLY_CANTLOCK
sqlite3.SQLITE_READONLY_DBMOVED
sqlite3.SQLITE_READONLY_DIRECTORY
sqlite3.SQLITE_READONLY_RECOVERY
sqlite3.SQLITE_READONLY_ROLLBACK
sqlite3.SQLITE_RECURSIVE
sqlite3.SQLITE_REINDEX
sqlite3.SQLITE_ROW
sqlite3.SQLITE_SAVEPOINT
sqlite3.SQLITE_SCHEMA
sqlite3.SQLITE_SELECT
sqlite3.SQLITE_TOOBIG
sqlite3.SQLITE_TRANSACTION
sqlite3.SQLITE_UPDATE
sqlite3.SQLITE_WARNING
sqlite3.SQLITE_WARNING_AUTOINDEX
sqlite3.Time(??)
sqlite3.TimeFromTicks(??)
sqlite3.Timestamp(??)
sqlite3.TimestampFromTicks(??)
sqlite3.Warning(??)
sqlite3.adapt(??)
sqlite3.adapters(??)
sqlite3.apilevel
sqlite3.collections(??)
sqlite3.complete_statement(??)
sqlite3.connect(??)
sqlite3.converters(??)
sqlite3.datetime(??)
sqlite3.dbapi2(??)
sqlite3.dbapi2.Binary(??)
sqlite3.dbapi2.Blob(??)
sqlite3.dbapi2.Connection(??)
sqlite3.dbapi2.Cursor(??)
sqlite3.dbapi2.DataError(??)
sqlite3.dbapi2.DatabaseError(??)
sqlite3.dbapi2.Date(??)
sqlite3.dbapi2.DateFromTicks(??)
sqlite3.dbapi2.Error(??)
sqlite3.dbapi2.IntegrityError(??)
sqlite3.dbapi2.InterfaceError(??)
sqlite3.dbapi2.InternalError(??)
sqlite3.dbapi2.NotSupportedError(??)
sqlite3.dbapi2.OperationalError(??)
sqlite3.dbapi2.PARSE_COLNAMES
sqlite3.dbapi2.PARSE_DECLTYPES
sqlite3.dbapi2.PrepareProtocol(??)
sqlite3.dbapi2.ProgrammingError(??)
sqlite3.dbapi2.Row(??)
sqlite3.dbapi2.SQLITE_ABORT
sqlite3.dbapi2.SQLITE_ABORT_ROLLBACK
sqlite3.dbapi2.SQLITE_ALTER_TABLE
sqlite3.dbapi2.SQLITE_ANALYZE
sqlite3.dbapi2.SQLITE_ATTACH
sqlite3.dbapi2.SQLITE_AUTH
sqlite3.dbapi2.SQLITE_AUTH_USER
sqlite3.dbapi2.SQLITE_BUSY
sqlite3.dbapi2.SQLITE_BUSY_RECOVERY
sqlite3.dbapi2.SQLITE_BUSY_SNAPSHOT
sqlite3.dbapi2.SQLITE_BUSY_TIMEOUT
sqlite3.dbapi2.SQLITE_CANTOPEN
sqlite3.dbapi2.SQLITE_CANTOPEN_CONVPATH
sqlite3.dbapi2.SQLITE_CANTOPEN_DIRTYWAL
sqlite3.dbapi2.SQLITE_CANTOPEN_FULLPATH
sqlite3.dbapi2.SQLITE_CANTOPEN_ISDIR
sqlite3.dbapi2.SQLITE_CANTOPEN_NOTEMPDIR
sqlite3.dbapi2.SQLITE_CANTOPEN_SYMLINK
sqlite3.dbapi2.SQLITE_CONSTRAINT
sqlite3.dbapi2.SQLITE_CONSTRAINT_CHECK
sqlite3.dbapi2.SQLITE_CONSTRAINT_COMMITHOOK
sqlite3.dbapi2.SQLITE_CONSTRAINT_FOREIGNKEY
sqlite3.dbapi2.SQLITE_CONSTRAINT_FUNCTION
sqlite3.dbapi2.SQLITE_CONSTRAINT_NOTNULL
sqlite3.dbapi2.SQLITE_CONSTRAINT_PINNED
sqlite3.dbapi2.SQLITE_CONSTRAINT_PRIMARYKEY
sqlite3.dbapi2.SQLITE_CONSTRAINT_ROWID
sqlite3.dbapi2.SQLITE_CONSTRAINT_TRIGGER
sqlite3.dbapi2.SQLITE_CONSTRAINT_UNIQUE
sqlite3.dbapi2.SQLITE_CONSTRAINT_VTAB
sqlite3.dbapi2.SQLITE_CORRUPT
sqlite3.dbapi2.SQLITE_CORRUPT_INDEX
sqlite3.dbapi2.SQLITE_CORRUPT_SEQUENCE
sqlite3.dbapi2.SQLITE_CORRUPT_VTAB
sqlite3.dbapi2.SQLITE_CREATE_INDEX
sqlite3.dbapi2.SQLITE_CREATE_TABLE
sqlite3.dbapi2.SQLITE_CREATE_TEMP_INDEX
sqlite3.dbapi2.SQLITE_CREATE_TEMP_TABLE
sqlite3.dbapi2.SQLITE_CREATE_TEMP_TRIGGER
sqlite3.dbapi2.SQLITE_CREATE_TEMP_VIEW
sqlite3.dbapi2.SQLITE_CREATE_TRIGGER
sqlite3.dbapi2.SQLITE_CREATE_VIEW
sqlite3.dbapi2.SQLITE_CREATE_VTABLE
sqlite3.dbapi2.SQLITE_DELETE
sqlite3.dbapi2.SQLITE_DENY
sqlite3.dbapi2.SQLITE_DETACH
sqlite3.dbapi2.SQLITE_DONE
sqlite3.dbapi2.SQLITE_DROP_INDEX
sqlite3.dbapi2.SQLITE_DROP_TABLE
sqlite3.dbapi2.SQLITE_DROP_TEMP_INDEX
sqlite3.dbapi2.SQLITE_DROP_TEMP_TABLE
sqlite3.dbapi2.SQLITE_DROP_TEMP_TRIGGER
sqlite3.dbapi2.SQLITE_DROP_TEMP_VIEW
sqlite3.dbapi2.SQLITE_DROP_TRIGGER
sqlite3.dbapi2.SQLITE_DROP_VIEW
sqlite3.dbapi2.SQLITE_DROP_VTABLE
sqlite3.dbapi2.SQLITE_EMPTY
sqlite3.dbapi2.SQLITE_ERROR
sqlite3.dbapi2.SQLITE_ERROR_MISSING_COLLSEQ
sqlite3.dbapi2.SQLITE_ERROR_RETRY
sqlite3.dbapi2.SQLITE_ERROR_SNAPSHOT
sqlite3.dbapi2.SQLITE_FORMAT
sqlite3.dbapi2.SQLITE_FULL
sqlite3.dbapi2.SQLITE_FUNCTION
sqlite3.dbapi2.SQLITE_IGNORE
sqlite3.dbapi2.SQLITE_INSERT
sqlite3.dbapi2.SQLITE_INTERNAL
sqlite3.dbapi2.SQLITE_INTERRUPT
sqlite3.dbapi2.SQLITE_IOERR
sqlite3.dbapi2.SQLITE_IOERR_ACCESS
sqlite3.dbapi2.SQLITE_IOERR_AUTH
sqlite3.dbapi2.SQLITE_IOERR_BEGIN_ATOMIC
sqlite3.dbapi2.SQLITE_IOERR_BLOCKED
sqlite3.dbapi2.SQLITE_IOERR_CHECKRESERVEDLOCK
sqlite3.dbapi2.SQLITE_IOERR_CLOSE
sqlite3.dbapi2.SQLITE_IOERR_COMMIT_ATOMIC
sqlite3.dbapi2.SQLITE_IOERR_CONVPATH
sqlite3.dbapi2.SQLITE_IOERR_CORRUPTFS
sqlite3.dbapi2.SQLITE_IOERR_DATA
sqlite3.dbapi2.SQLITE_IOERR_DELETE
sqlite3.dbapi2.SQLITE_IOERR_DELETE_NOENT
sqlite3.dbapi2.SQLITE_IOERR_DIR_CLOSE
sqlite3.dbapi2.SQLITE_IOERR_DIR_FSYNC
sqlite3.dbapi2.SQLITE_IOERR_FSTAT
sqlite3.dbapi2.SQLITE_IOERR_FSYNC
sqlite3.dbapi2.SQLITE_IOERR_GETTEMPPATH
sqlite3.dbapi2.SQLITE_IOERR_LOCK
sqlite3.dbapi2.SQLITE_IOERR_MMAP
sqlite3.dbapi2.SQLITE_IOERR_NOMEM
sqlite3.dbapi2.SQLITE_IOERR_RDLOCK
sqlite3.dbapi2.SQLITE_IOERR_READ
sqlite3.dbapi2.SQLITE_IOERR_ROLLBACK_ATOMIC
sqlite3.dbapi2.SQLITE_IOERR_SEEK
sqlite3.dbapi2.SQLITE_IOERR_SHMLOCK
sqlite3.dbapi2.SQLITE_IOERR_SHMMAP
sqlite3.dbapi2.SQLITE_IOERR_SHMOPEN
sqlite3.dbapi2.SQLITE_IOERR_SHMSIZE
sqlite3.dbapi2.SQLITE_IOERR_SHORT_READ
sqlite3.dbapi2.SQLITE_IOERR_TRUNCATE
sqlite3.dbapi2.SQLITE_IOERR_UNLOCK
sqlite3.dbapi2.SQLITE_IOERR_VNODE
sqlite3.dbapi2.SQLITE_IOERR_WRITE
sqlite3.dbapi2.SQLITE_LIMIT_ATTACHED
sqlite3.dbapi2.SQLITE_LIMIT_COLUMN
sqlite3.dbapi2.SQLITE_LIMIT_COMPOUND_SELECT
sqlite3.dbapi2.SQLITE_LIMIT_EXPR_DEPTH
sqlite3.dbapi2.SQLITE_LIMIT_FUNCTION_ARG
sqlite3.dbapi2.SQLITE_LIMIT_LENGTH
sqlite3.dbapi2.SQLITE_LIMIT_LIKE_PATTERN_LENGTH
sqlite3.dbapi2.SQLITE_LIMIT_SQL_LENGTH
sqlite3.dbapi2.SQLITE_LIMIT_TRIGGER_DEPTH
sqlite3.dbapi2.SQLITE_LIMIT_VARIABLE_NUMBER
sqlite3.dbapi2.SQLITE_LIMIT_VDBE_OP
sqlite3.dbapi2.SQLITE_LIMIT_WORKER_THREADS
sqlite3.dbapi2.SQLITE_LOCKED
sqlite3.dbapi2.SQLITE_LOCKED_SHAREDCACHE
sqlite3.dbapi2.SQLITE_LOCKED_VTAB
sqlite3.dbapi2.SQLITE_MISMATCH
sqlite3.dbapi2.SQLITE_MISUSE
sqlite3.dbapi2.SQLITE_NOLFS
sqlite3.dbapi2.SQLITE_NOMEM
sqlite3.dbapi2.SQLITE_NOTADB
sqlite3.dbapi2.SQLITE_NOTFOUND
sqlite3.dbapi2.SQLITE_NOTICE
sqlite3.dbapi2.SQLITE_NOTICE_RECOVER_ROLLBACK
sqlite3.dbapi2.SQLITE_NOTICE_RECOVER_WAL
sqlite3.dbapi2.SQLITE_OK
sqlite3.dbapi2.SQLITE_OK_LOAD_PERMANENTLY
sqlite3.dbapi2.SQLITE_OK_SYMLINK
sqlite3.dbapi2.SQLITE_PERM
sqlite3.dbapi2.SQLITE_PRAGMA
sqlite3.dbapi2.SQLITE_PROTOCOL
sqlite3.dbapi2.SQLITE_RANGE
sqlite3.dbapi2.SQLITE_READ
sqlite3.dbapi2.SQLITE_READONLY
sqlite3.dbapi2.SQLITE_READONLY_CANTINIT
sqlite3.dbapi2.SQLITE_READONLY_CANTLOCK
sqlite3.dbapi2.SQLITE_READONLY_DBMOVED
sqlite3.dbapi2.SQLITE_READONLY_DIRECTORY
sqlite3.dbapi2.SQLITE_READONLY_RECOVERY
sqlite3.dbapi2.SQLITE_READONLY_ROLLBACK
sqlite3.dbapi2.SQLITE_RECURSIVE
sqlite3.dbapi2.SQLITE_REINDEX
sqlite3.dbapi2.SQLITE_ROW
sqlite3.dbapi2.SQLITE_SAVEPOINT
sqlite3.dbapi2.SQLITE_SCHEMA
sqlite3.dbapi2.SQLITE_SELECT
sqlite3.dbapi2.SQLITE_TOOBIG
sqlite3.dbapi2.SQLITE_TRANSACTION
sqlite3.dbapi2.SQLITE_UPDATE
sqlite3.dbapi2.SQLITE_WARNING
sqlite3.dbapi2.SQLITE_WARNING_AUTOINDEX
sqlite3.dbapi2.Time(??)
sqlite3.dbapi2.TimeFromTicks(??)
sqlite3.dbapi2.Timestamp(??)
sqlite3.dbapi2.TimestampFromTicks(??)
sqlite3.dbapi2.Warning(??)
sqlite3.dbapi2.adapt(??)
sqlite3.dbapi2.adapters(??)
sqlite3.dbapi2.apilevel
sqlite3.dbapi2.collections(??)
sqlite3.dbapi2.complete_statement(??)
sqlite3.dbapi2.connect(??)
sqlite3.dbapi2.converters(??)
sqlite3.dbapi2.datetime(??)
sqlite3.dbapi2.enable_callback_tracebacks(??)
sqlite3.dbapi2.enable_shared_cache(??)
sqlite3.dbapi2.paramstyle
sqlite3.dbapi2.register_adapter(??)
sqlite3.dbapi2.register_converter(??)
sqlite3.dbapi2.sqlite_version
sqlite3.dbapi2.sqlite_version_info(??)
sqlite3.dbapi2.threadsafety
sqlite3.dbapi2.time(??)
sqlite3.dbapi2.version
sqlite3.dbapi2.version_info(??)
sqlite3.enable_callback_tracebacks(??)
sqlite3.enable_shared_cache(??)
sqlite3.paramstyle
sqlite3.register_adapter(??)
sqlite3.register_converter(??)
sqlite3.sqlite_version
sqlite3.sqlite_version_info(??)
sqlite3.threadsafety
sqlite3.time(??)
sqlite3.version
sqlite3.version_info(??)
ssl.ALERT_DESCRIPTION_ACCESS_DENIED(??)
ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE(??)
ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE_HASH_VALUE(??)
ssl.ALERT_DESCRIPTION_BAD_CERTIFICATE_STATUS_RESPONSE(??)
ssl.ALERT_DESCRIPTION_BAD_RECORD_MAC(??)
ssl.ALERT_DESCRIPTION_CERTIFICATE_EXPIRED(??)
ssl.ALERT_DESCRIPTION_CERTIFICATE_REVOKED(??)
ssl.ALERT_DESCRIPTION_CERTIFICATE_UNKNOWN(??)
ssl.ALERT_DESCRIPTION_CERTIFICATE_UNOBTAINABLE(??)
ssl.ALERT_DESCRIPTION_CLOSE_NOTIFY(??)
ssl.ALERT_DESCRIPTION_DECODE_ERROR(??)
ssl.ALERT_DESCRIPTION_DECOMPRESSION_FAILURE(??)
ssl.ALERT_DESCRIPTION_DECRYPT_ERROR(??)
ssl.ALERT_DESCRIPTION_HANDSHAKE_FAILURE(??)
ssl.ALERT_DESCRIPTION_ILLEGAL_PARAMETER(??)
ssl.ALERT_DESCRIPTION_INSUFFICIENT_SECURITY(??)
ssl.ALERT_DESCRIPTION_INTERNAL_ERROR(??)
ssl.ALERT_DESCRIPTION_NO_RENEGOTIATION(??)
ssl.ALERT_DESCRIPTION_PROTOCOL_VERSION(??)
ssl.ALERT_DESCRIPTION_RECORD_OVERFLOW(??)
ssl.ALERT_DESCRIPTION_UNEXPECTED_MESSAGE(??)
ssl.ALERT_DESCRIPTION_UNKNOWN_CA(??)
ssl.ALERT_DESCRIPTION_UNKNOWN_PSK_IDENTITY(??)
ssl.ALERT_DESCRIPTION_UNRECOGNIZED_NAME(??)
ssl.ALERT_DESCRIPTION_UNSUPPORTED_CERTIFICATE(??)
ssl.ALERT_DESCRIPTION_UNSUPPORTED_EXTENSION(??)
ssl.ALERT_DESCRIPTION_USER_CANCELLED(??)
ssl.AlertDescription(??)
ssl.CERT_NONE(??)
ssl.CERT_OPTIONAL(??)
ssl.CERT_REQUIRED(??)
ssl.CHANNEL_BINDING_TYPES(??)
ssl.CertificateError(??)
ssl.DER_cert_to_PEM_cert(??)
ssl.DefaultVerifyPaths(cafile, capath, openssl_cafile_env, openssl_cafile, openssl_capath_env, openssl_capath)
ssl.HAS_ALPN(??)
ssl.HAS_ECDH(??)
ssl.HAS_NEVER_CHECK_COMMON_NAME(??)
ssl.HAS_NPN(??)
ssl.HAS_SNI(??)
ssl.HAS_SSLv2(??)
ssl.HAS_SSLv3(??)
ssl.HAS_TLSv1(??)
ssl.HAS_TLSv1_1(??)
ssl.HAS_TLSv1_2(??)
ssl.HAS_TLSv1_3(??)
ssl.MemoryBIO(??)
ssl.OPENSSL_VERSION
ssl.OPENSSL_VERSION_INFO(??)
ssl.OPENSSL_VERSION_NUMBER
ssl.OP_ALL(??)
ssl.OP_CIPHER_SERVER_PREFERENCE(??)
ssl.OP_ENABLE_MIDDLEBOX_COMPAT(??)
ssl.OP_NO_COMPRESSION(??)
ssl.OP_NO_RENEGOTIATION(??)
ssl.OP_NO_SSLv2(??)
ssl.OP_NO_SSLv3(??)
ssl.OP_NO_TICKET(??)
ssl.OP_NO_TLSv1(??)
ssl.OP_NO_TLSv1_1(??)
ssl.OP_NO_TLSv1_2(??)
ssl.OP_NO_TLSv1_3(??)
ssl.OP_SINGLE_DH_USE(??)
ssl.OP_SINGLE_ECDH_USE(??)
ssl.Options(??)
ssl.PEM_FOOTER
ssl.PEM_HEADER
ssl.PEM_cert_to_DER_cert(??)
ssl.PROTOCOL_SSLv23(??)
ssl.PROTOCOL_TLS(??)
ssl.PROTOCOL_TLS_CLIENT(??)
ssl.PROTOCOL_TLS_SERVER(??)
ssl.PROTOCOL_TLSv1(??)
ssl.PROTOCOL_TLSv1_1(??)
ssl.PROTOCOL_TLSv1_2(??)
ssl.Purpose(??)
ssl.RAND_add(??)
ssl.RAND_bytes(??)
ssl.RAND_pseudo_bytes(??)
ssl.RAND_status(??)
ssl.SOCK_STREAM(??)
ssl.SOL_SOCKET
ssl.SO_TYPE
ssl.SSLCertVerificationError(??)
ssl.SSLContext(??)
ssl.SSLEOFError(??)
ssl.SSLError(??)
ssl.SSLErrorNumber(??)
ssl.SSLObject(??)
ssl.SSLSession(??)
ssl.SSLSocket(??)
ssl.SSLSyscallError(??)
ssl.SSLWantReadError(??)
ssl.SSLWantWriteError(??)
ssl.SSLZeroReturnError(??)
ssl.SSL_ERROR_EOF(??)
ssl.SSL_ERROR_INVALID_ERROR_CODE(??)
ssl.SSL_ERROR_SSL(??)
ssl.SSL_ERROR_SYSCALL(??)
ssl.SSL_ERROR_WANT_CONNECT(??)
ssl.SSL_ERROR_WANT_READ(??)
ssl.SSL_ERROR_WANT_WRITE(??)
ssl.SSL_ERROR_WANT_X509_LOOKUP(??)
ssl.SSL_ERROR_ZERO_RETURN(??)
ssl.TLSVersion(??)
ssl.VERIFY_ALLOW_PROXY_CERTS(??)
ssl.VERIFY_CRL_CHECK_CHAIN(??)
ssl.VERIFY_CRL_CHECK_LEAF(??)
ssl.VERIFY_DEFAULT(??)
ssl.VERIFY_X509_PARTIAL_CHAIN(??)
ssl.VERIFY_X509_STRICT(??)
ssl.VERIFY_X509_TRUSTED_FIRST(??)
ssl.VerifyFlags(??)
ssl.VerifyMode(??)
ssl.base64(??)
ssl.cert_time_to_seconds(??)
ssl.create_connection(??)
ssl.create_default_context(??)
ssl.errno(??)
ssl.get_default_verify_paths(??)
ssl.get_protocol_name(??)
ssl.get_server_certificate(??)
ssl.match_hostname(??)
ssl.namedtuple(??)
ssl.os(??)
ssl.socket(??)
ssl.socket_error(??)
ssl.sys(??)
ssl.warnings(??)
ssl.wrap_socket(??)
stat.FILE_ATTRIBUTE_ARCHIVE
stat.FILE_ATTRIBUTE_COMPRESSED
stat.FILE_ATTRIBUTE_DEVICE
stat.FILE_ATTRIBUTE_DIRECTORY
stat.FILE_ATTRIBUTE_ENCRYPTED
stat.FILE_ATTRIBUTE_HIDDEN
stat.FILE_ATTRIBUTE_INTEGRITY_STREAM
stat.FILE_ATTRIBUTE_NORMAL
stat.FILE_ATTRIBUTE_NOT_CONTENT_INDEXED
stat.FILE_ATTRIBUTE_NO_SCRUB_DATA
stat.FILE_ATTRIBUTE_OFFLINE
stat.FILE_ATTRIBUTE_READONLY
stat.FILE_ATTRIBUTE_REPARSE_POINT
stat.FILE_ATTRIBUTE_SPARSE_FILE
stat.FILE_ATTRIBUTE_SYSTEM
stat.FILE_ATTRIBUTE_TEMPORARY
stat.FILE_ATTRIBUTE_VIRTUAL
stat.SF_APPEND
stat.SF_ARCHIVED
stat.SF_IMMUTABLE
stat.SF_NOUNLINK
stat.SF_SNAPSHOT
stat.ST_ATIME
stat.ST_CTIME
stat.ST_DEV
stat.ST_GID
stat.ST_INO
stat.ST_MODE
stat.ST_MTIME
stat.ST_NLINK
stat.ST_SIZE
stat.ST_UID
stat.S_ENFMT
stat.S_IEXEC
stat.S_IFBLK
stat.S_IFCHR
stat.S_IFDIR
stat.S_IFDOOR
stat.S_IFIFO
stat.S_IFLNK
stat.S_IFMT(??)
stat.S_IFPORT
stat.S_IFREG
stat.S_IFSOCK
stat.S_IFWHT
stat.S_IMODE(??)
stat.S_IREAD
stat.S_IRGRP
stat.S_IROTH
stat.S_IRUSR
stat.S_IRWXG
stat.S_IRWXO
stat.S_IRWXU
stat.S_ISBLK(mode) -> bool
stat.S_ISCHR(mode) -> bool
stat.S_ISDIR(mode) -> bool
stat.S_ISDOOR(mode) -> bool
stat.S_ISFIFO(mode) -> bool
stat.S_ISGID
stat.S_ISLNK(mode) -> bool
stat.S_ISPORT(mode) -> bool
stat.S_ISREG(mode) -> bool
stat.S_ISSOCK(mode) -> bool
stat.S_ISUID
stat.S_ISVTX
stat.S_ISWHT(mode) -> bool
stat.S_IWGRP
stat.S_IWOTH
stat.S_IWRITE
stat.S_IWUSR
stat.S_IXGRP
stat.S_IXOTH
stat.S_IXUSR
stat.UF_APPEND
stat.UF_COMPRESSED
stat.UF_HIDDEN
stat.UF_IMMUTABLE
stat.UF_NODUMP
stat.UF_NOUNLINK
stat.UF_OPAQUE
stat.filemode(??)
staticmethod(function) -> method
statistics.Counter(??)
statistics.Decimal(??)
statistics.Fraction(??)
statistics.LinearRegression(slope, intercept)
statistics.NormalDist(??)
statistics.StatisticsError(??)
statistics.bisect_left(??)
statistics.bisect_right(??)
statistics.correlation(??)
statistics.covariance(??)
statistics.defaultdict(default_factory=None, /, [...]) --> dict with default factory
statistics.erf(??)
statistics.exp(??)
statistics.fabs(??)
statistics.fmean(??)
statistics.fsum(??)
statistics.geometric_mean(??)
statistics.groupby(??)
statistics.harmonic_mean(??)
statistics.hypot(*coordinates) -> value
statistics.linear_regression(??)
statistics.log(x, [base=math.e])
statistics.math(??)
statistics.mean(??)
statistics.median(??)
statistics.median_grouped(??)
statistics.median_high(??)
statistics.median_low(??)
statistics.mode(??)
statistics.mul(??)
statistics.multimode(??)
statistics.namedtuple(??)
statistics.numbers(??)
statistics.pstdev(??)
statistics.pvariance(??)
statistics.quantiles(??)
statistics.random(??)
statistics.reduce(function, iterable[, initial]) -> value
statistics.repeat(object [,times]) -> create an iterator which returns the object
statistics.sqrt(??)
statistics.stdev(??)
statistics.sys(??)
statistics.tau
statistics.variance(??)
str(object='') -> str
string.Formatter(??)
string.Template(??)
string.ascii_letters
string.ascii_lowercase
string.ascii_uppercase
string.capwords(s [,sep]) -> string
string.digits
string.hexdigits
string.octdigits
string.printable
string.punctuation
string.whitespace
stringprep.b1_set(??)
stringprep.b3_exceptions(??)
stringprep.c22_specials(??)
stringprep.c6_set(??)
stringprep.c7_set(??)
stringprep.c8_set(??)
stringprep.c9_set(??)
stringprep.in_table_a1(??)
stringprep.in_table_b1(??)
stringprep.in_table_c11(??)
stringprep.in_table_c11_c12(??)
stringprep.in_table_c12(??)
stringprep.in_table_c21(??)
stringprep.in_table_c21_c22(??)
stringprep.in_table_c22(??)
stringprep.in_table_c3(??)
stringprep.in_table_c4(??)
stringprep.in_table_c5(??)
stringprep.in_table_c6(??)
stringprep.in_table_c7(??)
stringprep.in_table_c8(??)
stringprep.in_table_c9(??)
stringprep.in_table_d1(??)
stringprep.in_table_d2(??)
stringprep.map_table_b2(??)
stringprep.map_table_b3(??)
stringprep.unicodedata(??)
struct.Struct(fmt) --> compiled struct object
struct.calcsize(??)
struct.error(??)
struct.iter_unpack(??)
struct.pack(format, v1, v2, ...) -> bytes
struct.pack_into(format, buffer, offset, v1, v2, ...)
struct.unpack(??)
struct.unpack_from(??)
subprocess.CalledProcessError(??)
subprocess.CompletedProcess(??)
subprocess.DEVNULL
subprocess.PIPE
subprocess.Popen(??)
subprocess.STDOUT
subprocess.SubprocessError(??)
subprocess.TimeoutExpired(??)
subprocess.builtins(??)
subprocess.call(??)
subprocess.check_call(??)
subprocess.check_output(??)
subprocess.contextlib(??)
subprocess.errno(??)
subprocess.fcntl(??)
subprocess.getoutput(??)
subprocess.getstatusoutput(??)
subprocess.io(??)
subprocess.list2cmdline(??)
subprocess.locale(??)
subprocess.os(??)
subprocess.run(??)
subprocess.select(??)
subprocess.selectors(??)
subprocess.signal(??)
subprocess.sys(??)
subprocess.threading(??)
subprocess.time(??)
subprocess.types(??)
subprocess.warnings(??)
sum(??)
sunau.AUDIO_FILE_ENCODING_ADPCM_G721
sunau.AUDIO_FILE_ENCODING_ADPCM_G722
sunau.AUDIO_FILE_ENCODING_ADPCM_G723_3
sunau.AUDIO_FILE_ENCODING_ADPCM_G723_5
sunau.AUDIO_FILE_ENCODING_ALAW_8
sunau.AUDIO_FILE_ENCODING_DOUBLE
sunau.AUDIO_FILE_ENCODING_FLOAT
sunau.AUDIO_FILE_ENCODING_LINEAR_16
sunau.AUDIO_FILE_ENCODING_LINEAR_24
sunau.AUDIO_FILE_ENCODING_LINEAR_32
sunau.AUDIO_FILE_ENCODING_LINEAR_8
sunau.AUDIO_FILE_ENCODING_MULAW_8
sunau.AUDIO_FILE_MAGIC
sunau.AUDIO_UNKNOWN_SIZE
sunau.Au_read(??)
sunau.Au_write(??)
sunau.Error(??)
sunau.namedtuple(??)
sunau.open(??)
sunau.warnings(??)
super() -> same as super(__class__, <first argument>)
symtable.CELL
symtable.Class(??)
symtable.DEF_ANNOT
symtable.DEF_BOUND
symtable.DEF_GLOBAL
symtable.DEF_IMPORT
symtable.DEF_LOCAL
symtable.DEF_NONLOCAL
symtable.DEF_PARAM
symtable.FREE
symtable.Function(??)
symtable.GLOBAL_EXPLICIT
symtable.GLOBAL_IMPLICIT
symtable.LOCAL
symtable.SCOPE_MASK
symtable.SCOPE_OFF
symtable.Symbol(??)
symtable.SymbolTable(??)
symtable.SymbolTableFactory(??)
symtable.USE
symtable.symtable(??)
symtable.weakref(??)
sys.abiflags
sys.addaudithook(??)
sys.api_version
sys.argv(??)
sys.audit(event, *args)
sys.base_exec_prefix
sys.base_prefix
sys.breakpointhook(*args, **kws)
sys.builtin_module_names(??)
sys.byteorder
sys.call_tracing(??)
sys.copyright
sys.displayhook(??)
sys.dont_write_bytecode(??)
sys.exc_info(??)
sys.excepthook(??)
sys.exception(??)
sys.exec_prefix
sys.executable
sys.exit(??)
sys.flags(??)
sys.float_info(??)
sys.float_repr_style
sys.get_asyncgen_hooks(??)
sys.get_coroutine_origin_tracking_depth(??)
sys.getallocatedblocks(??)
sys.getdefaultencoding(??)
sys.getdlopenflags(??)
sys.getfilesystemencodeerrors(??)
sys.getfilesystemencoding(??)
sys.getprofile(??)
sys.getrecursionlimit(??)
sys.getrefcount(??)
sys.getsizeof(object [, default]) -> int
sys.getswitchinterval(??)
sys.gettrace(??)
sys.hash_info(??)
sys.hexversion
sys.implementation(??)
sys.int_info(??)
sys.intern(??)
sys.is_finalizing(??)
sys.maxsize
sys.maxunicode
sys.meta_path(??)
sys.modules(??)
sys.orig_argv(??)
sys.path(??)
sys.path_hooks(??)
sys.path_importer_cache(??)
sys.platform
sys.platlibdir
sys.prefix
sys.pycache_prefix(??)
sys.set_asyncgen_hooks(* [, firstiter] [, finalizer])
sys.set_coroutine_origin_tracking_depth(??)
sys.setdlopenflags(??)
sys.setprofile(function)
sys.setrecursionlimit(??)
sys.setswitchinterval(??)
sys.settrace(function)
sys.stderr(??)
sys.stdin(??)
sys.stdlib_module_names(??)
sys.stdout(??)
sys.thread_info(??)
sys.unraisablehook(??)
sys.version
sys.version_info(??)
sys.warnoptions(??)
sysconfig.expand_makefile_vars(??)
sysconfig.get_config_h_filename(??)
sysconfig.get_config_var(??)
sysconfig.get_config_vars(??)
sysconfig.get_default_scheme(??)
sysconfig.get_makefile_filename(??)
sysconfig.get_path(??)
sysconfig.get_path_names(??)
sysconfig.get_paths(??)
sysconfig.get_platform(??)
sysconfig.get_preferred_scheme(??)
sysconfig.get_python_version(??)
sysconfig.get_scheme_names(??)
sysconfig.is_python_build(??)
sysconfig.os(??)
sysconfig.pardir
sysconfig.parse_config_h(??)
sysconfig.realpath(??)
sysconfig.sys(??)
tabnanny.NannyNag(??)
tabnanny.Whitespace(??)
tabnanny.check(file_or_dir)
tabnanny.errprint(??)
tabnanny.filename_only
tabnanny.format_witnesses(??)
tabnanny.main(??)
tabnanny.os(??)
tabnanny.process_tokens(??)
tabnanny.sys(??)
tabnanny.tokenize(??)
tabnanny.verbose
tarfile.AREGTYPE
tarfile.BLKTYPE
tarfile.BLOCKSIZE
tarfile.CHRTYPE
tarfile.CONTTYPE
tarfile.CompressionError(??)
tarfile.DEFAULT_FORMAT
tarfile.DIRTYPE
tarfile.ENCODING
tarfile.EOFHeaderError(??)
tarfile.EmptyHeaderError(??)
tarfile.ExFileObject(??)
tarfile.ExtractError(??)
tarfile.FIFOTYPE
tarfile.GNUTYPE_LONGLINK
tarfile.GNUTYPE_LONGNAME
tarfile.GNUTYPE_SPARSE
tarfile.GNU_FORMAT
tarfile.GNU_MAGIC
tarfile.GNU_TYPES(??)
tarfile.HeaderError(??)
tarfile.InvalidHeaderError(??)
tarfile.LENGTH_LINK
tarfile.LENGTH_NAME
tarfile.LENGTH_PREFIX
tarfile.LNKTYPE
tarfile.NUL
tarfile.PAX_FIELDS(??)
tarfile.PAX_FORMAT
tarfile.PAX_NAME_FIELDS(??)
tarfile.PAX_NUMBER_FIELDS(??)
tarfile.POSIX_MAGIC
tarfile.RECORDSIZE
tarfile.REGTYPE
tarfile.REGULAR_TYPES(??)
tarfile.ReadError(??)
tarfile.SOLARIS_XHDTYPE
tarfile.SUPPORTED_TYPES(??)
tarfile.SYMTYPE
tarfile.StreamError(??)
tarfile.SubsequentHeaderError(??)
tarfile.TarError(??)
tarfile.TarFile(??)
tarfile.TarInfo(??)
tarfile.TruncatedHeaderError(??)
tarfile.USTAR_FORMAT
tarfile.XGLTYPE
tarfile.XHDTYPE
tarfile.bltn_open(??)
tarfile.calc_chksums(??)
tarfile.copy(??)
tarfile.copyfileobj(??)
tarfile.grp(??)
tarfile.io(??)
tarfile.is_tarfile(??)
tarfile.itn(??)
tarfile.main(??)
tarfile.nti(??)
tarfile.nts(??)
tarfile.open(??)
tarfile.os(??)
tarfile.pwd(??)
tarfile.re(??)
tarfile.shutil(??)
tarfile.stat(??)
tarfile.stn(??)
tarfile.struct(??)
tarfile.symlink_exception(??)
tarfile.sys(??)
tarfile.time(??)
tarfile.version
telnetlib.AO
telnetlib.AUTHENTICATION
telnetlib.AYT
telnetlib.BINARY
telnetlib.BM
telnetlib.BRK
telnetlib.CHARSET
telnetlib.COM_PORT_OPTION
telnetlib.DEBUGLEVEL
telnetlib.DET
telnetlib.DM
telnetlib.DO
telnetlib.DONT
telnetlib.EC
telnetlib.ECHO
telnetlib.EL
telnetlib.ENCRYPT
telnetlib.EOR
telnetlib.EXOPL
telnetlib.FORWARD_X
telnetlib.GA
telnetlib.IAC
telnetlib.IP
telnetlib.KERMIT
telnetlib.LFLOW
telnetlib.LINEMODE
telnetlib.LOGOUT
telnetlib.NAMS
telnetlib.NAOCRD
telnetlib.NAOFFD
telnetlib.NAOHTD
telnetlib.NAOHTS
telnetlib.NAOL
telnetlib.NAOLFD
telnetlib.NAOP
telnetlib.NAOVTD
telnetlib.NAOVTS
telnetlib.NAWS
telnetlib.NEW_ENVIRON
telnetlib.NOOPT
telnetlib.NOP
telnetlib.OLD_ENVIRON
telnetlib.OUTMRK
telnetlib.PRAGMA_HEARTBEAT
telnetlib.PRAGMA_LOGON
telnetlib.RCP
telnetlib.RCTE
telnetlib.RSP
telnetlib.SB
telnetlib.SE
telnetlib.SEND_URL
telnetlib.SGA
telnetlib.SNDLOC
telnetlib.SSPI_LOGON
telnetlib.STATUS
telnetlib.SUPDUP
telnetlib.SUPDUPOUTPUT
telnetlib.SUPPRESS_LOCAL_ECHO
telnetlib.TELNET_PORT
telnetlib.TLS
telnetlib.TM
telnetlib.TN3270E
telnetlib.TSPEED
telnetlib.TTYLOC
telnetlib.TTYPE
telnetlib.TUID
telnetlib.Telnet(??)
telnetlib.VT3270REGIME
telnetlib.WILL
telnetlib.WONT
telnetlib.X3PAD
telnetlib.XASCII
telnetlib.XAUTH
telnetlib.XDISPLOC
telnetlib.selectors(??)
telnetlib.socket(??)
telnetlib.sys(??)
telnetlib.test(??)
telnetlib.theNULL
telnetlib.warnings(??)
tempfile.NamedTemporaryFile(??)
tempfile.SpooledTemporaryFile(??)
tempfile.TMP_MAX
tempfile.TemporaryDirectory(??)
tempfile.TemporaryFile(??)
tempfile.gettempdir(??)
tempfile.gettempdirb(??)
tempfile.gettempprefix(??)
tempfile.gettempprefixb(??)
tempfile.mkdtemp(??)
tempfile.mkstemp(??)
tempfile.mktemp(??)
tempfile.tempdir(??)
tempfile.template
textwrap.TextWrapper(??)
textwrap.dedent(??)
textwrap.fill(??)
textwrap.indent(??)
textwrap.re(??)
textwrap.shorten(??)
textwrap.wrap(??)
threading.Barrier(??)
threading.BoundedSemaphore(??)
threading.BrokenBarrierError(??)
threading.Condition(??)
threading.Event(??)
threading.ExceptHookArgs(??)
threading.Lock(??)
threading.RLock(??)
threading.Semaphore(??)
threading.TIMEOUT_MAX
threading.Thread(??)
threading.ThreadError(??)
threading.Timer(??)
threading.WeakSet(??)
threading.activeCount(??)
threading.active_count(??)
threading.currentThread(??)
threading.current_thread(??)
threading.enumerate(??)
threading.excepthook(exc_type, exc_value, exc_traceback, thread)
threading.functools(??)
threading.get_ident() -> integer
threading.get_native_id() -> integer
threading.getprofile(??)
threading.gettrace(??)
threading.local(??)
threading.main_thread(??)
threading.setprofile(??)
threading.settrace(??)
threading.stack_size([size]) -> size
time.CLOCK_MONOTONIC
time.CLOCK_MONOTONIC_RAW
time.CLOCK_PROCESS_CPUTIME_ID
time.CLOCK_REALTIME
time.CLOCK_THREAD_CPUTIME_ID
time.CLOCK_UPTIME_RAW
time.altzone
time.asctime([tuple]) -> string
time.clock_getres(clk_id) -> floating point number
time.clock_gettime(clk_id) -> float
time.clock_gettime_ns(clk_id) -> int
time.clock_settime(clk_id, time)
time.clock_settime_ns(clk_id, time)
time.ctime(seconds) -> string
time.daylight
time.get_clock_info(name: str) -> dict
time.gmtime([seconds]) -> (tm_year, tm_mon, tm_mday, tm_hour, tm_min,
time.localtime([seconds]) -> (tm_year,tm_mon,tm_mday,tm_hour,tm_min,
time.mktime(tuple) -> floating point number
time.monotonic() -> float
time.monotonic_ns() -> int
time.perf_counter() -> float
time.perf_counter_ns() -> int
time.process_time() -> float
time.process_time_ns(??)
time.sleep(seconds)
time.strftime(format[, tuple]) -> string
time.strptime(string, format) -> struct_time
time.struct_time(??)
time.thread_time() -> float
time.thread_time_ns(??)
time.time() -> floating point number
time.time_ns() -> int
time.timezone
time.tzname(??)
time.tzset()
timeit.Timer(??)
timeit.default_number
timeit.default_repeat
timeit.default_timer(??)
timeit.dummy_src_name
timeit.gc(??)
timeit.itertools(??)
timeit.main(??)
timeit.reindent(??)
timeit.repeat(??)
timeit.sys(??)
timeit.template
timeit.time(??)
timeit.timeit(??)
tkinter.ACTIVE
tkinter.ALL
tkinter.ANCHOR
tkinter.ARC
tkinter.BASELINE
tkinter.BEVEL
tkinter.BOTH
tkinter.BOTTOM
tkinter.BROWSE
tkinter.BUTT
tkinter.BaseWidget(??)
tkinter.BitmapImage(??)
tkinter.BooleanVar(??)
tkinter.Button(??)
tkinter.CASCADE
tkinter.CENTER
tkinter.CHAR
tkinter.CHECKBUTTON
tkinter.CHORD
tkinter.COMMAND
tkinter.CURRENT
tkinter.CallWrapper(??)
tkinter.Canvas(??)
tkinter.Checkbutton(??)
tkinter.DISABLED
tkinter.DOTBOX
tkinter.DoubleVar(??)
tkinter.E
tkinter.END
tkinter.EW
tkinter.EXCEPTION
tkinter.EXTENDED
tkinter.Entry(??)
tkinter.Event(??)
tkinter.EventType(??)
tkinter.FALSE
tkinter.FIRST
tkinter.FLAT
tkinter.Frame(??)
tkinter.GROOVE
tkinter.Grid(??)
tkinter.HIDDEN
tkinter.HORIZONTAL
tkinter.INSERT
tkinter.INSIDE
tkinter.Image(??)
tkinter.IntVar(??)
tkinter.LAST
tkinter.LEFT
tkinter.Label(??)
tkinter.LabelFrame(??)
tkinter.Listbox(??)
tkinter.MITER
tkinter.MOVETO
tkinter.MULTIPLE
tkinter.Menu(??)
tkinter.Menubutton(??)
tkinter.Message(??)
tkinter.Misc(??)
tkinter.N
tkinter.NE
tkinter.NO
tkinter.NONE
tkinter.NORMAL
tkinter.NS
tkinter.NSEW
tkinter.NUMERIC
tkinter.NW
tkinter.NoDefaultRoot(??)
tkinter.OFF
tkinter.ON
tkinter.OUTSIDE
tkinter.OptionMenu(??)
tkinter.PAGES
tkinter.PIESLICE
tkinter.PROJECTING
tkinter.Pack(??)
tkinter.PanedWindow(??)
tkinter.PhotoImage(??)
tkinter.Place(??)
tkinter.RADIOBUTTON
tkinter.RAISED
tkinter.READABLE
tkinter.RIDGE
tkinter.RIGHT
tkinter.ROUND
tkinter.Radiobutton(??)
tkinter.S
tkinter.SCROLL
tkinter.SE
tkinter.SEL
tkinter.SEL_FIRST
tkinter.SEL_LAST
tkinter.SEPARATOR
tkinter.SINGLE
tkinter.SOLID
tkinter.SUNKEN
tkinter.SW
tkinter.Scale(??)
tkinter.Scrollbar(??)
tkinter.Spinbox(??)
tkinter.StringVar(??)
tkinter.TOP
tkinter.TRUE
tkinter.Tcl(??)
tkinter.TclError(??)
tkinter.TclVersion
tkinter.Text(??)
tkinter.Tk(??)
tkinter.TkVersion
tkinter.Toplevel(??)
tkinter.UNDERLINE
tkinter.UNITS
tkinter.VERTICAL
tkinter.Variable(??)
tkinter.W
tkinter.WORD
tkinter.WRITABLE
tkinter.Widget(??)
tkinter.Wm(??)
tkinter.X
tkinter.XView(??)
tkinter.Y
tkinter.YES
tkinter.YView(??)
tkinter.collections(??)
tkinter.colorchooser(??)
tkinter.colorchooser.Chooser(??)
tkinter.colorchooser.Dialog(??)
tkinter.colorchooser.askcolor(??)
tkinter.commondialog(??)
tkinter.commondialog.Dialog(??)
tkinter.commondialog.Frame(??)
tkinter.constants(??)
tkinter.constants.ACTIVE
tkinter.constants.ALL
tkinter.constants.ANCHOR
tkinter.constants.ARC
tkinter.constants.BASELINE
tkinter.constants.BEVEL
tkinter.constants.BOTH
tkinter.constants.BOTTOM
tkinter.constants.BROWSE
tkinter.constants.BUTT
tkinter.constants.CASCADE
tkinter.constants.CENTER
tkinter.constants.CHAR
tkinter.constants.CHECKBUTTON
tkinter.constants.CHORD
tkinter.constants.COMMAND
tkinter.constants.CURRENT
tkinter.constants.DISABLED
tkinter.constants.DOTBOX
tkinter.constants.E
tkinter.constants.END
tkinter.constants.EW
tkinter.constants.EXTENDED
tkinter.constants.FALSE
tkinter.constants.FIRST
tkinter.constants.FLAT
tkinter.constants.GROOVE
tkinter.constants.HIDDEN
tkinter.constants.HORIZONTAL
tkinter.constants.INSERT
tkinter.constants.INSIDE
tkinter.constants.LAST
tkinter.constants.LEFT
tkinter.constants.MITER
tkinter.constants.MOVETO
tkinter.constants.MULTIPLE
tkinter.constants.N
tkinter.constants.NE
tkinter.constants.NO
tkinter.constants.NONE
tkinter.constants.NORMAL
tkinter.constants.NS
tkinter.constants.NSEW
tkinter.constants.NUMERIC
tkinter.constants.NW
tkinter.constants.OFF
tkinter.constants.ON
tkinter.constants.OUTSIDE
tkinter.constants.PAGES
tkinter.constants.PIESLICE
tkinter.constants.PROJECTING
tkinter.constants.RADIOBUTTON
tkinter.constants.RAISED
tkinter.constants.RIDGE
tkinter.constants.RIGHT
tkinter.constants.ROUND
tkinter.constants.S
tkinter.constants.SCROLL
tkinter.constants.SE
tkinter.constants.SEL
tkinter.constants.SEL_FIRST
tkinter.constants.SEL_LAST
tkinter.constants.SEPARATOR
tkinter.constants.SINGLE
tkinter.constants.SOLID
tkinter.constants.SUNKEN
tkinter.constants.SW
tkinter.constants.TOP
tkinter.constants.TRUE
tkinter.constants.UNDERLINE
tkinter.constants.UNITS
tkinter.constants.VERTICAL
tkinter.constants.W
tkinter.constants.WORD
tkinter.constants.X
tkinter.constants.Y
tkinter.constants.YES
tkinter.dialog(??)
tkinter.dialog.Button(??)
tkinter.dialog.DIALOG_ICON
tkinter.dialog.Dialog(??)
tkinter.dialog.Pack(??)
tkinter.dialog.TclError(??)
tkinter.dialog.Widget(??)
tkinter.dnd(??)
tkinter.dnd.DndHandler(??)
tkinter.dnd.Icon(??)
tkinter.dnd.Tester(??)
tkinter.dnd.dnd_start(??)
tkinter.dnd.test(??)
tkinter.dnd.tkinter(??)
tkinter.enum(??)
tkinter.filedialog.BOTH
tkinter.filedialog.BOTTOM
tkinter.filedialog.Button(??)
tkinter.filedialog.Dialog(??)
tkinter.filedialog.Directory(??)
tkinter.filedialog.END
tkinter.filedialog.Entry(??)
tkinter.filedialog.FileDialog(??)
tkinter.filedialog.Frame(??)
tkinter.filedialog.LEFT
tkinter.filedialog.Listbox(??)
tkinter.filedialog.LoadFileDialog(??)
tkinter.filedialog.Open(??)
tkinter.filedialog.RIGHT
tkinter.filedialog.SaveAs(??)
tkinter.filedialog.SaveFileDialog(??)
tkinter.filedialog.Scrollbar(??)
tkinter.filedialog.TOP
tkinter.filedialog.Tk(??)
tkinter.filedialog.Toplevel(??)
tkinter.filedialog.X
tkinter.filedialog.Y
tkinter.filedialog.YES
tkinter.filedialog.askdirectory(??)
tkinter.filedialog.askopenfile(??)
tkinter.filedialog.askopenfilename(??)
tkinter.filedialog.askopenfilenames(??)
tkinter.filedialog.askopenfiles(??)
tkinter.filedialog.asksaveasfile(??)
tkinter.filedialog.asksaveasfilename(??)
tkinter.filedialog.commondialog(??)
tkinter.filedialog.dialogstates(??)
tkinter.filedialog.fnmatch(??)
tkinter.filedialog.os(??)
tkinter.filedialog.test(??)
tkinter.font.BOLD
tkinter.font.Font(??)
tkinter.font.ITALIC
tkinter.font.NORMAL
tkinter.font.ROMAN
tkinter.font.families(??)
tkinter.font.itertools(??)
tkinter.font.names(??)
tkinter.font.nametofont(??)
tkinter.font.tkinter(??)
tkinter.getboolean(??)
tkinter.getdouble(??)
tkinter.getint(??)
tkinter.image_names(??)
tkinter.image_types(??)
tkinter.mainloop(??)
tkinter.messagebox(??)
tkinter.messagebox.ABORT
tkinter.messagebox.ABORTRETRYIGNORE
tkinter.messagebox.CANCEL
tkinter.messagebox.Dialog(??)
tkinter.messagebox.ERROR
tkinter.messagebox.IGNORE
tkinter.messagebox.INFO
tkinter.messagebox.Message(??)
tkinter.messagebox.NO
tkinter.messagebox.OK
tkinter.messagebox.OKCANCEL
tkinter.messagebox.QUESTION
tkinter.messagebox.RETRY
tkinter.messagebox.RETRYCANCEL
tkinter.messagebox.WARNING
tkinter.messagebox.YES
tkinter.messagebox.YESNO
tkinter.messagebox.YESNOCANCEL
tkinter.messagebox.askokcancel(??)
tkinter.messagebox.askquestion(??)
tkinter.messagebox.askretrycancel(??)
tkinter.messagebox.askyesno(??)
tkinter.messagebox.askyesnocancel(??)
tkinter.messagebox.showerror(??)
tkinter.messagebox.showinfo(??)
tkinter.messagebox.showwarning(??)
tkinter.re(??)
tkinter.scrolledtext(??)
tkinter.scrolledtext.BOTH
tkinter.scrolledtext.Frame(??)
tkinter.scrolledtext.Grid(??)
tkinter.scrolledtext.LEFT
tkinter.scrolledtext.Pack(??)
tkinter.scrolledtext.Place(??)
tkinter.scrolledtext.RIGHT
tkinter.scrolledtext.Scrollbar(??)
tkinter.scrolledtext.ScrolledText(??)
tkinter.scrolledtext.Text(??)
tkinter.scrolledtext.Y
tkinter.scrolledtext.example(??)
tkinter.simpledialog.ACTIVE
tkinter.simpledialog.ALL
tkinter.simpledialog.ANCHOR
tkinter.simpledialog.ARC
tkinter.simpledialog.BASELINE
tkinter.simpledialog.BEVEL
tkinter.simpledialog.BOTH
tkinter.simpledialog.BOTTOM
tkinter.simpledialog.BROWSE
tkinter.simpledialog.BUTT
tkinter.simpledialog.BaseWidget(??)
tkinter.simpledialog.BitmapImage(??)
tkinter.simpledialog.BooleanVar(??)
tkinter.simpledialog.Button(??)
tkinter.simpledialog.CASCADE
tkinter.simpledialog.CENTER
tkinter.simpledialog.CHAR
tkinter.simpledialog.CHECKBUTTON
tkinter.simpledialog.CHORD
tkinter.simpledialog.COMMAND
tkinter.simpledialog.CURRENT
tkinter.simpledialog.CallWrapper(??)
tkinter.simpledialog.Canvas(??)
tkinter.simpledialog.Checkbutton(??)
tkinter.simpledialog.DISABLED
tkinter.simpledialog.DOTBOX
tkinter.simpledialog.Dialog(??)
tkinter.simpledialog.DoubleVar(??)
tkinter.simpledialog.E
tkinter.simpledialog.END
tkinter.simpledialog.EW
tkinter.simpledialog.EXCEPTION
tkinter.simpledialog.EXTENDED
tkinter.simpledialog.Entry(??)
tkinter.simpledialog.Event(??)
tkinter.simpledialog.EventType(??)
tkinter.simpledialog.FALSE
tkinter.simpledialog.FIRST
tkinter.simpledialog.FLAT
tkinter.simpledialog.Frame(??)
tkinter.simpledialog.GROOVE
tkinter.simpledialog.Grid(??)
tkinter.simpledialog.HIDDEN
tkinter.simpledialog.HORIZONTAL
tkinter.simpledialog.INSERT
tkinter.simpledialog.INSIDE
tkinter.simpledialog.Image(??)
tkinter.simpledialog.IntVar(??)
tkinter.simpledialog.LAST
tkinter.simpledialog.LEFT
tkinter.simpledialog.Label(??)
tkinter.simpledialog.LabelFrame(??)
tkinter.simpledialog.Listbox(??)
tkinter.simpledialog.MITER
tkinter.simpledialog.MOVETO
tkinter.simpledialog.MULTIPLE
tkinter.simpledialog.Menu(??)
tkinter.simpledialog.Menubutton(??)
tkinter.simpledialog.Message(??)
tkinter.simpledialog.Misc(??)
tkinter.simpledialog.N
tkinter.simpledialog.NE
tkinter.simpledialog.NO
tkinter.simpledialog.NONE
tkinter.simpledialog.NORMAL
tkinter.simpledialog.NS
tkinter.simpledialog.NSEW
tkinter.simpledialog.NUMERIC
tkinter.simpledialog.NW
tkinter.simpledialog.NoDefaultRoot(??)
tkinter.simpledialog.OFF
tkinter.simpledialog.ON
tkinter.simpledialog.OUTSIDE
tkinter.simpledialog.OptionMenu(??)
tkinter.simpledialog.PAGES
tkinter.simpledialog.PIESLICE
tkinter.simpledialog.PROJECTING
tkinter.simpledialog.Pack(??)
tkinter.simpledialog.PanedWindow(??)
tkinter.simpledialog.PhotoImage(??)
tkinter.simpledialog.Place(??)
tkinter.simpledialog.RADIOBUTTON
tkinter.simpledialog.RAISED
tkinter.simpledialog.READABLE
tkinter.simpledialog.RIDGE
tkinter.simpledialog.RIGHT
tkinter.simpledialog.ROUND
tkinter.simpledialog.Radiobutton(??)
tkinter.simpledialog.S
tkinter.simpledialog.SCROLL
tkinter.simpledialog.SE
tkinter.simpledialog.SEL
tkinter.simpledialog.SEL_FIRST
tkinter.simpledialog.SEL_LAST
tkinter.simpledialog.SEPARATOR
tkinter.simpledialog.SINGLE
tkinter.simpledialog.SOLID
tkinter.simpledialog.SUNKEN
tkinter.simpledialog.SW
tkinter.simpledialog.Scale(??)
tkinter.simpledialog.Scrollbar(??)
tkinter.simpledialog.SimpleDialog(??)
tkinter.simpledialog.Spinbox(??)
tkinter.simpledialog.StringVar(??)
tkinter.simpledialog.TOP
tkinter.simpledialog.TRUE
tkinter.simpledialog.Tcl(??)
tkinter.simpledialog.TclError(??)
tkinter.simpledialog.TclVersion
tkinter.simpledialog.Text(??)
tkinter.simpledialog.Tk(??)
tkinter.simpledialog.TkVersion
tkinter.simpledialog.Toplevel(??)
tkinter.simpledialog.UNDERLINE
tkinter.simpledialog.UNITS
tkinter.simpledialog.VERTICAL
tkinter.simpledialog.Variable(??)
tkinter.simpledialog.W
tkinter.simpledialog.WORD
tkinter.simpledialog.WRITABLE
tkinter.simpledialog.Widget(??)
tkinter.simpledialog.Wm(??)
tkinter.simpledialog.X
tkinter.simpledialog.XView(??)
tkinter.simpledialog.Y
tkinter.simpledialog.YES
tkinter.simpledialog.YView(??)
tkinter.simpledialog.askfloat(??)
tkinter.simpledialog.askinteger(??)
tkinter.simpledialog.askstring(??)
tkinter.simpledialog.getboolean(??)
tkinter.simpledialog.getdouble(??)
tkinter.simpledialog.getint(??)
tkinter.simpledialog.image_names(??)
tkinter.simpledialog.image_types(??)
tkinter.simpledialog.mainloop(??)
tkinter.simpledialog.messagebox(??)
tkinter.sys(??)
tkinter.tix.ACROSSTOP
tkinter.tix.ACTIVE
tkinter.tix.ALL
tkinter.tix.ANCHOR
tkinter.tix.ARC
tkinter.tix.ASCII
tkinter.tix.AUTO
tkinter.tix.BALLOON
tkinter.tix.BASELINE
tkinter.tix.BEVEL
tkinter.tix.BOTH
tkinter.tix.BOTTOM
tkinter.tix.BROWSE
tkinter.tix.BUTT
tkinter.tix.Balloon(??)
tkinter.tix.BaseWidget(??)
tkinter.tix.BitmapImage(??)
tkinter.tix.BooleanVar(??)
tkinter.tix.Button(??)
tkinter.tix.ButtonBox(??)
tkinter.tix.CASCADE
tkinter.tix.CELL
tkinter.tix.CENTER
tkinter.tix.CHAR
tkinter.tix.CHECKBUTTON
tkinter.tix.CHORD
tkinter.tix.COLUMN
tkinter.tix.COMMAND
tkinter.tix.CObjView(??)
tkinter.tix.CURRENT
tkinter.tix.CallWrapper(??)
tkinter.tix.Canvas(??)
tkinter.tix.CheckList(??)
tkinter.tix.Checkbutton(??)
tkinter.tix.ComboBox(??)
tkinter.tix.Control(??)
tkinter.tix.DECREASING
tkinter.tix.DISABLED
tkinter.tix.DOTBOX
tkinter.tix.DialogShell(??)
tkinter.tix.DirList(??)
tkinter.tix.DirSelectBox(??)
tkinter.tix.DirSelectDialog(??)
tkinter.tix.DirTree(??)
tkinter.tix.DisplayStyle(??)
tkinter.tix.DoubleVar(??)
tkinter.tix.E
tkinter.tix.END
tkinter.tix.EW
tkinter.tix.EXCEPTION
tkinter.tix.EXTENDED
tkinter.tix.Entry(??)
tkinter.tix.Event(??)
tkinter.tix.EventType(??)
tkinter.tix.ExFileSelectBox(??)
tkinter.tix.ExFileSelectDialog(??)
tkinter.tix.FALSE
tkinter.tix.FIRST
tkinter.tix.FLAT
tkinter.tix.FileEntry(??)
tkinter.tix.FileSelectBox(??)
tkinter.tix.FileSelectDialog(??)
tkinter.tix.FileTypeList(??)
tkinter.tix.Form(??)
tkinter.tix.Frame(??)
tkinter.tix.GROOVE
tkinter.tix.Grid(??)
tkinter.tix.HIDDEN
tkinter.tix.HList(??)
tkinter.tix.HORIZONTAL
tkinter.tix.IMAGE
tkinter.tix.IMAGETEXT
tkinter.tix.IMMEDIATE
tkinter.tix.INCREASING
tkinter.tix.INSERT
tkinter.tix.INSIDE
tkinter.tix.INTEGER
tkinter.tix.Image(??)
tkinter.tix.InputOnly(??)
tkinter.tix.IntVar(??)
tkinter.tix.LAST
tkinter.tix.LEFT
tkinter.tix.Label(??)
tkinter.tix.LabelEntry(??)
tkinter.tix.LabelFrame(??)
tkinter.tix.ListNoteBook(??)
tkinter.tix.Listbox(??)
tkinter.tix.MAIN
tkinter.tix.MAX
tkinter.tix.MITER
tkinter.tix.MOVETO
tkinter.tix.MULTIPLE
tkinter.tix.Menu(??)
tkinter.tix.Menubutton(??)
tkinter.tix.Message(??)
tkinter.tix.Meter(??)
tkinter.tix.Misc(??)
tkinter.tix.N
tkinter.tix.NE
tkinter.tix.NO
tkinter.tix.NONE
tkinter.tix.NORMAL
tkinter.tix.NS
tkinter.tix.NSEW
tkinter.tix.NUMERIC
tkinter.tix.NW
tkinter.tix.NoDefaultRoot(??)
tkinter.tix.NoteBook(??)
tkinter.tix.NoteBookFrame(??)
tkinter.tix.OFF
tkinter.tix.ON
tkinter.tix.OUTSIDE
tkinter.tix.OptionMenu(??)
tkinter.tix.OptionName(??)
tkinter.tix.PAGES
tkinter.tix.PIESLICE
tkinter.tix.PROJECTING
tkinter.tix.Pack(??)
tkinter.tix.PanedWindow(??)
tkinter.tix.PhotoImage(??)
tkinter.tix.Place(??)
tkinter.tix.PopupMenu(??)
tkinter.tix.RADIOBUTTON
tkinter.tix.RAISED
tkinter.tix.READABLE
tkinter.tix.REAL
tkinter.tix.RIDGE
tkinter.tix.RIGHT
tkinter.tix.ROUND
tkinter.tix.ROW
tkinter.tix.Radiobutton(??)
tkinter.tix.ResizeHandle(??)
tkinter.tix.S
tkinter.tix.SCROLL
tkinter.tix.SE
tkinter.tix.SEL
tkinter.tix.SEL_FIRST
tkinter.tix.SEL_LAST
tkinter.tix.SEPARATOR
tkinter.tix.SINGLE
tkinter.tix.SOLID
tkinter.tix.STATUS
tkinter.tix.SUNKEN
tkinter.tix.SW
tkinter.tix.S_REGION
tkinter.tix.Scale(??)
tkinter.tix.Scrollbar(??)
tkinter.tix.ScrolledGrid(??)
tkinter.tix.ScrolledHList(??)
tkinter.tix.ScrolledListBox(??)
tkinter.tix.ScrolledTList(??)
tkinter.tix.ScrolledText(??)
tkinter.tix.ScrolledWindow(??)
tkinter.tix.Select(??)
tkinter.tix.Shell(??)
tkinter.tix.Spinbox(??)
tkinter.tix.StdButtonBox(??)
tkinter.tix.StringVar(??)
tkinter.tix.TCL_ALL_EVENTS
tkinter.tix.TCL_DONT_WAIT
tkinter.tix.TCL_FILE_EVENTS
tkinter.tix.TCL_IDLE_EVENTS
tkinter.tix.TCL_TIMER_EVENTS
tkinter.tix.TCL_WINDOW_EVENTS
tkinter.tix.TEXT
tkinter.tix.TList(??)
tkinter.tix.TOP
tkinter.tix.TRUE
tkinter.tix.Tcl(??)
tkinter.tix.TclError(??)
tkinter.tix.TclVersion
tkinter.tix.Text(??)
tkinter.tix.TixSubWidget(??)
tkinter.tix.TixWidget(??)
tkinter.tix.Tk(??)
tkinter.tix.TkVersion
tkinter.tix.Toplevel(??)
tkinter.tix.Tree(??)
tkinter.tix.UNDERLINE
tkinter.tix.UNITS
tkinter.tix.VERTICAL
tkinter.tix.Variable(??)
tkinter.tix.W
tkinter.tix.WINDOW
tkinter.tix.WORD
tkinter.tix.WRITABLE
tkinter.tix.Widget(??)
tkinter.tix.Wm(??)
tkinter.tix.X
tkinter.tix.XView(??)
tkinter.tix.X_REGION
tkinter.tix.Y
tkinter.tix.YES
tkinter.tix.YView(??)
tkinter.tix.Y_REGION
tkinter.tix.getboolean(??)
tkinter.tix.getdouble(??)
tkinter.tix.getint(??)
tkinter.tix.image_names(??)
tkinter.tix.image_types(??)
tkinter.tix.mainloop(??)
tkinter.tix.os(??)
tkinter.tix.tixCommand(??)
tkinter.tix.tkinter(??)
tkinter.tix.warnings(??)
tkinter.ttk.Button(??)
tkinter.ttk.Checkbutton(??)
tkinter.ttk.Combobox(??)
tkinter.ttk.Entry(??)
tkinter.ttk.Frame(??)
tkinter.ttk.Label(??)
tkinter.ttk.LabelFrame(??)
tkinter.ttk.LabeledScale(??)
tkinter.ttk.Labelframe(??)
tkinter.ttk.Menubutton(??)
tkinter.ttk.Notebook(??)
tkinter.ttk.OptionMenu(??)
tkinter.ttk.PanedWindow(??)
tkinter.ttk.Panedwindow(??)
tkinter.ttk.Progressbar(??)
tkinter.ttk.Radiobutton(??)
tkinter.ttk.Scale(??)
tkinter.ttk.Scrollbar(??)
tkinter.ttk.Separator(??)
tkinter.ttk.Sizegrip(??)
tkinter.ttk.Spinbox(??)
tkinter.ttk.Style(??)
tkinter.ttk.Treeview(??)
tkinter.ttk.Widget(??)
tkinter.ttk.setup_master(??)
tkinter.ttk.tclobjs_to_py(??)
tkinter.ttk.tkinter(??)
tkinter.types(??)
tkinter.wantobjects
token.AMPER
token.AMPEREQUAL
token.ASYNC
token.AT
token.ATEQUAL
token.AWAIT
token.CIRCUMFLEX
token.CIRCUMFLEXEQUAL
token.COLON
token.COLONEQUAL
token.COMMA
token.COMMENT
token.DEDENT
token.DOT
token.DOUBLESLASH
token.DOUBLESLASHEQUAL
token.DOUBLESTAR
token.DOUBLESTAREQUAL
token.ELLIPSIS
token.ENCODING
token.ENDMARKER
token.EQEQUAL
token.EQUAL
token.ERRORTOKEN
token.EXACT_TOKEN_TYPES(??)
token.GREATER
token.GREATEREQUAL
token.INDENT
token.ISEOF(??)
token.ISNONTERMINAL(??)
token.ISTERMINAL(??)
token.LBRACE
token.LEFTSHIFT
token.LEFTSHIFTEQUAL
token.LESS
token.LESSEQUAL
token.LPAR
token.LSQB
token.MINEQUAL
token.MINUS
token.NAME
token.NEWLINE
token.NL
token.NOTEQUAL
token.NT_OFFSET
token.NUMBER
token.N_TOKENS
token.OP
token.PERCENT
token.PERCENTEQUAL
token.PLUS
token.PLUSEQUAL
token.RARROW
token.RBRACE
token.RIGHTSHIFT
token.RIGHTSHIFTEQUAL
token.RPAR
token.RSQB
token.SEMI
token.SLASH
token.SLASHEQUAL
token.SOFT_KEYWORD
token.STAR
token.STAREQUAL
token.STRING
token.TILDE
token.TYPE_COMMENT
token.TYPE_IGNORE
token.VBAR
token.VBAREQUAL
token.tok_name(??)
tokenize.AMPER
tokenize.AMPEREQUAL
tokenize.ASYNC
tokenize.AT
tokenize.ATEQUAL
tokenize.AWAIT
tokenize.BOM_UTF8
tokenize.Binnumber
tokenize.CIRCUMFLEX
tokenize.CIRCUMFLEXEQUAL
tokenize.COLON
tokenize.COLONEQUAL
tokenize.COMMA
tokenize.COMMENT
tokenize.Comment
tokenize.ContStr
tokenize.DEDENT
tokenize.DOT
tokenize.DOUBLESLASH
tokenize.DOUBLESLASHEQUAL
tokenize.DOUBLESTAR
tokenize.DOUBLESTAREQUAL
tokenize.Decnumber
tokenize.Double
tokenize.Double3
tokenize.ELLIPSIS
tokenize.ENCODING
tokenize.ENDMARKER
tokenize.EQEQUAL
tokenize.EQUAL
tokenize.ERRORTOKEN
tokenize.EXACT_TOKEN_TYPES(??)
tokenize.Expfloat
tokenize.Exponent
tokenize.Floatnumber
tokenize.Funny
tokenize.GREATER
tokenize.GREATEREQUAL
tokenize.Hexnumber
tokenize.INDENT
tokenize.ISEOF(??)
tokenize.ISNONTERMINAL(??)
tokenize.ISTERMINAL(??)
tokenize.Ignore
tokenize.Imagnumber
tokenize.Intnumber
tokenize.LBRACE
tokenize.LEFTSHIFT
tokenize.LEFTSHIFTEQUAL
tokenize.LESS
tokenize.LESSEQUAL
tokenize.LPAR
tokenize.LSQB
tokenize.MINEQUAL
tokenize.MINUS
tokenize.NAME
tokenize.NEWLINE
tokenize.NL
tokenize.NOTEQUAL
tokenize.NT_OFFSET
tokenize.NUMBER
tokenize.N_TOKENS
tokenize.Name
tokenize.Number
tokenize.OP
tokenize.Octnumber
tokenize.PERCENT
tokenize.PERCENTEQUAL
tokenize.PLUS
tokenize.PLUSEQUAL
tokenize.PlainToken
tokenize.Pointfloat
tokenize.PseudoExtras
tokenize.PseudoToken
tokenize.RARROW
tokenize.RBRACE
tokenize.RIGHTSHIFT
tokenize.RIGHTSHIFTEQUAL
tokenize.RPAR
tokenize.RSQB
tokenize.SEMI
tokenize.SLASH
tokenize.SLASHEQUAL
tokenize.SOFT_KEYWORD
tokenize.STAR
tokenize.STAREQUAL
tokenize.STRING
tokenize.Single
tokenize.Single3
tokenize.Special
tokenize.StopTokenizing(??)
tokenize.String
tokenize.StringPrefix
tokenize.TILDE
tokenize.TYPE_COMMENT
tokenize.TYPE_IGNORE
tokenize.TextIOWrapper(??)
tokenize.Token
tokenize.TokenError(??)
tokenize.TokenInfo(??)
tokenize.Triple
tokenize.Untokenizer(??)
tokenize.VBAR
tokenize.VBAREQUAL
tokenize.Whitespace
tokenize.any(??)
tokenize.blank_re(??)
tokenize.collections(??)
tokenize.cookie_re(??)
tokenize.detect_encoding(??)
tokenize.endpats(??)
tokenize.functools(??)
tokenize.generate_tokens(??)
tokenize.group(??)
tokenize.lookup(??)
tokenize.main(??)
tokenize.maybe(??)
tokenize.open(??)
tokenize.re(??)
tokenize.single_quoted(??)
tokenize.sys(??)
tokenize.tabsize
tokenize.tok_name(??)
tokenize.tokenize(??)
tokenize.triple_quoted(??)
tokenize.untokenize(??)
tomllib.TOMLDecodeError(??)
tomllib.load(??)
tomllib.loads(??)
trace.CoverageResults(??)
trace.PRAGMA_NOCOVER
trace.Trace(??)
trace.dis(??)
trace.gc(??)
trace.inspect(??)
trace.linecache(??)
trace.main(??)
trace.os(??)
trace.pickle(??)
trace.sys(??)
trace.sysconfig(??)
trace.threading(??)
trace.token(??)
trace.tokenize(??)
traceback.FrameSummary(??)
traceback.StackSummary(??)
traceback.TracebackException(??)
traceback.clear_frames(??)
traceback.collections(??)
traceback.extract_stack(??)
traceback.extract_tb(??)
traceback.format_exc(??)
traceback.format_exception(??)
traceback.format_exception_only(??)
traceback.format_list(??)
traceback.format_stack(??)
traceback.format_tb(??)
traceback.itertools(??)
traceback.linecache(??)
traceback.print_exc(??)
traceback.print_exception(??)
traceback.print_last(??)
traceback.print_list(??)
traceback.print_stack(??)
traceback.print_tb(??)
traceback.suppress(??)
traceback.sys(??)
traceback.textwrap(??)
traceback.walk_stack(??)
traceback.walk_tb(??)
tracemalloc.BaseFilter(??)
tracemalloc.DomainFilter(??)
tracemalloc.Filter(??)
tracemalloc.Frame(??)
tracemalloc.Iterable(??)
tracemalloc.Sequence(??)
tracemalloc.Snapshot(??)
tracemalloc.Statistic(??)
tracemalloc.StatisticDiff(??)
tracemalloc.Trace(??)
tracemalloc.Traceback(??)
tracemalloc.clear_traces(??)
tracemalloc.fnmatch(??)
tracemalloc.get_object_traceback(??)
tracemalloc.get_traceback_limit(??)
tracemalloc.get_traced_memory(??)
tracemalloc.get_tracemalloc_memory(??)
tracemalloc.is_tracing(??)
tracemalloc.linecache(??)
tracemalloc.os(??)
tracemalloc.pickle(??)
tracemalloc.reset_peak(??)
tracemalloc.start(??)
tracemalloc.stop(??)
tracemalloc.take_snapshot(??)
tracemalloc.total_ordering(??)
tty.B0
tty.B110
tty.B115200
tty.B1200
tty.B134
tty.B150
tty.B1800
tty.B19200
tty.B200
tty.B230400
tty.B2400
tty.B300
tty.B38400
tty.B4800
tty.B50
tty.B57600
tty.B600
tty.B75
tty.B9600
tty.BRKINT
tty.BS0
tty.BS1
tty.BSDLY
tty.CC
tty.CDSUSP
tty.CEOF
tty.CEOL
tty.CEOT
tty.CERASE
tty.CFLAG
tty.CFLUSH
tty.CINTR
tty.CKILL
tty.CLNEXT
tty.CLOCAL
tty.CQUIT
tty.CR0
tty.CR1
tty.CR2
tty.CR3
tty.CRDLY
tty.CREAD
tty.CRPRNT
tty.CRTSCTS
tty.CS5
tty.CS6
tty.CS7
tty.CS8
tty.CSIZE
tty.CSTART
tty.CSTOP
tty.CSTOPB
tty.CSUSP
tty.CWERASE
tty.ECHO
tty.ECHOCTL
tty.ECHOE
tty.ECHOK
tty.ECHOKE
tty.ECHONL
tty.ECHOPRT
tty.EXTA
tty.EXTB
tty.FF0
tty.FF1
tty.FFDLY
tty.FIOASYNC
tty.FIOCLEX
tty.FIONBIO
tty.FIONCLEX
tty.FIONREAD
tty.FLUSHO
tty.HUPCL
tty.ICANON
tty.ICRNL
tty.IEXTEN
tty.IFLAG
tty.IGNBRK
tty.IGNCR
tty.IGNPAR
tty.IMAXBEL
tty.INLCR
tty.INPCK
tty.ISIG
tty.ISPEED
tty.ISTRIP
tty.IXANY
tty.IXOFF
tty.IXON
tty.LFLAG
tty.NCCS
tty.NL0
tty.NL1
tty.NLDLY
tty.NOFLSH
tty.OCRNL
tty.OFDEL
tty.OFILL
tty.OFLAG
tty.ONLCR
tty.ONLRET
tty.ONOCR
tty.OPOST
tty.OSPEED
tty.PARENB
tty.PARMRK
tty.PARODD
tty.PENDIN
tty.TAB0
tty.TAB1
tty.TAB2
tty.TAB3
tty.TABDLY
tty.TCIFLUSH
tty.TCIOFF
tty.TCIOFLUSH
tty.TCION
tty.TCOFLUSH
tty.TCOOFF
tty.TCOON
tty.TCSADRAIN
tty.TCSAFLUSH
tty.TCSANOW
tty.TCSASOFT
tty.TIOCCONS
tty.TIOCEXCL
tty.TIOCGETD
tty.TIOCGPGRP
tty.TIOCGSIZE
tty.TIOCGWINSZ
tty.TIOCMBIC
tty.TIOCMBIS
tty.TIOCMGET
tty.TIOCMSET
tty.TIOCM_CAR
tty.TIOCM_CD
tty.TIOCM_CTS
tty.TIOCM_DSR
tty.TIOCM_DTR
tty.TIOCM_LE
tty.TIOCM_RI
tty.TIOCM_RNG
tty.TIOCM_RTS
tty.TIOCM_SR
tty.TIOCM_ST
tty.TIOCNOTTY
tty.TIOCNXCL
tty.TIOCOUTQ
tty.TIOCPKT
tty.TIOCPKT_DATA
tty.TIOCPKT_DOSTOP
tty.TIOCPKT_FLUSHREAD
tty.TIOCPKT_FLUSHWRITE
tty.TIOCPKT_NOSTOP
tty.TIOCPKT_START
tty.TIOCPKT_STOP
tty.TIOCSCTTY
tty.TIOCSETD
tty.TIOCSPGRP
tty.TIOCSSIZE
tty.TIOCSTI
tty.TIOCSWINSZ
tty.TOSTOP
tty.VDISCARD
tty.VEOF
tty.VEOL
tty.VEOL2
tty.VERASE
tty.VINTR
tty.VKILL
tty.VLNEXT
tty.VMIN
tty.VQUIT
tty.VREPRINT
tty.VSTART
tty.VSTOP
tty.VSUSP
tty.VT0
tty.VT1
tty.VTDLY
tty.VTIME
tty.VWERASE
tty.error(??)
tty.setcbreak(??)
tty.setraw(??)
tty.tcdrain(??)
tty.tcflow(??)
tty.tcflush(??)
tty.tcgetattr(??)
tty.tcgetwinsize(??)
tty.tcsendbreak(??)
tty.tcsetattr(??)
tty.tcsetwinsize(??)
tuple(??)
turtle.Canvas(??)
turtle.Pen(??)
turtle.RawPen(??)
turtle.RawTurtle(??)
turtle.Screen(??)
turtle.ScrolledCanvas(??)
turtle.Shape(??)
turtle.TK(??)
turtle.TNavigator(??)
turtle.TPen(??)
turtle.Tbuffer(??)
turtle.Terminator(??)
turtle.Turtle(??)
turtle.TurtleGraphicsError(??)
turtle.TurtleScreen(??)
turtle.TurtleScreenBase(??)
turtle.Vec2D(??)
turtle.addshape(??)
turtle.back(??)
turtle.backward(??)
turtle.begin_fill(??)
turtle.begin_poly(??)
turtle.bgcolor(??)
turtle.bgpic(??)
turtle.bk(??)
turtle.bye(??)
turtle.circle(??)
turtle.clear(??)
turtle.clearscreen(??)
turtle.clearstamp(??)
turtle.clearstamps(??)
turtle.clone(??)
turtle.color(??)
turtle.colormode(??)
turtle.config_dict(??)
turtle.deepcopy(??)
turtle.degrees(??)
turtle.delay(??)
turtle.distance(??)
turtle.done(??)
turtle.dot(??)
turtle.down(??)
turtle.end_fill(??)
turtle.end_poly(??)
turtle.exitonclick(??)
turtle.fd(??)
turtle.fillcolor(??)
turtle.filling(??)
turtle.forward(??)
turtle.get_poly(??)
turtle.get_shapepoly(??)
turtle.getcanvas(??)
turtle.getmethparlist(??)
turtle.getpen(??)
turtle.getscreen(??)
turtle.getshapes(??)
turtle.getturtle(??)
turtle.goto(??)
turtle.heading(??)
turtle.hideturtle(??)
turtle.home(??)
turtle.ht(??)
turtle.inspect(??)
turtle.isdown(??)
turtle.isfile(??)
turtle.isvisible(??)
turtle.join(??)
turtle.left(??)
turtle.listen(??)
turtle.lt(??)
turtle.mainloop(??)
turtle.math(??)
turtle.mode(??)
turtle.numinput(??)
turtle.onclick(??)
turtle.ondrag(??)
turtle.onkey(??)
turtle.onkeypress(??)
turtle.onkeyrelease(??)
turtle.onrelease(??)
turtle.onscreenclick(??)
turtle.ontimer(??)
turtle.pd(??)
turtle.pen(??)
turtle.pencolor(??)
turtle.pendown(??)
turtle.pensize(??)
turtle.penup(??)
turtle.pos(??)
turtle.position(??)
turtle.pu(??)
turtle.radians(??)
turtle.read_docstrings(??)
turtle.readconfig(??)
turtle.register_shape(??)
turtle.reset(??)
turtle.resetscreen(??)
turtle.resizemode(??)
turtle.right(??)
turtle.rt(??)
turtle.screensize(??)
turtle.seth(??)
turtle.setheading(??)
turtle.setpos(??)
turtle.setposition(??)
turtle.settiltangle(??)
turtle.setundobuffer(??)
turtle.setup(??)
turtle.setworldcoordinates(??)
turtle.setx(??)
turtle.sety(??)
turtle.shape(??)
turtle.shapesize(??)
turtle.shapetransform(??)
turtle.shearfactor(??)
turtle.showturtle(??)
turtle.simpledialog(??)
turtle.speed(??)
turtle.split(??)
turtle.st(??)
turtle.stamp(??)
turtle.sys(??)
turtle.textinput(??)
turtle.tilt(??)
turtle.tiltangle(??)
turtle.time(??)
turtle.title(??)
turtle.towards(??)
turtle.tracer(??)
turtle.turtles(??)
turtle.turtlesize(??)
turtle.types(??)
turtle.undo (repeatedly) the last turtle action.
turtle.undobufferentries(??)
turtle.up(??)
turtle.update(??)
turtle.warnings(??)
turtle.width(??)
turtle.window_height(??)
turtle.window_width(??)
turtle.write(??)
turtle.write_docstringdict(??)
turtle.xcor(??)
turtle.ycor(??)
type(object) -> the object's type
types.AsyncGeneratorType(??)
types.BuiltinFunctionType(??)
types.BuiltinMethodType(??)
types.CellType(??)
types.ClassMethodDescriptorType(??)
types.CodeType(??)
types.CoroutineType(??)
types.DynamicClassAttribute(??)
types.EllipsisType(??)
types.FrameType(??)
types.FunctionType(??)
types.GeneratorType(??)
types.GenericAlias(??)
types.GetSetDescriptorType(??)
types.LambdaType(??)
types.MappingProxyType(??)
types.MemberDescriptorType(??)
types.MethodDescriptorType(??)
types.MethodType(??)
types.MethodWrapperType(??)
types.ModuleType(??)
types.NoneType(??)
types.NotImplementedType(??)
types.SimpleNamespace(??)
types.TracebackType(tb_next, tb_frame, tb_lasti, tb_lineno)
types.UnionType(??)
types.WrapperDescriptorType(??)
types.coroutine(??)
types.new_class(??)
types.prepare_class(??)
types.resolve_bases(??)
typing.ABCMeta(??)
typing.AbstractSet(??)
typing.Annotated(??)
typing.Any(??)
typing.AnyStr(??)
typing.AsyncContextManager(??)
typing.AsyncGenerator(??)
typing.AsyncIterable(??)
typing.AsyncIterator(??)
typing.Awaitable(??)
typing.BinaryIO(??)
typing.ByteString(??)
typing.CT_co(??)
typing.Callable(??)
typing.ChainMap(??)
typing.ClassVar(??)
typing.Collection(??)
typing.Concatenate(??)
typing.Container(??)
typing.ContextManager(??)
typing.Coroutine(??)
typing.Counter(??)
typing.DefaultDict(??)
typing.Deque(??)
typing.Dict(??)
typing.EXCLUDED_ATTRIBUTES(??)
typing.Final(??)
typing.ForwardRef(??)
typing.FrozenSet(??)
typing.Generator(??)
typing.Generic(??)
typing.GenericAlias(??)
typing.Hashable(??)
typing.IO(??)
typing.ItemsView(??)
typing.Iterable(??)
typing.Iterator(??)
typing.KT(??)
typing.KeysView(??)
typing.List(??)
typing.Literal(??)
typing.LiteralString(??)
typing.Mapping(??)
typing.MappingView(??)
typing.Match(??)
typing.MethodDescriptorType(??)
typing.MethodWrapperType(??)
typing.MutableMapping(??)
typing.MutableSequence(??)
typing.MutableSet(??)
typing.NamedTuple(??)
typing.NamedTupleMeta(??)
typing.Never(??)
typing.NewType(??)
typing.NoReturn(??)
typing.NotRequired(??)
typing.Optional(??)
typing.OrderedDict(??)
typing.ParamSpec(??)
typing.ParamSpecArgs(??)
typing.ParamSpecKwargs(??)
typing.Pattern(??)
typing.Protocol(??)
typing.Required(??)
typing.Reversible(??)
typing.Self(??)
typing.Sequence(??)
typing.Set(??)
typing.Sized(??)
typing.SupportsAbs(??)
typing.SupportsBytes(??)
typing.SupportsComplex(??)
typing.SupportsFloat(??)
typing.SupportsIndex(??)
typing.SupportsInt(??)
typing.SupportsRound(??)
typing.T(??)
typing.TYPE_CHECKING(??)
typing.T_co(??)
typing.T_contra(??)
typing.Text(??)
typing.TextIO(??)
typing.Tuple(??)
typing.Type(??)
typing.TypeAlias(??)
typing.TypeGuard(??)
typing.TypeVar(??)
typing.TypeVarTuple(??)
typing.TypedDict(??)
typing.Union(??)
typing.Unpack(??)
typing.VT(??)
typing.VT_co(??)
typing.V_co(??)
typing.ValuesView(??)
typing.WrapperDescriptorType(??)
typing.abstractmethod(??)
typing.assert_never(??)
typing.assert_type(??)
typing.cast(??)
typing.clear_overloads(??)
typing.collections(??)
typing.contextlib(??)
typing.dataclass_transform(??)
typing.defaultdict(default_factory=None, /, [...]) --> dict with default factory
typing.final(??)
typing.functools(??)
typing.get_args(??)
typing.get_origin(??)
typing.get_overloads(??)
typing.get_type_hints(??)
typing.io(??)
typing.is_typeddict(??)
typing.no_type_check(??)
typing.no_type_check_decorator(??)
typing.operator(??)
typing.overload(??)
typing.re(??)
typing.reveal_type(??)
typing.runtime_checkable(??)
typing.stdlib_re(??)
typing.sys(??)
typing.types(??)
typing.warnings(??)
urllib.error(??)
urllib.error.ContentTooShortError(??)
urllib.error.HTTPError(??)
urllib.error.URLError(??)
urllib.error.urllib(??)
urllib.parse(??)
urllib.parse.DefragResult(??)
urllib.parse.DefragResultBytes(??)
urllib.parse.ParseResult(??)
urllib.parse.ParseResultBytes(??)
urllib.parse.ResultBase(??)
urllib.parse.SplitResult(??)
urllib.parse.SplitResultBytes(??)
urllib.parse.clear_cache(??)
urllib.parse.functools(??)
urllib.parse.namedtuple(??)
urllib.parse.non_hierarchical(??)
urllib.parse.parse_qs(??)
urllib.parse.parse_qsl(??)
urllib.parse.quote('abc def') -> 'abc%20def'
urllib.parse.quote_from_bytes(??)
urllib.parse.quote_plus(??)
urllib.parse.re(??)
urllib.parse.scheme_chars
urllib.parse.splitattr(??)
urllib.parse.splithost(??)
urllib.parse.splitnport(??)
urllib.parse.splitpasswd(??)
urllib.parse.splitport(??)
urllib.parse.splitquery(??)
urllib.parse.splittag(??)
urllib.parse.splittype(??)
urllib.parse.splituser(??)
urllib.parse.splitvalue(??)
urllib.parse.sys(??)
urllib.parse.to_bytes(??)
urllib.parse.types(??)
urllib.parse.unquote(??)
urllib.parse.unquote_plus(??)
urllib.parse.unquote_to_bytes('abc%20def') -> b'abc def'.
urllib.parse.unwrap(??)
urllib.parse.urldefrag(??)
urllib.parse.urlencode(??)
urllib.parse.urljoin(??)
urllib.parse.urlparse(??)
urllib.parse.urlsplit(??)
urllib.parse.urlunparse(??)
urllib.parse.urlunsplit(??)
urllib.parse.uses_fragment(??)
urllib.parse.uses_netloc(??)
urllib.parse.uses_params(??)
urllib.parse.uses_query(??)
urllib.parse.uses_relative(??)
urllib.parse.warnings(??)
urllib.request(??)
urllib.request.AbstractBasicAuthHandler(??)
urllib.request.AbstractDigestAuthHandler(??)
urllib.request.AbstractHTTPHandler(??)
urllib.request.BaseHandler(??)
urllib.request.CacheFTPHandler(??)
urllib.request.ContentTooShortError(??)
urllib.request.DataHandler(??)
urllib.request.FTPHandler(??)
urllib.request.FancyURLopener(??)
urllib.request.FileHandler(??)
urllib.request.HTTPBasicAuthHandler(??)
urllib.request.HTTPCookieProcessor(??)
urllib.request.HTTPDefaultErrorHandler(??)
urllib.request.HTTPDigestAuthHandler(??)
urllib.request.HTTPError(??)
urllib.request.HTTPErrorProcessor(??)
urllib.request.HTTPHandler(??)
urllib.request.HTTPPasswordMgr(??)
urllib.request.HTTPPasswordMgrWithDefaultRealm(??)
urllib.request.HTTPPasswordMgrWithPriorAuth(??)
urllib.request.HTTPRedirectHandler(??)
urllib.request.HTTPSHandler(??)
urllib.request.MAXFTPCACHE
urllib.request.OpenerDirector(??)
urllib.request.ProxyBasicAuthHandler(??)
urllib.request.ProxyDigestAuthHandler(??)
urllib.request.ProxyHandler(??)
urllib.request.Request(??)
urllib.request.URLError(??)
urllib.request.URLopener(??)
urllib.request.UnknownHandler(??)
urllib.request.addclosehook(??)
urllib.request.addinfourl(??)
urllib.request.base64(??)
urllib.request.bisect(??)
urllib.request.build_opener(??)
urllib.request.contextlib(??)
urllib.request.email(??)
urllib.request.ftpcache(??)
urllib.request.ftperrors(??)
urllib.request.ftpwrapper(??)
urllib.request.getproxies(??)
urllib.request.getproxies_environment(??)
urllib.request.getproxies_macosx_sysconf(??)
urllib.request.hashlib(??)
urllib.request.http(??)
urllib.request.install_opener(??)
urllib.request.io(??)
urllib.request.localhost(??)
urllib.request.noheaders(??)
urllib.request.os(??)
urllib.request.parse_http_list(??)
urllib.request.parse_keqv_list(??)
urllib.request.pathname2url(??)
urllib.request.posixpath(??)
urllib.request.proxy_bypass(??)
urllib.request.proxy_bypass_environment(??)
urllib.request.proxy_bypass_macosx_sysconf(??)
urllib.request.quote('abc def') -> 'abc%20def'
urllib.request.re(??)
urllib.request.request_host(??)
urllib.request.socket(??)
urllib.request.ssl(??)
urllib.request.string(??)
urllib.request.sys(??)
urllib.request.tempfile(??)
urllib.request.thishost(??)
urllib.request.time(??)
urllib.request.unquote(??)
urllib.request.unquote_to_bytes('abc%20def') -> b'abc def'.
urllib.request.unwrap(??)
urllib.request.url2pathname(??)
urllib.request.urlcleanup(??)
urllib.request.urljoin(??)
urllib.request.urlopen(??)
urllib.request.urlparse(??)
urllib.request.urlretrieve(??)
urllib.request.urlsplit(??)
urllib.request.urlunparse(??)
urllib.request.warnings(??)
urllib.response(??)
urllib.response.addbase(??)
urllib.response.addclosehook(??)
urllib.response.addinfo(??)
urllib.response.addinfourl(??)
urllib.response.tempfile(??)
urllib.robotparser.Entry(??)
urllib.robotparser.RequestRate(requests, seconds)
urllib.robotparser.RobotFileParser(??)
urllib.robotparser.RuleLine(??)
urllib.robotparser.collections(??)
urllib.robotparser.urllib(??)
uu.Error(??)
uu.binascii(??)
uu.decode(??)
uu.encode(??)
uu.os(??)
uu.sys(??)
uu.test(??)
uu.warnings(??)
uuid.Enum(??)
uuid.NAMESPACE_DNS(??)
uuid.NAMESPACE_OID(??)
uuid.NAMESPACE_URL(??)
uuid.NAMESPACE_X500(??)
uuid.RESERVED_FUTURE
uuid.RESERVED_MICROSOFT
uuid.RESERVED_NCS
uuid.RFC_4122
uuid.SafeUUID(??)
uuid.UUID(??)
uuid.bytes_(??)
uuid.getnode(??)
uuid.int_(??)
uuid.os(??)
uuid.sys(??)
uuid.uuid1(??)
uuid.uuid3(??)
uuid.uuid4(??)
uuid.uuid5(??)
vars([object]) -> dictionary
venv.CORE_VENV_DEPS(??)
venv.EnvBuilder(??)
venv.create(??)
venv.logger(??)
venv.logging(??)
venv.main(??)
venv.os(??)
venv.shutil(??)
venv.subprocess(??)
venv.sys(??)
venv.sysconfig(??)
venv.types(??)
warnings.WarningMessage(??)
warnings.catch_warnings(??)
warnings.defaultaction
warnings.filters(??)
warnings.filterwarnings(??)
warnings.formatwarning(??)
warnings.onceregistry(??)
warnings.resetwarnings(??)
warnings.showwarning(??)
warnings.simplefilter(??)
warnings.sys(??)
warnings.warn(??)
warnings.warn_explicit(??)
wave.Error(??)
wave.WAVE_FORMAT_PCM
wave.Wave_read(??)
wave.Wave_write(??)
wave.builtins(??)
wave.namedtuple(??)
wave.open(??)
wave.struct(??)
wave.sys(??)
weakref.CallableProxyType(??)
weakref.KeyedRef(??)
weakref.ProxyType(??)
weakref.ProxyTypes(??)
weakref.ReferenceType(??)
weakref.WeakKeyDictionary(??)
weakref.WeakMethod(??)
weakref.WeakSet(??)
weakref.WeakValueDictionary(??)
weakref.finalize(??)
weakref.getweakrefcount(??)
weakref.getweakrefs(??)
weakref.itertools(??)
weakref.proxy(??)
weakref.ref(??)
weakref.sys(??)
webbrowser.BackgroundBrowser(??)
webbrowser.BaseBrowser(??)
webbrowser.Chrome(??)
webbrowser.Chromium(??)
webbrowser.Elinks(??)
webbrowser.Error(??)
webbrowser.Galeon(??)
webbrowser.GenericBrowser(??)
webbrowser.Grail(??)
webbrowser.Konqueror(??)
webbrowser.MacOSX(??)
webbrowser.MacOSXOSAScript(??)
webbrowser.Mozilla(??)
webbrowser.Netscape(??)
webbrowser.Opera(??)
webbrowser.UnixBrowser(??)
webbrowser.get(??)
webbrowser.main(??)
webbrowser.open(??)
webbrowser.open_new(??)
webbrowser.open_new_tab(??)
webbrowser.os(??)
webbrowser.register(??)
webbrowser.register_X_browsers(??)
webbrowser.register_standard_browsers(??)
webbrowser.shlex(??)
webbrowser.shutil(??)
webbrowser.subprocess(??)
webbrowser.sys(??)
webbrowser.threading(??)
webbrowser.warnings(??)
wsgiref.handlers(??)
wsgiref.handlers.BaseCGIHandler(??)
wsgiref.handlers.BaseHandler(??)
wsgiref.handlers.CGIHandler(??)
wsgiref.handlers.FileWrapper(??)
wsgiref.handlers.Headers(??)
wsgiref.handlers.IISCGIHandler(??)
wsgiref.handlers.SimpleHandler(??)
wsgiref.handlers.format_date_time(??)
wsgiref.handlers.guess_scheme(??)
wsgiref.handlers.is_hop_by_hop(??)
wsgiref.handlers.os(??)
wsgiref.handlers.read_environ(??)
wsgiref.handlers.sys(??)
wsgiref.handlers.time(??)
wsgiref.headers(??)
wsgiref.headers.Headers(??)
wsgiref.headers.re(??)
wsgiref.headers.tspecials(??)
wsgiref.simple_server.BaseHTTPRequestHandler(??)
wsgiref.simple_server.HTTPServer(??)
wsgiref.simple_server.ServerHandler(??)
wsgiref.simple_server.SimpleHandler(??)
wsgiref.simple_server.WSGIRequestHandler(??)
wsgiref.simple_server.WSGIServer(??)
wsgiref.simple_server.demo_app(??)
wsgiref.simple_server.make_server(??)
wsgiref.simple_server.python_implementation(??)
wsgiref.simple_server.server_version
wsgiref.simple_server.software_version
wsgiref.simple_server.sys(??)
wsgiref.simple_server.sys_version
wsgiref.simple_server.urllib(??)
wsgiref.types.Any(??)
wsgiref.types.Callable(??)
wsgiref.types.ErrorStream(??)
wsgiref.types.FileWrapper(??)
wsgiref.types.InputStream(??)
wsgiref.types.Iterable(??)
wsgiref.types.Protocol(??)
wsgiref.types.StartResponse(??)
wsgiref.types.TracebackType(tb_next, tb_frame, tb_lasti, tb_lineno)
wsgiref.types.TypeAlias(??)
wsgiref.types.WSGIApplication(??)
wsgiref.types.WSGIEnvironment(??)
wsgiref.util(??)
wsgiref.util.FileWrapper(??)
wsgiref.util.application_uri(??)
wsgiref.util.guess_scheme(??)
wsgiref.util.is_hop_by_hop(??)
wsgiref.util.posixpath(??)
wsgiref.util.request_uri(??)
wsgiref.util.setup_testing_defaults(??)
wsgiref.util.shift_path_info(??)
wsgiref.validate.ErrorWrapper(??)
wsgiref.validate.InputWrapper(??)
wsgiref.validate.IteratorWrapper(??)
wsgiref.validate.PartialIteratorWrapper(??)
wsgiref.validate.WSGIWarning(??)
wsgiref.validate.WriteWrapper(??)
wsgiref.validate.assert_(??)
wsgiref.validate.bad_header_value_re(??)
wsgiref.validate.check_content_type(??)
wsgiref.validate.check_environ(??)
wsgiref.validate.check_errors(??)
wsgiref.validate.check_exc_info(??)
wsgiref.validate.check_headers(??)
wsgiref.validate.check_input(??)
wsgiref.validate.check_iterator(??)
wsgiref.validate.check_status(??)
wsgiref.validate.check_string_type(??)
wsgiref.validate.header_re(??)
wsgiref.validate.re(??)
wsgiref.validate.sys(??)
wsgiref.validate.validator(??)
wsgiref.validate.warnings(??)
xdrlib.BytesIO(??)
xdrlib.ConversionError(??)
xdrlib.Error(??)
xdrlib.Packer(??)
xdrlib.Unpacker(??)
xdrlib.raise_conversion_error(??)
xdrlib.struct(??)
xdrlib.warnings(??)
xdrlib.wraps(??)
xml.dom.DOMException(??)
xml.dom.DOMSTRING_SIZE_ERR
xml.dom.DomstringSizeErr(??)
xml.dom.EMPTY_NAMESPACE(??)
xml.dom.EMPTY_PREFIX(??)
xml.dom.HIERARCHY_REQUEST_ERR
xml.dom.HierarchyRequestErr(??)
xml.dom.INDEX_SIZE_ERR
xml.dom.INUSE_ATTRIBUTE_ERR
xml.dom.INVALID_ACCESS_ERR
xml.dom.INVALID_CHARACTER_ERR
xml.dom.INVALID_MODIFICATION_ERR
xml.dom.INVALID_STATE_ERR
xml.dom.IndexSizeErr(??)
xml.dom.InuseAttributeErr(??)
xml.dom.InvalidAccessErr(??)
xml.dom.InvalidCharacterErr(??)
xml.dom.InvalidModificationErr(??)
xml.dom.InvalidStateErr(??)
xml.dom.NAMESPACE_ERR
xml.dom.NOT_FOUND_ERR
xml.dom.NOT_SUPPORTED_ERR
xml.dom.NO_DATA_ALLOWED_ERR
xml.dom.NO_MODIFICATION_ALLOWED_ERR
xml.dom.NamespaceErr(??)
xml.dom.NoDataAllowedErr(??)
xml.dom.NoModificationAllowedErr(??)
xml.dom.Node(??)
xml.dom.NodeFilter(??)
xml.dom.NodeFilter.NodeFilter(??)
xml.dom.NotFoundErr(??)
xml.dom.NotSupportedErr(??)
xml.dom.SYNTAX_ERR
xml.dom.SyntaxErr(??)
xml.dom.UserDataHandler(??)
xml.dom.VALIDATION_ERR
xml.dom.ValidationErr(??)
xml.dom.WRONG_DOCUMENT_ERR
xml.dom.WrongDocumentErr(??)
xml.dom.XHTML_NAMESPACE
xml.dom.XMLNS_NAMESPACE
xml.dom.XML_NAMESPACE
xml.dom.domreg(??)
xml.dom.domreg.getDOMImplementation(name = None, features = ()) -> DOM implementation.
xml.dom.domreg.registerDOMImplementation(name, factory)
xml.dom.domreg.registered(??)
xml.dom.domreg.sys(??)
xml.dom.domreg.well_known_implementations(??)
xml.dom.expatbuilder(??)
xml.dom.expatbuilder.CDATA_SECTION_NODE
xml.dom.expatbuilder.DOCUMENT_NODE
xml.dom.expatbuilder.EMPTY_NAMESPACE(??)
xml.dom.expatbuilder.EMPTY_PREFIX(??)
xml.dom.expatbuilder.ElementInfo(??)
xml.dom.expatbuilder.ExpatBuilder(??)
xml.dom.expatbuilder.ExpatBuilderNS(??)
xml.dom.expatbuilder.FILTER_ACCEPT
xml.dom.expatbuilder.FILTER_INTERRUPT
xml.dom.expatbuilder.FILTER_REJECT
xml.dom.expatbuilder.FILTER_SKIP
xml.dom.expatbuilder.FilterCrutch(??)
xml.dom.expatbuilder.FilterVisibilityController(??)
xml.dom.expatbuilder.FragmentBuilder(??)
xml.dom.expatbuilder.FragmentBuilderNS(??)
xml.dom.expatbuilder.InternalSubsetExtractor(??)
xml.dom.expatbuilder.Namespaces(??)
xml.dom.expatbuilder.Node(??)
xml.dom.expatbuilder.NodeFilter(??)
xml.dom.expatbuilder.ParseEscape(??)
xml.dom.expatbuilder.Rejecter(??)
xml.dom.expatbuilder.Skipper(??)
xml.dom.expatbuilder.TEXT_NODE
xml.dom.expatbuilder.XMLNS_NAMESPACE
xml.dom.expatbuilder.expat(??)
xml.dom.expatbuilder.makeBuilder(??)
xml.dom.expatbuilder.minidom(??)
xml.dom.expatbuilder.parse(??)
xml.dom.expatbuilder.parseFragment(??)
xml.dom.expatbuilder.parseFragmentString(??)
xml.dom.expatbuilder.parseString(??)
xml.dom.expatbuilder.theDOMImplementation(??)
xml.dom.expatbuilder.xmlbuilder(??)
xml.dom.getDOMImplementation(name = None, features = ()) -> DOM implementation.
xml.dom.minicompat(??)
xml.dom.minicompat.EmptyNodeList(??)
xml.dom.minicompat.NodeList(??)
xml.dom.minicompat.StringTypes(??)
xml.dom.minicompat.defproperty(??)
xml.dom.minicompat.xml(??)
xml.dom.minidom(??)
xml.dom.minidom.Attr(??)
xml.dom.minidom.AttributeList(??)
xml.dom.minidom.CDATASection(??)
xml.dom.minidom.CharacterData(??)
xml.dom.minidom.Childless(??)
xml.dom.minidom.Comment(??)
xml.dom.minidom.DOMImplementation(??)
xml.dom.minidom.DOMImplementationLS(??)
xml.dom.minidom.Document(??)
xml.dom.minidom.DocumentFragment(??)
xml.dom.minidom.DocumentLS(??)
xml.dom.minidom.DocumentType(??)
xml.dom.minidom.EMPTY_NAMESPACE(??)
xml.dom.minidom.EMPTY_PREFIX(??)
xml.dom.minidom.Element(??)
xml.dom.minidom.ElementInfo(??)
xml.dom.minidom.EmptyNodeList(??)
xml.dom.minidom.Entity(??)
xml.dom.minidom.Identified(??)
xml.dom.minidom.NamedNodeMap(??)
xml.dom.minidom.Node(??)
xml.dom.minidom.NodeList(??)
xml.dom.minidom.Notation(??)
xml.dom.minidom.ProcessingInstruction(??)
xml.dom.minidom.ReadOnlySequentialNamedNodeMap(??)
xml.dom.minidom.StringTypes(??)
xml.dom.minidom.Text(??)
xml.dom.minidom.TypeInfo(??)
xml.dom.minidom.XMLNS_NAMESPACE
xml.dom.minidom.defproperty(??)
xml.dom.minidom.domreg(??)
xml.dom.minidom.getDOMImplementation(??)
xml.dom.minidom.io(??)
xml.dom.minidom.parse(??)
xml.dom.minidom.parseString(??)
xml.dom.minidom.xml(??)
xml.dom.pulldom(??)
xml.dom.pulldom.CHARACTERS
xml.dom.pulldom.COMMENT
xml.dom.pulldom.DOMEventStream(??)
xml.dom.pulldom.END_DOCUMENT
xml.dom.pulldom.END_ELEMENT
xml.dom.pulldom.ErrorHandler(??)
xml.dom.pulldom.IGNORABLE_WHITESPACE
xml.dom.pulldom.PROCESSING_INSTRUCTION
xml.dom.pulldom.PullDOM(??)
xml.dom.pulldom.SAX2DOM(??)
xml.dom.pulldom.START_DOCUMENT
xml.dom.pulldom.START_ELEMENT
xml.dom.pulldom.default_bufsize
xml.dom.pulldom.parse(??)
xml.dom.pulldom.parseString(??)
xml.dom.pulldom.xml(??)
xml.dom.registerDOMImplementation(name, factory)
xml.dom.xmlbuilder(??)
xml.dom.xmlbuilder.DOMBuilder(??)
xml.dom.xmlbuilder.DOMBuilderFilter(??)
xml.dom.xmlbuilder.DOMEntityResolver(??)
xml.dom.xmlbuilder.DOMImplementationLS(??)
xml.dom.xmlbuilder.DOMInputSource(??)
xml.dom.xmlbuilder.DocumentLS(??)
xml.dom.xmlbuilder.Options(??)
xml.dom.xmlbuilder.copy(??)
xml.dom.xmlbuilder.xml(??)
xml.etree.ElementInclude.DEFAULT_MAX_INCLUSION_DEPTH
xml.etree.ElementInclude.ElementTree(??)
xml.etree.ElementInclude.FatalIncludeError(??)
xml.etree.ElementInclude.LimitedRecursiveIncludeError(??)
xml.etree.ElementInclude.XINCLUDE
xml.etree.ElementInclude.XINCLUDE_FALLBACK
xml.etree.ElementInclude.XINCLUDE_INCLUDE
xml.etree.ElementInclude.copy(??)
xml.etree.ElementInclude.default_loader(??)
xml.etree.ElementInclude.include(??)
xml.etree.ElementInclude.urljoin(??)
xml.etree.ElementPath(??)
xml.etree.ElementPath.find(??)
xml.etree.ElementPath.findall(??)
xml.etree.ElementPath.findtext(??)
xml.etree.ElementPath.get_parent_map(??)
xml.etree.ElementPath.iterfind(??)
xml.etree.ElementPath.ops(??)
xml.etree.ElementPath.prepare_child(??)
xml.etree.ElementPath.prepare_descendant(??)
xml.etree.ElementPath.prepare_parent(??)
xml.etree.ElementPath.prepare_predicate(??)
xml.etree.ElementPath.prepare_self(??)
xml.etree.ElementPath.prepare_star(??)
xml.etree.ElementPath.re(??)
xml.etree.ElementPath.xpath_tokenizer(??)
xml.etree.ElementPath.xpath_tokenizer_re(??)
xml.etree.ElementTree(??)
xml.etree.ElementTree.C14NWriterTarget(??)
xml.etree.ElementTree.Comment(??)
xml.etree.ElementTree.Element(??)
xml.etree.ElementTree.ElementPath(??)
xml.etree.ElementTree.ElementTree(??)
xml.etree.ElementTree.HTML_EMPTY(??)
xml.etree.ElementTree.PI(??)
xml.etree.ElementTree.ParseError(??)
xml.etree.ElementTree.ProcessingInstruction(??)
xml.etree.ElementTree.QName(??)
xml.etree.ElementTree.SubElement(??)
xml.etree.ElementTree.TreeBuilder(??)
xml.etree.ElementTree.VERSION
xml.etree.ElementTree.XML(??)
xml.etree.ElementTree.XMLID(??)
xml.etree.ElementTree.XMLParser(??)
xml.etree.ElementTree.XMLPullParser(??)
xml.etree.ElementTree.canonicalize(??)
xml.etree.ElementTree.collections(??)
xml.etree.ElementTree.contextlib(??)
xml.etree.ElementTree.dump(??)
xml.etree.ElementTree.fromstring(??)
xml.etree.ElementTree.fromstringlist(??)
xml.etree.ElementTree.indent(??)
xml.etree.ElementTree.io(??)
xml.etree.ElementTree.iselement(??)
xml.etree.ElementTree.iterparse(??)
xml.etree.ElementTree.parse(??)
xml.etree.ElementTree.re(??)
xml.etree.ElementTree.register_namespace(??)
xml.etree.ElementTree.sys(??)
xml.etree.ElementTree.tostring(??)
xml.etree.ElementTree.tostringlist(??)
xml.etree.ElementTree.warnings(??)
xml.etree.cElementTree(??)
xml.etree.cElementTree.C14NWriterTarget(??)
xml.etree.cElementTree.Comment(??)
xml.etree.cElementTree.Element(??)
xml.etree.cElementTree.ElementTree(??)
xml.etree.cElementTree.PI(??)
xml.etree.cElementTree.ParseError(??)
xml.etree.cElementTree.ProcessingInstruction(??)
xml.etree.cElementTree.QName(??)
xml.etree.cElementTree.SubElement(??)
xml.etree.cElementTree.TreeBuilder(??)
xml.etree.cElementTree.VERSION
xml.etree.cElementTree.XML(??)
xml.etree.cElementTree.XMLID(??)
xml.etree.cElementTree.XMLParser(??)
xml.etree.cElementTree.XMLPullParser(??)
xml.etree.cElementTree.canonicalize(??)
xml.etree.cElementTree.dump(??)
xml.etree.cElementTree.fromstring(??)
xml.etree.cElementTree.fromstringlist(??)
xml.etree.cElementTree.indent(??)
xml.etree.cElementTree.iselement(??)
xml.etree.cElementTree.iterparse(??)
xml.etree.cElementTree.parse(??)
xml.etree.cElementTree.register_namespace(??)
xml.etree.cElementTree.tostring(??)
xml.etree.cElementTree.tostringlist(??)
xml.parsers(??)
xml.parsers.expat(??)
xml.parsers.expat.EXPAT_VERSION
xml.parsers.expat.ErrorString(??)
xml.parsers.expat.ExpatError(??)
xml.parsers.expat.ParserCreate(??)
xml.parsers.expat.XMLParserType(??)
xml.parsers.expat.XML_PARAM_ENTITY_PARSING_ALWAYS
xml.parsers.expat.XML_PARAM_ENTITY_PARSING_NEVER
xml.parsers.expat.XML_PARAM_ENTITY_PARSING_UNLESS_STANDALONE
xml.parsers.expat.error(??)
xml.parsers.expat.errors(??)
xml.parsers.expat.expat_CAPI(??)
xml.parsers.expat.features(??)
xml.parsers.expat.model(??)
xml.parsers.expat.native_encoding
xml.parsers.expat.sys(??)
xml.parsers.expat.version_info(??)
xml.sax(??)
xml.sax.ContentHandler(??)
xml.sax.ErrorHandler(??)
xml.sax.InputSource(??)
xml.sax.SAXException(??)
xml.sax.SAXNotRecognizedException(??)
xml.sax.SAXNotSupportedException(??)
xml.sax.SAXParseException(??)
xml.sax.SAXReaderNotAvailable(??)
xml.sax.default_parser_list(??)
xml.sax.expatreader.AttributesImpl(??)
xml.sax.expatreader.AttributesNSImpl(??)
xml.sax.expatreader.ExpatLocator(??)
xml.sax.expatreader.ExpatParser(??)
xml.sax.expatreader.SAXException(??)
xml.sax.expatreader.SAXNotRecognizedException(??)
xml.sax.expatreader.SAXNotSupportedException(??)
xml.sax.expatreader.SAXParseException(??)
xml.sax.expatreader.SAXReaderNotAvailable(??)
xml.sax.expatreader.create_parser(??)
xml.sax.expatreader.expat(??)
xml.sax.expatreader.feature_external_ges
xml.sax.expatreader.feature_external_pes
xml.sax.expatreader.feature_namespace_prefixes
xml.sax.expatreader.feature_namespaces
xml.sax.expatreader.feature_string_interning
xml.sax.expatreader.feature_validation
xml.sax.expatreader.handler(??)
xml.sax.expatreader.property_interning_dict
xml.sax.expatreader.property_xml_string
xml.sax.expatreader.saxutils(??)
xml.sax.expatreader.version
xml.sax.expatreader.xmlreader(??)
xml.sax.handler(??)
xml.sax.handler.ContentHandler(??)
xml.sax.handler.DTDHandler(??)
xml.sax.handler.EntityResolver(??)
xml.sax.handler.ErrorHandler(??)
xml.sax.handler.LexicalHandler(??)
xml.sax.handler.all_features(??)
xml.sax.handler.all_properties(??)
xml.sax.handler.feature_external_ges
xml.sax.handler.feature_external_pes
xml.sax.handler.feature_namespace_prefixes
xml.sax.handler.feature_namespaces
xml.sax.handler.feature_string_interning
xml.sax.handler.feature_validation
xml.sax.handler.property_declaration_handler
xml.sax.handler.property_dom_node
xml.sax.handler.property_encoding
xml.sax.handler.property_interning_dict
xml.sax.handler.property_lexical_handler
xml.sax.handler.property_xml_string
xml.sax.handler.version
xml.sax.make_parser(??)
xml.sax.parse(??)
xml.sax.parseString(??)
xml.sax.saxutils.XMLFilterBase(??)
xml.sax.saxutils.XMLGenerator(??)
xml.sax.saxutils.codecs(??)
xml.sax.saxutils.escape(??)
xml.sax.saxutils.handler(??)
xml.sax.saxutils.io(??)
xml.sax.saxutils.os(??)
xml.sax.saxutils.prepare_input_source(??)
xml.sax.saxutils.quoteattr(??)
xml.sax.saxutils.unescape(??)
xml.sax.saxutils.urllib(??)
xml.sax.saxutils.xmlreader(??)
xml.sax.xmlreader(??)
xml.sax.xmlreader.AttributesImpl(??)
xml.sax.xmlreader.AttributesNSImpl(??)
xml.sax.xmlreader.IncrementalParser(??)
xml.sax.xmlreader.InputSource(??)
xml.sax.xmlreader.Locator(??)
xml.sax.xmlreader.SAXNotRecognizedException(??)
xml.sax.xmlreader.SAXNotSupportedException(??)
xml.sax.xmlreader.XMLReader(??)
xml.sax.xmlreader.handler(??)
xmlrpc.client(??)
xmlrpc.client.APPLICATION_ERROR
xmlrpc.client.Binary(??)
xmlrpc.client.Boolean(??)
xmlrpc.client.BytesIO(??)
xmlrpc.client.DateTime(??)
xmlrpc.client.Decimal(??)
xmlrpc.client.Error(??)
xmlrpc.client.ExpatParser(??)
xmlrpc.client.FastMarshaller(??)
xmlrpc.client.FastParser(??)
xmlrpc.client.FastUnmarshaller(??)
xmlrpc.client.Fault(??)
xmlrpc.client.GzipDecodedResponse(??)
xmlrpc.client.INTERNAL_ERROR
xmlrpc.client.INVALID_ENCODING_CHAR
xmlrpc.client.INVALID_METHOD_PARAMS
xmlrpc.client.INVALID_XMLRPC
xmlrpc.client.MAXINT
xmlrpc.client.METHOD_NOT_FOUND
xmlrpc.client.MININT
xmlrpc.client.Marshaller(??)
xmlrpc.client.MultiCall(??)
xmlrpc.client.MultiCallIterator(??)
xmlrpc.client.NOT_WELLFORMED_ERROR
xmlrpc.client.PARSE_ERROR
xmlrpc.client.ProtocolError(??)
xmlrpc.client.ResponseError(??)
xmlrpc.client.SERVER_ERROR
xmlrpc.client.SYSTEM_ERROR
xmlrpc.client.SafeTransport(??)
xmlrpc.client.Server(??)
xmlrpc.client.ServerProxy(??)
xmlrpc.client.TRANSPORT_ERROR
xmlrpc.client.Transport(??)
xmlrpc.client.UNSUPPORTED_ENCODING
xmlrpc.client.Unmarshaller(??)
xmlrpc.client.WRAPPERS(??)
xmlrpc.client.base64(??)
xmlrpc.client.boolean(??)
xmlrpc.client.datetime(year, month, day[, hour[, minute[, second[, microsecond[,tzinfo]]]]])
xmlrpc.client.dumps(??)
xmlrpc.client.errno(??)
xmlrpc.client.escape(??)
xmlrpc.client.expat(??)
xmlrpc.client.getparser() -> parser, unmarshaller
xmlrpc.client.gzip(??)
xmlrpc.client.gzip_decode(??)
xmlrpc.client.gzip_encode(??)
xmlrpc.client.http(??)
xmlrpc.client.loads(??)
xmlrpc.client.sys(??)
xmlrpc.client.time(??)
xmlrpc.client.urllib(??)
xmlrpc.server(??)
xmlrpc.server.BaseHTTPRequestHandler(??)
xmlrpc.server.CGIXMLRPCRequestHandler(??)
xmlrpc.server.DocCGIXMLRPCRequestHandler(??)
xmlrpc.server.DocXMLRPCRequestHandler(??)
xmlrpc.server.DocXMLRPCServer(??)
xmlrpc.server.Fault(??)
xmlrpc.server.MultiPathXMLRPCServer(??)
xmlrpc.server.ServerHTMLDoc(??)
xmlrpc.server.SimpleXMLRPCDispatcher(??)
xmlrpc.server.SimpleXMLRPCRequestHandler(??)
xmlrpc.server.SimpleXMLRPCServer(??)
xmlrpc.server.XMLRPCDocGenerator(??)
xmlrpc.server.dumps(??)
xmlrpc.server.fcntl(??)
xmlrpc.server.gzip_decode(??)
xmlrpc.server.gzip_encode(??)
xmlrpc.server.html(??)
xmlrpc.server.http(??)
xmlrpc.server.list_public_methods(??)
xmlrpc.server.loads(??)
xmlrpc.server.os(??)
xmlrpc.server.partial(func, *args, **keywords) - new function with partial application
xmlrpc.server.pydoc(??)
xmlrpc.server.re(??)
xmlrpc.server.resolve_dotted_attribute(a, 'b.c.d') => a.b.c.d
xmlrpc.server.signature(??)
xmlrpc.server.socketserver(??)
xmlrpc.server.sys(??)
xmlrpc.server.traceback(??)
zip(*iterables, strict=False) --> Yield tuples until an input is exhausted.
zipapp.MAIN_TEMPLATE
zipapp.ZipAppError(??)
zipapp.contextlib(??)
zipapp.create_archive(??)
zipapp.get_interpreter(??)
zipapp.main(??)
zipapp.os(??)
zipapp.pathlib(??)
zipapp.shebang_encoding
zipapp.shutil(??)
zipapp.stat(??)
zipapp.sys(??)
zipapp.zipfile(??)
zipfile.BZIP2_VERSION
zipfile.BadZipFile(??)
zipfile.BadZipfile(??)
zipfile.CompleteDirs(??)
zipfile.DEFAULT_VERSION
zipfile.FastLookup(??)
zipfile.LZMACompressor(??)
zipfile.LZMADecompressor(??)
zipfile.LZMA_VERSION
zipfile.LargeZipFile(??)
zipfile.MAX_EXTRACT_VERSION
zipfile.Path(??)
zipfile.PyZipFile(??)
zipfile.ZIP64_LIMIT
zipfile.ZIP64_VERSION
zipfile.ZIP_BZIP2
zipfile.ZIP_DEFLATED
zipfile.ZIP_FILECOUNT_LIMIT
zipfile.ZIP_LZMA
zipfile.ZIP_MAX_COMMENT
zipfile.ZIP_STORED
zipfile.ZipExtFile(??)
zipfile.ZipFile(??)
zipfile.ZipInfo(??)
zipfile.binascii(??)
zipfile.bz2(??)
zipfile.compressor_names(??)
zipfile.contextlib(??)
zipfile.crc32(??)
zipfile.error(??)
zipfile.importlib(??)
zipfile.io(??)
zipfile.is_zipfile(??)
zipfile.itertools(??)
zipfile.lzma(??)
zipfile.main(??)
zipfile.os(??)
zipfile.pathlib(??)
zipfile.posixpath(??)
zipfile.shutil(??)
zipfile.sizeCentralDir
zipfile.sizeEndCentDir
zipfile.sizeEndCentDir64
zipfile.sizeEndCentDir64Locator
zipfile.sizeFileHeader
zipfile.stat(??)
zipfile.stringCentralDir
zipfile.stringEndArchive
zipfile.stringEndArchive64
zipfile.stringEndArchive64Locator
zipfile.stringFileHeader
zipfile.struct(??)
zipfile.structCentralDir
zipfile.structEndArchive
zipfile.structEndArchive64
zipfile.structEndArchive64Locator
zipfile.structFileHeader
zipfile.sys(??)
zipfile.threading(??)
zipfile.time(??)
zipfile.zlib(??)
zipimport.END_CENTRAL_DIR_SIZE
zipimport.MAX_COMMENT_LEN
zipimport.STRING_END_ARCHIVE
zipimport.ZipImportError(??)
zipimport.alt_path_sep
zipimport.cp437_table
zipimport.marshal(??)
zipimport.path_sep
zipimport.sys(??)
zipimport.time(??)
zipimport.zipimporter(archivepath) -> zipimporter object
zoneinfo.InvalidTZPathWarning(??)
zoneinfo.TZPATH(??)
zoneinfo.ZoneInfo(??)
zoneinfo.ZoneInfoNotFoundError(??)
zoneinfo.available_timezones(??)
zoneinfo.reset_tzpath(??)
