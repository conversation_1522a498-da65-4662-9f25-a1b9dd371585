#ifndef QADVANCEDSETTING_H
#define QADVANCEDSETTING_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备属性-高级设置
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
#include <QPushButton>
#include <QGridLayout>

class QAdvancedSetting : public QWidget
{
    Q_OBJECT
public:
    explicit QAdvancedSetting(QWidget *parent = nullptr);

private:
    //高级设置
    QPushButton *m_btnComHarmonicWave;           //常规谐波
    QPushButton *m_btnPointsSetting;             //积分设置
    QPushButton *m_btnIECHarmonicWave;           //IEC&谐波
    QPushButton *m_btnElectricMachineSetting;    //电机设置

signals:
};

#endif // QADVANCEDSETTING_H
