#include "buttonedit.h"

#include <QHBoxLayout>
#include <QPushButton>

ButtonEdit::ButtonEdit(const QString &text, QWidget *parent)
    : QLineEdit(parent)
{
    button = new QPushButton(text);
    // button->setFlat(true);
    addButton();
}

ButtonEdit::ButtonEdit(const QIcon &icon, QWidget *parent)
    : QLineEdit(parent)
{
    button = new QPushButton;
    button->setIcon(icon);
    button->setFlat(true);
    addButton();
}

void ButtonEdit::addButton()
{
    connect(button, &QPushButton::clicked, this, &ButtonEdit::buttonClicked);
    // 按钮已经是edit的一部分了，不应该再能被单独聚焦，否则可能导致误触
    button->setFocusPolicy(Qt::NoFocus);
    // 设置鼠标，否则点击按钮时仍然会显示输入内容时的鼠标图标
    button->setCursor(Qt::ArrowCursor);

    auto policy = button->sizePolicy();
    policy.setHorizontalPolicy(QSizePolicy::Maximum );
    button->setSizePolicy(policy);

    auto btnLayout = new QHBoxLayout;
    btnLayout->addStretch();
    btnLayout->addWidget(button);

    // 设置组件右对齐，按钮会显示在edit的右侧
    btnLayout->setAlignment(Qt::AlignRight);
    btnLayout->setContentsMargins(0, 0, 0, 0);
    setLayout(btnLayout);

    // 设置输入区域的范围
    setTextMargins(0, 0,  button->sizeHint().width(), 0);
}
