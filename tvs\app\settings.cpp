#include "settings.h"

#include <QSettings>

void AppSettings::set(QAnyStringView key, const QVariant &value)
{
    m_settings->setValue(key, value);
}

QVariant AppSettings::get(QAnyStringView key, const QVariant &defaultValue) const
{
    return m_settings->value(key, defaultValue);
}

QVariant AppSettings::get(QAnyStringView key) const
{

    return m_settings->value(key);
}

AppSettings::AppSettings()
{
    m_settings = new QSettings("./config/app.ini", QSettings::IniFormat);
}
