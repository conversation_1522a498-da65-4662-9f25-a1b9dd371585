#ifndef DRAGTREEWIDGET_H
#define DRAGTREEWIDGET_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 可拖拽树控件
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QTreeWidget>
class QLabel;
class QTreeWidgetItem;

class DragTreeWidget : public QTreeWidget
{
    Q_OBJECT
public:
    DragTreeWidget(QWidget *parent = nullptr);

protected:
    QPoint m_pos;
    QTreeWidgetItem* m_item;
    QLabel* m_label;

    void mousePressEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
};

#endif // DRAGTREEWIDGET_H
