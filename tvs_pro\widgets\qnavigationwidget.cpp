/**********************************************************
 * @file QNavigationWidget.cpp
 * @brief A completed implementation of QNavigationWidget.
 * <AUTHOR>
 * @date 2021-8-8
 **********************************************************/

#include "QNavigationWidget.h"

QNavigationWidget::QNavigationWidget(QWidget *parent) : QWidget(parent)
{
    QHBoxLayout *layout = new QHBoxLayout(this);
    layout->setContentsMargins(0,0,0,0);

    m_navigationMenu = new QListWidget(this);
    m_navigationMenu->setFixedWidth(260);
    m_stackedWidget = new QStackedWidget(this);
    layout->addWidget(m_navigationMenu);
    layout->addWidget(m_stackedWidget);
    setLayout(layout);

    connect(m_navigationMenu, &QListWidget::currentRowChanged, this, &QNavigationWidget::changeTab);
}

void QNavigationWidget::addTab(QWidget *page, const QString &item, const QString &toolTip, const QString &iconFile)
{
    m_stackedWidget->addWidget(page);
    // m_navigationMenu->addItem(item);
    m_navigationMenu->addItem(new QListWidgetItem(QIcon(iconFile), item));
    if (!toolTip.isEmpty())
        m_navigationMenu->item(m_navigationMenu->count() - 1)->setToolTip(toolTip);
}

int QNavigationWidget::insertTab(const int index, QWidget *page, const QString &item, const QString &toolTip, const QString &iconFile)
{
    m_stackedWidget->insertWidget(index, page);
    // m_navigationMenu->insertItem(index, item);
    m_navigationMenu->insertItem(index, new QListWidgetItem(QIcon(iconFile), item));
    if (!toolTip.isEmpty())
        m_navigationMenu->item(index)->setToolTip(toolTip);
    return index;
}

void QNavigationWidget::setTabToolTip(const int index, const QString &toolTip)
{
    m_navigationMenu->item(index)->setToolTip(toolTip);
}

void QNavigationWidget::setTabText(const int index, const QString &text)
{
    m_navigationMenu->item(index)->setText(text);
}

void QNavigationWidget::deleteTab(const int index)
{
    m_stackedWidget->removeWidget(m_stackedWidget->widget(index));
    delete m_navigationMenu->takeItem(index);
}

int QNavigationWidget::getCurrentIndex() const
{
    return m_navigationMenu->currentRow();
}

void QNavigationWidget::setCurrentIndex(const int index)
{
    m_navigationMenu->setCurrentRow(index);
    m_stackedWidget->setCurrentIndex(index);
}

QWidget *QNavigationWidget::getCurrentWidget() const
{
    return m_stackedWidget->currentWidget();
}

void QNavigationWidget::changeTab(const int index)
{
    m_stackedWidget->setCurrentIndex(index);
    emit currentIndexChanged(index);
}
