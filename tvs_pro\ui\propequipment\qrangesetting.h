#ifndef QRANGESETTING_H
#define QRANGESETTING_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备属性-量程配置
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>


class SwitchButton;
class QLineEdit;
class QComboBox;


//量程配置
class QRangeSetting : public QWidget
{
    Q_OBJECT
public:
    explicit QRangeSetting(QWidget *parent = nullptr);

private:

    //配置单元
    QComboBox *m_combSettingCell;
    //电压自动量程
    SwitchButton *m_switchVoltageAutoRange;
    //电压量程
    QComboBox *m_combVoltageRange;
    //电流自动量程
    SwitchButton *m_switchElectricAutoRange;
    //电流量程
    QComboBox *m_combElectricRange;
    //传感器量程
    QComboBox *m_combSensorRange;
    //外部传感器
    SwitchButton *m_switchSensorOut;
    //传感器比率
    QLineEdit *m_sensorRatio;
    //电压传感器PT
    QLineEdit *m_voltageSensorPT;
    //电流传感器PT
    QLineEdit *m_electricSensorPT;

signals:
};

#endif // QRANGESETTING_H
