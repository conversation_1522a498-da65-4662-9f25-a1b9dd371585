#include "uipjopen.h"
#include "ui/project/uirecentfiles.h"
#include "widgets/stdwidget.h"
#include "widgets/uirichlist.h"

#include <MainWindow.h>
#include <QFileDialog>
#include <QMessageBox>
#include <QVBoxLayout>

#include <app/settings.h>

UiPjOpen::UiPjOpen(QWidget *parent)
    : QWidget{parent}
{
    // 创建垂直布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_pj_open.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    // 创建标题
    auto *titleLabel = new LabelH2("打开");
    titleLabel->setObjectName("welcome");
    mainLayout->addWidget(titleLabel);

    auto btnOpen = new QPushButton(QIcon("./res/icon/Open.png"), "打开工程", this);
    mainLayout->addWidget(btnOpen);

    connect(btnOpen, &QPushButton::clicked,[=]()
    {
        MainWindow::instance->openProject();
    }
           );

    // auto uiList = new RecentFilesWidget();
    // mainLayout->addWidget(uiList);

    auto recentFiles = new UiRecentFiles();
    mainLayout->addWidget(recentFiles, 1);
}
