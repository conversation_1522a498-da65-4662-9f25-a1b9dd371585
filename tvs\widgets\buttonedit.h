#ifndef BUTTONEDIT_H
#define BUTTONEDIT_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 带按钮的LineEdit
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QLineEdit >
class QPushButton;

class ButtonEdit: public QLineEdit
{
    Q_OBJECT
public:
    explicit ButtonEdit(const QString &text, QWidget *parent = nullptr);
    explicit ButtonEdit(const QIcon &icon, QWidget *parent = nullptr);
    ~ButtonEdit() override = default;

private:
    // void setTextButton();
    // void setIconButton();
    void addButton();

private:
    QPushButton *button;

Q_SIGNALS:
    void buttonClicked(bool);
};

#endif // BUTTONEDIT_H
