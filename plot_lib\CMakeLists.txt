cmake_minimum_required(VERSION 3.14)

project(plot LANGUAGES CXX)

set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_INCLUDE_CURRENT_DIR ON)

# Debug 模式下库名加 'd' 后缀
set(CMAKE_DEBUG_POSTFIX "d")

find_package(QT NAMES Qt6 Qt5 REQUIRED COMPONENTS Widgets)
find_package(Qt${QT_VERSION_MAJOR} REQUIRED COMPONENTS Widgets PrintSupport)

file(GLOB_RECURSE MAIN_SOURCES
    "*.cpp"
    "*.h"
)

list(FILTER MAIN_SOURCES EXCLUDE REGEX ".*/(build|Build|BUILD|_build|cmake-build-.*)/.*")
list(FILTER MAIN_SOURCES EXCLUDE REGEX ".*moc_.*\\.cpp$")

# 创建动态库而不是可执行文件
add_library(plot SHARED
    ${MAIN_SOURCES}
    plot_res.qrc
)


# 设置输出目录
set(LIBRARY_OUTPUT_PATH ${CMAKE_SOURCE_DIR}/3rd/plot/lib)
file(MAKE_DIRECTORY ${LIBRARY_OUTPUT_PATH})

set_target_properties(plot PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${LIBRARY_OUTPUT_PATH}
    LIBRARY_OUTPUT_DIRECTORY_DEBUG ${LIBRARY_OUTPUT_PATH}
    LIBRARY_OUTPUT_DIRECTORY_RELEASE ${LIBRARY_OUTPUT_PATH}
)

# 编译选项
target_compile_options(plot PRIVATE
    -Wa,-mbig-obj
    -Wl,--stack,67108864  # 增加到64MB
)


target_link_libraries(plot PRIVATE Qt${QT_VERSION_MAJOR}::Widgets Qt${QT_VERSION_MAJOR}::PrintSupport)

target_compile_definitions(plot PRIVATE PLOT_LIBRARY)
