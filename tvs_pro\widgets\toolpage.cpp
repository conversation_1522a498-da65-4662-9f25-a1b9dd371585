#include "toolpage.h"

#include <QFormLayout>
#include <QDebug>
#include <QHBoxLayout>
#include <QLabel>
#include <QFile>
#include <QPushButton>

ToolPage::ToolPage(QWidget *parent) :
    QWidget(parent),
    m_expanded(true)
{
    setAttribute(Qt::WA_StyledBackground);
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0, 0, 0, 0);
    mainLayout->setSpacing(0);

    // 创建标题
    m_button = new ToolBoxButton("");

    m_label = new ToolBoxIcon();
    // m_label->setFixedSize(20, 20);
    // m_label->setPixmap(QPixmap("./res/icon/down-arrow.png").scaled(m_label->size(), Qt::IgnoreAspectRatio, Qt::SmoothTransformation));

    m_label->setPixmap(QPixmap("./res/icon/widget/down-arrow.png"));
    m_label->setScaledContents(true);


    QHBoxLayout *layout = new QHBoxLayout(m_button);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->addStretch(1);
    layout->addWidget(m_label);

    m_content = new ToolBoxContent();
    m_content->setAttribute(Qt::WA_StyledBackground);

    m_layout = new QVBoxLayout(m_content);
    m_layout->setContentsMargins(0, 0, 0, 0);

    mainLayout->addWidget(m_button);
    mainLayout->addWidget(m_content, 1);

    connect(m_button, &QPushButton::clicked, this, &ToolPage::onPushButtonFoldClicked);
}


void ToolPage::addWidget(const QString &title, QWidget *widget)
{
    m_button->setText(title);
    m_layout->addWidget(widget);
}

void ToolPage::expand()
{
    m_content->show();
    m_expanded = true;

    // m_label->setPixmap(QPixmap("./res/icon/down-arrow.png").scaled(m_label->size(), Qt::IgnoreAspectRatio, Qt::SmoothTransformation));
    m_label->setPixmap(QPixmap("./res/icon/down-arrow.png"));
}

void ToolPage::collapse()
{
    m_content->hide();
    m_expanded = false;

    // m_label->setPixmap(QPixmap("./res/icon/left-arrow.png").scaled(m_label->size(), Qt::IgnoreAspectRatio, Qt::SmoothTransformation));
    m_label->setPixmap(QPixmap("./res/icon/widget/left-arrow.png"));
}

void ToolPage::onPushButtonFoldClicked()
{
    if (m_expanded)
    {
        collapse();
    }
    else
    {
        expand();
    }
}
