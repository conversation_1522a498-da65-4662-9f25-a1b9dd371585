#include "uitestwave.h"
#include "qevent.h"
#include "qmimedata.h"
#include "dlgexport.h"

#include <QGraphicsSceneDragDropEvent>
#include <QLabel>
#include <QToolBar>
#include <QVBoxLayout>

#include <app/settings.h>

#include <utils/actionmanager.h>

UiTestDrop::UiTestDrop(QWidget *parent)
    : QLabel{parent}
{
    setAcceptDrops(true);
}

void UiTestDrop::dragMoveEvent(QDragMoveEvent *event)
{
    event->accept();
}


void UiTestDrop::dragEnterEvent(QDragEnterEvent *event)
{
    event->accept();
    QLabel::dragEnterEvent(event);
}

void UiTestDrop::dropEvent(QDropEvent *event)
{
    if (event->mimeData()->hasFormat("Data/Source"))
    {
        //获取拖拽的数据
        QByteArray itemData = event->mimeData()->data("Data/Source");
        QDataStream dataStream(&itemData, QIODevice::ReadOnly);
        QString str;
        dataStream >>str;
        qDebug()<<str;

        auto newstr = text() + str + "\n";
        setText(newstr);
    }

    QLabel::dropEvent(event);
}

UiTestWave::UiTestWave(QWidget *parent)
    : QWidget{parent}
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_wave.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto toolBar = new QToolBar();
    mainLayout->addWidget(toolBar);

    toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);
    toolBar->addWidget(new QLabel("主波形"));

    QWidget* blank = new QWidget();
    blank->setObjectName("Blank");
    blank->setSizePolicy(QSizePolicy::Expanding, QSizePolicy::Preferred);
    toolBar->addWidget(blank);

    auto actRoll = toolBar->addAction( "继续滚屏");
    actRoll->setCheckable(true);

    toolBar->addSeparator();
    auto actList = toolBar->addAction( "波形列表");

    connect(actList, &QAction::triggered,[=]
    {
        ActionManager::instance().executeCommand("show_porp_channel");
    });

    toolBar->addSeparator();

    toolBar->addAction(QIcon("./res/icon/widget/curve1.png"), "");
    toolBar->addAction(QIcon("./res/icon/widget/curve2.png"), "");
    toolBar->addAction(QIcon("./res/icon/widget/curve3.png"), "");
    toolBar->addAction(QIcon("./res/icon/widget/curve4.png"), "");
    auto actExport = toolBar->addAction(QIcon("./res/icon/widget/export.png"), "");
    toolBar->addSeparator();
    toolBar->addAction(QIcon("./res/icon/widget/scale.png"), "");

    connect(actExport, &QAction::triggered,[=]
    {
        DlgExport dlgExport;
        dlgExport.setWindowTitle("数据导出");
        dlgExport.exec();
    });



    auto chart = new UiTestDrop();
    chart->setStyleSheet("background-color: black; color:white");
    mainLayout->addWidget(chart, 1);
}

