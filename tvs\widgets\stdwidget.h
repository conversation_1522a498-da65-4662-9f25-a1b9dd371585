#ifndef STDWIDGET_H
#define STDWIDGET_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 基础组件的标准样式扩展
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include "qtabbar.h"
#include <QLabel>
#include <QPushButton>
#include <QWidget>




class ButtonPrimary : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};

class ButtonSuccess : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};

class ButtonInfo : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};

class ButtonWarning : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};

class ButtonDanger : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};


class IconButton : public QPushButton
{
    Q_OBJECT
public:
    using QPushButton::QPushButton; // 继承父类的构造函数
};

class LabelIcon : public QLabel
{
    Q_OBJECT
public:
    using QLabel::QLabel; // 继承父类的构造函数
};

class LabelH1 : public QLabel
{
    Q_OBJECT
public:
    using QLabel::QLabel; // 继承父类的构造函数
};


class LabelH2 : public QLabel
{
    Q_OBJECT
public:
    using QLabel::QLabel; // 继承父类的构造函数
};


class LabelH3 : public QLabel
{
    Q_OBJECT
public:
    using QLabel::QLabel; // 继承父类的构造函数
};


class LabelTitle : public QLabel
{
    Q_OBJECT
public:
    using QLabel::QLabel; // 继承父类的构造函数
};


class LabelInfo : public QLabel
{
    Q_OBJECT
public:
    using QLabel::QLabel; // 继承父类的构造函数
};


class GroupButton : public QTabBar
{
    Q_OBJECT
public:
    GroupButton(QWidget *parent = nullptr);
};





#endif // STDWIDGET_H
