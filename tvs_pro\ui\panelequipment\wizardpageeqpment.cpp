
#include "widgets/stdwidget.h"
#include "widgets/flowlayout.h"
#include "widgets/toolbox.h"

#include "wizardpageeqpment.h"

#include <MainWindow.h>
#include <QButtonGroup>
#include <QFormLayout>
#include <QLabel>
#include <QLineEdit>
#include <QMessageBox>
#include <QPushButton>
#include <QRegularExpressionValidator>
#include <QToolBox>
#include <QVBoxLayout>

#include <app/settings.h>


EquipmentItem::EquipmentItem(const QString &title, const QString &info, const QString &picFile, QWidget *parent)
    : QPushButton{parent}
{
    // setFixedSize(150, 150);
    setCheckable(true);
    auto layout = new QVBoxLayout();
    setLayout(layout);
    // layout->setContentsMargins(0,0,0,0);

    auto pic = new LabelIcon();
    pic->setPixmap(QPixmap(picFile));
    pic->setScaledContents(true);
    pic->setAlignment(Qt::AlignCenter);

    layout->addWidget(pic, 1);

    auto wtitle = new LabelTitle(title);
    auto winfo = new LabelInfo(info);

    wtitle->setAlignment(Qt::AlignCenter);
    winfo->setAlignment(Qt::AlignCenter);

    layout->addWidget(wtitle);
    layout->addWidget(winfo);
}


WizardPageEquipment::WizardPageEquipment()
{
    setTitle("选择设备类型");

    QVBoxLayout *layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);

    ToolBox * toolBox = new ToolBox();
    layout->addWidget(toolBox);

    m_buttonGroup= new QButtonGroup();

    //进行信号和槽的绑定
    // connect(buttonGroup, SIGNAL(buttonClicked(int)),
    //         this, SLOT(onGroupButtonClicked(int)));

    connect(m_buttonGroup, &QButtonGroup::idClicked, [this](int id)
    {
        qDebug() << "单击id为 " << id;
        emit equipmentChanged(id);
    });


    registerField("equipment", this, "equipment", SIGNAL(equipmentChanged));

    // mCanFwdList = new QListWidget;
    // mCanFwdList->setIconSize(QSize(50, 50));
    // mCanFwdList->setWordWrap(true);
    // mCanFwdList->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    // registerField("CanFwd", this, "canFwd", SIGNAL(canFwdChanged));
    // connect(mCanFwdList, SIGNAL(currentRowChanged(int)),
    //         this, SIGNAL(canFwdChanged()));

    auto gonglv = new EquipmentFlow();
    toolBox->addItem(gonglv, "功率测试");
    gonglv->setAttribute(Qt::WA_StyledBackground);
    FlowLayout *flowLayout = new FlowLayout(gonglv, 0, 0, 0);
    flowLayout->setContentsMargins(0,0,0,0);


    QStringList equipmentList =  {"8730C", "8930H", "8962A1"};
    for (int i = 0; i < equipmentList.length(); ++i)
    {
        auto name = equipmentList[i];
        auto yiqi = new EquipmentItem(name, "---", QString("./res/equipment/%1.png").arg(name));
        flowLayout->addWidget(yiqi);
        m_buttonGroup->addButton(yiqi, i);

        if(0 == i)
        {
            yiqi->setChecked(true);
        }
    }


    auto dianyuan = new EquipmentFlow();
    toolBox->addItem(dianyuan, "电源");
    dianyuan->setAttribute(Qt::WA_StyledBackground);
    FlowLayout *flowLayout_dianyuan = new FlowLayout(dianyuan, 0, 0, 0);
    flowLayout_dianyuan->setContentsMargins(0,0,0,0);

    for (int i = 0; i < 4; ++i)
    {
        auto name = "device"+QString::number(i+1);
        auto yiqi = new EquipmentItem(name, "---", QString("./res/equipment/%1.png").arg(name));
        flowLayout_dianyuan->addWidget(yiqi);
        m_buttonGroup->addButton(yiqi, i+equipmentList.length());
    }
}

int WizardPageEquipment::getEquipment()
{
    if(m_buttonGroup)
    {
        return m_buttonGroup->checkedId();
    }

    return -1;
}


WizardPageEquipmentConnect::WizardPageEquipmentConnect()
{
    setTitle("连接设备");



    QFormLayout *formLayout = new QFormLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_equipment_wizard_connect.margins");
    if(margins.size() == 4)
    {
        formLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_equipment_wizard_connect.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_equipment_wizard_connect.hspacing");

    formLayout->setVerticalSpacing(vspacing);
    formLayout->setHorizontalSpacing(hspacing);

    m_name = new QLineEdit("PA");
    m_type = new QLineEdit("测试");
    m_communicate = new QLineEdit("测试");
    m_port = new QLineEdit("88");
    m_ip = new QLineEdit("127.0.0.1");
    m_ip->setClearButtonEnabled(true);
    m_ip->setValidator(new QRegularExpressionValidator(QRegularExpression("\\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\b")));
    m_ip->setInputMask("***************; ");


    formLayout->addRow("设备别名", m_name);
    formLayout->addRow("设备类型", m_type);
    formLayout->addRow("通讯方式", m_communicate);
    formLayout->addRow("端口", m_port);
    formLayout->addRow("IP", m_ip);

    registerField("name", m_name);
    registerField("type", m_type);
    registerField("communicate", m_communicate);
    registerField("port", m_port);
    registerField("ip", m_ip);
}

bool WizardPageEquipmentConnect::validatePage()
{
    QString name = field("name").toString();
    if (name.isEmpty())
    {
        QMessageBox::warning(this, "青智", "请输入设备别名.");
        return false;
    }


    QString ip = field("ip").toString();
    if (ip.isEmpty())
    {
        QMessageBox::warning(this, "青智", "请输入设备ip.");
        return false;
    }

    if(MainWindow::instance->m_doc.hasEquipmentIp(ip))
    {
        QMessageBox::warning(this, "青智", "设备 IP 不能重复.");
        return false;
    }

    return true;
}

void WizardPageEquipmentConnect::initializePage()
{
    auto equipment = wizard()->field("equipment").toInt();
    qDebug() << equipment;
}
