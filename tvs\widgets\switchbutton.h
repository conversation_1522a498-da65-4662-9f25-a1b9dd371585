#ifndef SWITCHBUTTON_H
#define SWITCHBUTTON_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 滑动开关控件
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QPushButton>

class QVariantAnimation;

class SwitchButton : public QPushButton
{
    Q_OBJECT
public:
    explicit SwitchButton(QWidget *parent = 0);

protected:
    void paintEvent(QPaintEvent *);

private:
    void onToggled(bool checked);
private:
    qreal progress = 0;
    QVariantAnimation *animation = nullptr;
};


#endif // SWITCHBUTTON_H
