#include "uiequipment.h"
#include "qsplitter.h"
#include "uistatus.h"
#include "ui/equipment/uidata.h"
#include "ui/equipment/uiequipwave.h"

#include <MainWindow.h>
#include <QLabel>
#include <QVBoxLayout>

UiEquipment::UiEquipment(const QString& id, QWidget *parent)
    : QWidget{parent}, m_id(id)
{
    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(0,0,0,0);

    // 分割窗口
    QSplitter * splitter = new QSplitter(Qt::Vertical, this);
    splitter->setContentsMargins(0,0,0,0);

    mainLayout->addWidget(splitter);

    m_uiWave = new UiEquipWave();
    m_uiData = new UiData();

    splitter->addWidget(m_uiWave);
    splitter->addWidget(m_uiData);

    int h = splitter->size().height();
    splitter->setSizes( { h/2, h/2 } );

    // 不允许折叠
    splitter->setCollapsible(0, false);
    splitter->setCollapsible(1, false);

    m_uiStatus = new UiStatus();
    mainLayout->addWidget(m_uiStatus);

    auto lb1 = new QLabel("开始时间: 00:00:00");
    lb1->setAlignment(Qt::AlignCenter);
    m_uiStatus->m_layout->addWidget(lb1, 1);

    auto line = new QFrame();
    line->setFrameShape(QFrame::VLine);

    m_uiStatus->m_layout->addWidget(line);


    auto txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/warn.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">PA设备异常告警:已断开连接</td>
</table>
)";

    m_info = new QLabel(txt);
    m_info->setAlignment(Qt::AlignCenter);
    m_uiStatus->m_layout->addWidget(m_info);

    connect(MainWindow::instance, &MainWindow::equipmentConnectChanged, [this](QString id, bool bconnect){
        if( m_id == id )
        {
            QString txt = R"(
<table>
    <td align="left" valign="middle">
        <img src="./res/icon/status/%1.png" width="16" height="16" style="vertical-align:middle">
    </td>
    <td align="left" valign="middle">%2</td>
</table>
)";

            if(bconnect)
            {
                txt = txt.arg("success", "PA设备已连接");
            }
            else
            {
                txt = txt.arg("warn", "PA设备异常告警:已断开连接");
            }

            m_info->setText(txt);
        }
    });
}

bool UiEquipment::isUiDataVisiable() const
{
    return m_uiData->isVisible();
}

void UiEquipment::setUiDataVisiable(bool visible)
{
    m_uiData->setVisible(visible);
}

void UiEquipment::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);
    // 设置最小尺寸
    m_uiWave->setMinimumSize(100, event->size().height()/5);
    m_uiData->setMinimumSize(100, event->size().height()/5);
}
