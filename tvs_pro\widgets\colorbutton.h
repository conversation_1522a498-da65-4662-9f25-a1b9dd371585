#ifndef COLORBUTTON_H
#define COLORBUTTON_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 颜色选择按钮
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QPushButton>
class QLabel;

class ColorButton : public QPushButton
{
    Q_OBJECT

public:
    explicit ColorButton(QWidget* parent=nullptr, QColor color = Qt::black);

    void	setColor( const QColor& color );
    void    allowSetAlpha( bool allow );

    QColor	color() const;

Q_SIGNALS:
    void onColorChanged(const QColor &color);

private slots:
    void	btnClicked();

private:
    QColor	m_selectedColor;
    bool    m_allowSetAlpha;
    QLabel*  m_label;
};

#endif // COLORBUTTON_H
