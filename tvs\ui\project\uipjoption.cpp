#include "uipjoption.h"
#include "widgets/stdwidget.h"


#include <QComboBox>
#include <QFormLayout>
#include <QLineEdit>

#include <app/settings.h>

UiPjOption::UiPjOption(QWidget *parent)
    : QWidget{parent}
{
    auto *label = new LabelH2("信息");

    QVBoxLayout *mainLayout = new QVBoxLayout(this);
    mainLayout->addWidget(label);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_pj_option.margins");
    if(margins.size() == 4)
    {
        mainLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_pj_option.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_pj_option.hspacing");

    QFormLayout *formLayout = new QFormLayout();

    formLayout->setVerticalSpacing(vspacing);
    formLayout->setHorizontalSpacing(hspacing);

    mainLayout->addLayout(formLayout);
    mainLayout->addStretch();

    // formLayout->setFieldGrowthPolicy(QFormLayout::FieldsStayAtSizeHint);

    m_color_options = new QComboBox();
    QStringList list_clr = {"彩色","灰度","黑白","反色"};
    m_color_options->addItems(list_clr);
    // m_color_options->setMinimumWidth(150);

    m_tmp_path = new QLineEdit();
    // m_tmp_path->setMinimumWidth(150);

    m_copy_options = new QComboBox();
    QStringList list_copy = {"保存工程文件时询问我","不复制导入的文件","只复制特定大小以下的文件","复制所有导入的文件"};
    m_copy_options->addItems(list_copy);
    // m_copy_options->setMinimumWidth(150);


    m_file_size = new QLineEdit();
    // m_file_size->setMinimumWidth(150);


    formLayout->addRow("截屏图片颜色模式:", m_color_options);
    formLayout->addRow("临时文件存储路径:", m_tmp_path);
    formLayout->addRow("导入的文件是否复制到工程:", m_copy_options);
    formLayout->addRow("文件大小:", m_file_size);


    // auto lineedit = new QLineEdit();
    // lineedit->setMaximumWidth(200);
    // layout->addRow("作者", lineedit);

}
