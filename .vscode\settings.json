{"files.associations": {"*.erd": "json", "*.vuerd": "json", "*.qss": "css", "*.cpp": "cpp", "new": "cpp", "atomic": "cpp", "memory": "cpp", "deque": "cpp", "list": "cpp", "map": "cpp", "unordered_map": "cpp", "vector": "cpp", "xhash": "cpp", "xstring": "cpp", "xtree": "cpp", "string": "cpp", "qscrollbar": "cpp", "qvboxlayout": "cpp", "algorithm": "c", "any": "c", "array": "c", "bit": "c", "cctype": "c", "charconv": "c", "chrono": "c", "clocale": "c", "cmath": "c", "codecvt": "c", "compare": "c", "concepts": "c", "condition_variable": "c", "cstdarg": "c", "cstddef": "c", "cstdint": "c", "cstdio": "c", "cstdlib": "c", "cstring": "c", "ctime": "c", "cwchar": "c", "cwctype": "c", "exception": "c", "filesystem": "c", "format": "c", "forward_list": "c", "fstream": "c", "functional": "c", "future": "c", "initializer_list": "c", "iomanip": "c", "ios": "c", "iosfwd": "c", "iostream": "c", "istream": "c", "iterator": "c", "limits": "c", "locale": "c", "mutex": "c", "numbers": "c", "numeric": "c", "optional": "c", "ostream": "c", "queue": "c", "random": "c", "ratio": "c", "semaphore": "c", "set": "c", "source_location": "c", "sstream": "c", "stack": "c", "stdexcept": "c", "stop_token": "c", "streambuf": "c", "system_error": "c", "thread": "c", "tuple": "c", "type_traits": "c", "typeinfo": "c", "utility": "c", "valarray": "c", "variant": "c", "xfacet": "c", "xiosbase": "c", "xlocale": "c", "xlocbuf": "c", "xlocinfo": "c", "xlocmes": "c", "xlocmon": "c", "xlocnum": "c", "xloctime": "c", "xmemory": "c", "xstddef": "c", "xtr1common": "c", "xutility": "c", "memory_resource": "c", "string_view": "c", "cinttypes": "c", "qrandomgenerator": "cpp", "qtimer": "cpp", "kiss_fft.h": "c", "qhboxlayout": "cpp"}}