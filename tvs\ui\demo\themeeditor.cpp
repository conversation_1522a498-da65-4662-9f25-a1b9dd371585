#include "themeeditor.h"
#include "widgets/toast.h"
#include "widgets/codeeditor.h"

#include <QFile>
#include <QMessageBox>
#include <QPushButton>
#include <QVBoxLayout>

ThemeEditor::ThemeEditor(const QString &filePath, std::function<void(QString)> previewCallback, QWidget *parent)
    : QWidget(parent), m_filePath(filePath), m_previewCallback(previewCallback)

{
    // 创建垂直布局
    QVBoxLayout *mainLayout = new QVBoxLayout();

    // 创建水平布局用于放置按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout;

    // 添加一个占位符，将按钮推到右侧
    buttonLayout->addStretch();

    // 创建保存按钮
    QPushButton *saveButton = new QPushButton("保存");
    saveButton->setMinimumWidth(80);
    buttonLayout->addWidget(saveButton);


    // 创建预览按钮
    QPushButton *previewButton = new QPushButton("预览");
    previewButton->setMinimumWidth(80);
    buttonLayout->addWidget(previewButton);

    connect(saveButton, &QPushButton::clicked,[&]
    {
        //内容保存到路径文件
        QFile file(m_filePath);

        //以文本方式打开
        if( file.open(QFile::WriteOnly | QFile::Truncate) )
        {
            QTextStream out(&file); //IO设备对象的地址对其进行初始化
            out.setEncoding(QStringConverter::Utf8);
            out.setGenerateByteOrderMark(false);
            out << m_codeEdit->toPlainText();
            out.flush();

            file.close();
            Toast::showMsg("保存成功");
        }
        else
        {
            Toast::showMsg("保存失败");
        }
    });

    connect(previewButton,&QPushButton::clicked,[&]
    {
        m_previewCallback( m_codeEdit->toPlainText() );
    });

    // 创建文本编辑框
    m_codeEdit = new CodeEditor();

    loadFileContent();
    // 将按钮布局和文本编辑框添加到主布局中
    mainLayout->addLayout(buttonLayout);
    mainLayout->addWidget(m_codeEdit);

    // 设置主窗口布局
    this->setLayout(mainLayout);
}

void ThemeEditor::loadFileContent()
{
    QFile file(m_filePath);
    if (file.open(QIODevice::ReadOnly | QIODevice::Text))
    {
        QTextStream in(&file);
        m_codeEdit->setPlainText(in.readAll());
        file.close();
    }
}
