#include "plot_widget.h"

#include "qcpl/qcpl_format.h"
#include "qcpl/qcpl_io_json.h"
#include "qcpl/qcpl_utils.h"

#include "helpers/OriDialogs.h"
#include "tools/OriPetname.h"
#include "core/Graph.h"
#include "core/DataSources.h"
#include "tools/OriMessageBus.h"
#include "qcpl/qcpl_axis.h"

#include "widgets/RangeEditor.h"
#include "helpers/OriDialogs.h"
#include "helpers/OriLayouts.h"

using Ori::MessageBus;

PlotItem::~PlotItem()
{
}

static QIcon makeGraphIcon(QColor color)
{
    int H, S, L;
    color.getHsl(&H, &S, &L);
    QColor backColor = QColor::fromHsl(H, int(float(S) * 0.8f), int(float(L) * 1.2f));
    QColor borderColor = QColor::fromHsl(H, int(float(S) * 0.5f), L);

    QPixmap px(16, 16);
    px.fill(Qt::transparent);

    QPainter p(&px);
    p.setRenderHint(QPainter::Antialiasing, true);

    QPen borderPen(borderColor);
    borderPen.setWidthF(1.5);
    p.setPen(borderPen);

    p.setBrush(backColor);
    p.drawEllipse(px.rect().adjusted(1, 1, -1, -1));

    // TODO draw gradient gloss

    return QIcon(px);
}

//------------------------------------------------------------------------------
//                             DaqDataSource
//------------------------------------------------------------------------------

// Open another (or the same, if you wish) source file
DataSource::ConfigResult DaqDataSource::configure()
{

    return ConfigResult(true);
}

GraphResult DaqDataSource::read()
{

    // _data = {reader.xs, reader.ys};
    return GraphResult::ok(_data);
}

QString DaqDataSource::makeTitle() const
{
    QString title;

    return title;
}

QString DaqDataSource::displayStr() const
{
    return QStringLiteral("");
}

PlotWidget::PlotWidget(QWidget *parent) : QWidget(parent)
{
    setWindowTitle("Zoomable Plot");

    _plot = std::make_unique<QCPL::Plot>(QCPL::PlotOptions({replaceDefaultAxes : false}), this);

    // _plot->formatSaver.reset(new QCPL::FormatStorageIni);

    // _plot->legend->setSelectableParts(QCPLegend::spLegendBox);
    _plot->legend->setSelectedParts(QCPLegend::spNone);

    _plot->legend->setRowSpacing(3);
    _plot->legend->setColumnSpacing(8);
    _plot->legend->setMargins(QMargins(7, 5, 7, 4));
    _plot->legend->setAntialiased(false);
    _plot->legend->setIconSize(32, 18);

    _plot->legend->setIconTextPadding(7);
    _plot->legend->setBorderPen(QPen(Qt::lightGray, 0));
    _plot->legend->setSelectedBorderPen(QPen(Qt::blue, 2));
    _plot->legend->setIconBorderPen(Qt::NoPen);
    _plot->legend->setSelectedIconBorderPen(QPen(Qt::blue, 2));
    _plot->legend->setBrush(Qt::darkGray);
    _plot->legend->setSelectedBrush(Qt::darkGray);
    _plot->legend->setTextColor(Qt::white);
    _plot->legend->setSelectedTextColor(Qt::blue);

    auto layout = new QVBoxLayout(this);
    layout->setContentsMargins(0, 0, 0, 0);
    layout->setSpacing(0);
    layout->addWidget(_plot.get());

    // auto m = menuBar()->addMenu("Data");
    // m->addAction("Add random line graph", this, &PlotWidget::addRandomSampleLine);
    // m->addAction("Add random coormap graph", this, &PlotWidget::addRandomSampleColormap);
    // m->addAction("Save plot format...", this, &PlotWidget::savePlotFormat);
    // m->addAction("Load plot format...", this, &PlotWidget::loadPlotFormat);

    // m = menuBar()->addMenu("Limits");
    // m->addAction("Auto", this, [this]{ _plot->autolimits(); });
    // m->addAction("Auto X", this, [this]{ _plot->autolimitsX(); });
    // m->addAction("Auto Y", this, [this]{ _plot->autolimitsY(); });
    // m->addAction("Limits...", this, [this]{ _plot->limitsDlgXY(); });
    // m->addAction("Limits X...", this, [this]{ _plot->limitsDlgX(); });
    // m->addAction("Limits Y...", this, [this]{ _plot->limitsDlgY(); });

    // m = menuBar()->addMenu("Format");
    // m->addAction("Plot format...", this, [this]{ QCPL::plotFormatDlg(_plot.get()); });
    // m->addAction("Legend format...", this, [this]{ _plot->legendFormatDlg(); });
    // m->addSeparator();
    // m->addAction("Title format...", this, [this]{ _plot->titleFormatDlg(); });
    // m->addAction("Title text...", this, [this]{ _plot->titleTextDlg(); });
    // m->addSeparator();
    // m->addAction("X-axis format...", this, [this]{ _plot->axisFormatDlgX(); });
    // m->addAction("X-axis text...", this, [this]{ _plot->axisTextDlgX(); });
    // m->addSeparator();
    // m->addAction("Y-axis format...", this, [this]{ _plot->axisFormatDlgY(); });
    // m->addAction("Y-axis text...", this, [this]{ _plot->axisTextDlgY(); });

    // Example of usage of text variables
    // Variable getters provide values for variables.
    // They are called when one calls `format()` on partucular TextFormatter object
    auto getWndW = [this]
    { return QString::number(width()); };
    auto getWndH = [this]
    { return QString::number(height()); };
    _plot->addTextVarT("{text_var}", "Text var 3", []
                       { return "Hallo World"; });
    _plot->addTextVarT("{unit_1}", "Unit of measurement 1", []
                       { return "mm"; });
    _plot->addTextVarT("{unit_2}", "Unit of measurement 2", []
                       { return "kg"; });
    _plot->addTextVarT("{wndW}", "Main window width", getWndW);
    _plot->addTextVarT("{wndH}", "Main window height", getWndH);

    _plot->addTextVarX("{some_text}", "Text var 1", []
                       { return "Galenium overloader"; });
    _plot->addTextVarX("{unit_1}", "Unit of measurement 1", []
                       { return "mm"; });
    _plot->addTextVarX("{unit_2}", "Unit of measurement 2", []
                       { return "kg"; });
    _plot->addTextVarX("{wndW}", "Main window width", getWndW);
    _plot->addTextVarX("{wndH}", "Main window height", getWndH);

    _plot->addTextVarY("{text_var}", "Text var 2", []
                       { return "Lorem ipsum"; });
    _plot->addTextVarY("{unit_1}", "Unit of measurement 1", []
                       { return "mm"; });
    _plot->addTextVarY("{unit_2}", "Unit of measurement 2", []
                       { return "kg"; });
    _plot->addTextVarY("{wndW}", "Main window width", getWndW);
    _plot->addTextVarY("{wndH}", "Main window height", getWndH);

    _plot->setDefaultTextT("Plot title");
    _plot->setDefaultTextX("Axis X title");
    _plot->setDefaultTextY("Axis Y title");

    _plot->menuLegend = new QMenu(this);
    _plot->menuLegend->addAction("Format...", this, [this]
                                 { _plot->legendFormatDlg(); });
    _plot->menuLegend->addAction("Copy format", this, [this]()
                                 { QCPL::copyLegendFormat(_plot->legend); });
    _plot->menuLegend->addAction("Paste format", this, &PlotWidget::pasteLegendFormat);
    _plot->menuLegend->addAction("Hide", this, [this]()
                                 {
        _plot->legend->setVisible(false);
        _plot->replot(); });
    _plot->menuTitle = new QMenu(this);
    _plot->menuTitle->addAction("Format...", this, [this]
                                { _plot->titleFormatDlg(); });
    _plot->menuTitle->addAction("Text...", this, [this]
                                { _plot->titleTextDlg(); });
    _plot->menuTitle->addAction("Copy format", this, [this]()
                                { QCPL::copyTitleFormat(_plot->title()); });
    _plot->menuTitle->addAction("Paste format", this, &PlotWidget::pasteTitleFormat);
    _plot->menuTitle->addAction("Hide", this, [this]()
                                {
        _plot->title()->setVisible(false);
        _plot->updateTitleVisibility();
        _plot->replot(); });
    _plot->menuAxisX = new QMenu(this);
    _plot->menuAxisX->addAction("Format...", this, [this]
                                { _plot->axisFormatDlgX(); });
    _plot->menuAxisX->addAction("Text...", this, [this]
                                { _plot->axisTextDlgX(); });
    _plot->menuAxisX->addAction("Copy format", this, [this]()
                                { QCPL::copyAxisFormat(_plot->xAxis); });
    _plot->menuAxisX->addAction("Paste format", this, [this]()
                                { pasteAxisFormat(_plot->xAxis); });
    _plot->menuAxisY = new QMenu(this);
    _plot->menuAxisY->addAction("Format...", this, [this]
                                { _plot->axisFormatDlgY(); });
    _plot->menuAxisY->addAction("Text...", this, [this]
                                { _plot->axisTextDlgY(); });
    _plot->menuAxisY->addAction("Copy format", this, [this]()
                                { QCPL::copyAxisFormat(_plot->yAxis); });
    _plot->menuAxisY->addAction("Paste format", this, [this]()
                                { pasteAxisFormat(_plot->yAxis); });

    _plot->menuGraph = new QMenu(this);
    _plot->menuGraph->addAction(QIcon(":/toolbar/graph_title"), tr("Title..."), this, &PlotWidget::renameGraph);
    _plot->menuGraph->addAction(QIcon(":/toolbar/graph_format"), tr("Format..."), this, &PlotWidget::formatGraph);
    _plot->menuGraph->addAction(tr("Change Axes..."), this, &PlotWidget::changeGraphAxes);
    _plot->menuGraph->addSeparator();
    _plot->menuGraph->addAction(QIcon(":/toolbar/copy_fmt"), tr("Copy Format"), this, &PlotWidget::copyGraphFormat);
    _plot->menuGraph->addAction(QIcon(":/toolbar/paste_fmt"), tr("Paste Format"), this, &PlotWidget::pasteGraphFormat);
    _plot->menuGraph->addSeparator();
    _plot->menuGraph->addAction(QIcon(":/toolbar/graph_delete"), tr("Delete"), this, &PlotWidget::deleteGraph);

    _plot->menuPlot = new QMenu(this);
    auto m = _plot->menuPlot->addMenu("Data");
    m->addAction("Add random line graph", this, &PlotWidget::addRandomSampleLine);
    m->addAction("Add random coormap graph", this, &PlotWidget::addRandomSampleColormap);
    m->addAction("Save plot format...", this, &PlotWidget::savePlotFormat);
    m->addAction("Load plot format...", this, &PlotWidget::loadPlotFormat);

    m = _plot->menuPlot->addMenu("Limits");
    m->addAction("Auto", this, [this]
                 { _plot->autolimits(); });
    m->addAction("Auto X", this, [this]
                 { _plot->autolimitsX(); });
    m->addAction("Auto Y", this, [this]
                 { _plot->autolimitsY(); });
    m->addAction("Limits...", this, [this]
                 { _plot->limitsDlgXY(); });
    m->addAction("Limits X...", this, [this]
                 { _plot->limitsDlgX(); });
    m->addAction("Limits Y...", this, [this]
                 { _plot->limitsDlgY(); });

    m = _plot->menuPlot->addMenu("Format");
    m->addAction("Plot format...", this, [this]
                 { QCPL::plotFormatDlg(_plot.get()); });
    m->addAction("Legend format...", this, [this]
                 { _plot->legendFormatDlg(); });
    m->addSeparator();
    m->addAction("Title format...", this, [this]
                 { _plot->titleFormatDlg(); });
    m->addAction("Title text...", this, [this]
                 { _plot->titleTextDlg(); });
    m->addSeparator();
    m->addAction("X-axis format...", this, [this]
                 { _plot->axisFormatDlgX(); });
    m->addAction("X-axis text...", this, [this]
                 { _plot->axisTextDlgX(); });
    m->addSeparator();
    m->addAction("Y-axis format...", this, [this]
                 { _plot->axisFormatDlgY(); });
    m->addAction("Y-axis text...", this, [this]
                 { _plot->axisTextDlgY(); });

    // addRandomSampleLine();
    // _plot->autolimits();

    // _plot->autoZoomLimits();

    resize(800, 600);
}

void PlotWidget::markModified(const QString &reason)
{
    qDebug() << "Modified" << reason;
}

Graph *PlotWidget::selectedGraph(bool warn) const
{
    if (_items.size() == 1)
        return _items.front()->graph.get();
    auto lines = _plot->selectedGraphs();
    if (lines.isEmpty())
    {
        // if (warn)
        //     PopupMessage::warning(tr("Should select a graph"));
        return nullptr;
    }
    auto item = itemForLine(lines.first());
    return item ? item->graph.get() : nullptr;
}

QVector<Graph *> PlotWidget::selectedGraphs(bool warn) const
{
    if (_items.size() == 1)
        return {_items.front()->graph.get()};
    QVector<Graph *> res;
    foreach (auto line, _plot->selectedGraphs())
        if (auto item = itemForLine(line); item)
            res << item->graph.get();
    // if (res.isEmpty() and warn)
    //     PopupMessage::warning(qApp->tr("Should select a graph"));
    return res;
}

QCPGraph *PlotWidget::selectedGraphLine(bool warn) const
{
    if (_items.size() == 1)
        return _items.front()->line;
    auto lines = _plot->selectedGraphs();
    if (lines.isEmpty())
    {
        // if (warn)
        //     PopupMessage::warning(tr("Should select a graph"));
        return nullptr;
    }
    return lines.first();
}

PlotItem *PlotWidget::itemForLine(QCPGraph *line) const
{
    for (const auto &item : _items)
        if (item->line == line)
            return item.get();
    return nullptr;
}

PlotItem *PlotWidget::itemForGraph(Graph *graph) const
{
    for (const auto &item : _items)
        if (item->graph.get() == graph)
            return item.get();
    return nullptr;
}

bool PlotWidget::updateGraph(Graph *graph)
{
    auto item = itemForGraph(graph);
    if (!item)
        return false;

    item->line->setName(graph->title());
    _plot->updateGraph(item->line, {graph->data().xs, graph->data().ys});
    return true;
}

bool PlotWidget::addGraphData(Graph *graph, const QVector<double> &keys, const QVector<double> &values, bool alreadySorted, int maxDataPoints)
{
    auto item = itemForGraph(graph);
    if (!item)
        return false;

    item->line->addData(keys, values, alreadySorted, maxDataPoints);

    // bool hasRange = false;
    // auto range = item->line->getKeyRange(hasRange, QCP::sdBoth);
    // _plot->xAxis->setRange(range.lower, range.upper);
    _plot->autoZoomLimits();

    _plot->replot();
    return true;
    // todo
}

void PlotWidget::renameGraph()
{
    auto graph = selectedGraph();
    if (!graph)
        return;

    QString newTitle = Ori::Dlg::inputText(tr("Graph title:"), graph->title());
    if (newTitle.isEmpty())
        return;
    graph->setTitle(newTitle);
    markModified("PlotWidget::renameGraph");
    if (auto item = itemForGraph(graph); item)
    {
        item->line->setName(graph->title());
        _plot->replot();
    }
    MessageBus::send(MSG_GRAPH_RENAMED, {{"id", graph->id()}});
}

void PlotWidget::formatGraph()
{
    auto line = selectedGraphLine();
    if (!line)
        return;

    QCPL::GraphFormatDlgProps props;
    props.title = tr("Format %1").arg(line->name());
    if (QCPL::graphFormatDlg(line, props))
        markModified("PlotWidget::formatGraph");
}

void PlotWidget::changeGraphAxes()
{
    auto graph = selectedGraphLine();
    if (!graph)
        return;

    bool changed = false;
    auto res = QCPL::chooseAxes(_plot.get(), {graph->keyAxis(), graph->valueAxis()});
    if (res.x and res.x != graph->keyAxis())
    {
        changed = true;
        res.x->setVisible(true);
        graph->setKeyAxis(res.x);
        if (autolitmAfterAxesChanged)
            _plot->autolimits(res.x, false);
    }
    if (res.y and res.y != graph->valueAxis())
    {
        changed = true;
        res.y->setVisible(true);
        graph->setValueAxis(res.y);
        if (autolitmAfterAxesChanged)
            _plot->autolimits(res.y, false);
    }
    if (changed)
    {
        _plot->replot();
        markModified("PlotWidget::changeGraphAxes");
    }
}

void PlotWidget::copyGraphFormat()
{
    auto line = selectedGraphLine();
    if (line)
        QCPL::copyGraphFormat(line);
}

void PlotWidget::pasteGraphFormat()
{
    auto line = selectedGraphLine();
    if (!line)
        return;

    auto err = QCPL::pasteGraphFormat(line);
    if (err.isEmpty())
    {
        _plot->replot();
        markModified("PlotWidget::pasteGraphFormat");
    }
    // else PopupMessage::warning(err);
}

void PlotWidget::deleteGraph()
{
    auto graphs = selectedGraphs();
    if (graphs.empty())
        return;

    QStringList msg;
    msg << tr("These graphs will be deleted:") << "<br><br>";
    for (auto g : graphs)
        msg << "<b>" << g->title() << "</b><br>";
    msg << "<br>" << tr("Confirm?");

    if (Ori::Dlg::yes(msg.join("")))
        deleteGraphs(graphs);
}

void PlotWidget::deleteGraphs(const QVector<Graph *> &graphs)
{
    for (auto g : graphs)
    {
        auto item = itemForGraph(g);
        if (!item)
            continue;
        _plot->removeGraphAndAtts(item->line);

        _items.remove_if([item](const std::unique_ptr<PlotItem> &ptr)
                         { return ptr.get() == item; });
    }
    _plot->replot();
    markModified("PlotWidget::deleteGraphs");
    MessageBus::send(MSG_GRAPH_DELETED);
}

PlotWidget::~PlotWidget()
{
}

void PlotWidget::addGraph(Graph *g)
{
    auto item = std::make_unique<PlotItem>();
    item->graph.reset(g);

    item->line = _plot->makeNewGraph(g->title(), {g->data().xs, g->data().ys}, false);
    connect(item->line, SIGNAL(selectionChanged(bool)), this, SLOT(graphLineSelected(bool)));

    g->setColor(item->line->pen().color());
    g->setIcon(makeGraphIcon(g->color()));

    if (autolimitAfterGraphCreated)
    {
        _plot->autolimits(item->line->keyAxis(), false);
        _plot->autolimits(item->line->valueAxis(), false);
    }

    if (selectNewGraph)
        selectGraphLine(item->line, false);

    _plot->autoZoomLimits();

    _plot->replot();
    _items.push_back(std::move(item));
}

void PlotWidget::selectGraphLine(QCPGraph *line, bool replot)
{
    _plot->deselectAll();
    line->setSelection(QCPDataSelection(line->data()->dataRange()));
    if (replot)
        _plot->replot();
}

void PlotWidget::graphLineSelected(bool selected)
{
    auto line = qobject_cast<QCPGraph *>(sender());
    if (!line || !selected)
        return;
    auto item = itemForLine(line);
    if (item)
        emit graphSelected(item->graph.get());
}

// void PlotWidget::addRandomSampleLine()
// {
//     // _plot->makeNewGraph(OriPetname::make(), QCPL::makeRandomSample());

//     RandomSampleParams params;
//     params.rangeY.min = 0;
//     params.rangeY.max = 100;
//     params.rangeX.start = 0;
//     params.rangeX.stop = 99;
//     params.rangeX.step = 1;
//     params.rangeX.points = 100;
//     params.rangeX.useStep = false;

//     auto dataSource = new RandomSampleDataSource(params);
//     auto graph = new Graph(dataSource);
//     graph->refreshData(true);
//     addGraph(graph);
// }

void PlotWidget::addRandomSampleLine()
{
    RandomSampleParams params;
    params.rangeY.min = 0;
    params.rangeY.max = 100;
    params.rangeX.start = 0;
    params.rangeX.stop = 99;
    params.rangeX.step = 1;
    params.rangeX.points = 100;
    params.rangeX.useStep = false;

    auto editorX = new RangeEditor;
    editorX->setRange(params.rangeX);

    auto editorY = new MinMaxEditor;
    editorY->setValue(params.rangeY);

    auto editor = Ori::Layouts::LayoutV(
                      {
                          Ori::Layouts::LayoutV({editorX}).makeGroupBox("X"),
                          Ori::Layouts::LayoutV({editorY}).makeGroupBox("Y"),
                      })
                      .setMargin(0)
                      .makeWidgetAuto();

    if (Ori::Dlg::Dialog(editor.get(), false)
            .withTitle(tr("Random Sample Params"))
            .withContentToButtonsSpacingFactor(3)
            .withVerification([editorX]
                              { return editorX->range().verify(); })
            .exec())
    {
        params.rangeX = editorX->range();
        params.rangeY = editorY->value();

        auto dataSource = new RandomSampleDataSource(params);
        auto graph = new Graph(dataSource);
        graph->refreshData(true);
        addGraph(graph);
    }
}

void PlotWidget::createColorScale()
{
    _colorScale = new QCPColorScale(_plot.get());
    auto colorAxis = _colorScale->axis();
    auto plotArea = _plot->axisRectRC();
    _plot->plotLayout()->addElement(plotArea.row, plotArea.col + 1, _colorScale);

    // Register variables for color axis, not for the color scale itself.
    // This allows to use built-in axisTextDlg() function
    // colorScaleFormatDlg() also searches vars for scale->axis(), not for the scale itself
    auto getWndW = [this]
    { return QString::number(width()); };
    auto getWndH = [this]
    { return QString::number(height()); };
    _plot->addTextVar(colorAxis, "{force_var}", "Text var 2", []
                      { return "May the Force be with you"; });
    _plot->addTextVar(colorAxis, "{unit_1}", "Unit of measurement 1", []
                      { return "mm"; });
    _plot->addTextVar(colorAxis, "{unit_2}", "Unit of measurement 2", []
                      { return "kg"; });
    _plot->addTextVar(colorAxis, "{wndW}", "Main window width", getWndW);
    _plot->addTextVar(colorAxis, "{wndH}", "Main window height", getWndH);

    auto menu = new QMenu(this);
    menu->addAction("Format...", _plot.get(), [this]
                    { _plot->colorScaleFormatDlg(_colorScale); });
    menu->addAction("Text...", _plot.get(), [this, colorAxis]
                    { _plot->axisTextDlg(colorAxis); });
    menu->addAction("Copy format", this, [this]()
                    { QCPL::copyColorScaleFormat(_colorScale); });
    menu->addAction("Paste format", this, [this]()
                    {
        auto err = QCPL::pasteColorScaleFormat(_colorScale);
        if (err.isEmpty())
        {
            qDebug() << "Color scale format pasted";
            _plot->replot();
        }
        else Ori::Dlg::error(err); });
    _plot->menus[_colorScale] = menu;
    _plot->axisIdents[colorAxis] = "Color Scale";
}

void PlotWidget::addRandomSampleColormap()
{
    if (!_colorScale)
        createColorScale();

    auto graph = new QCPColorMap(_plot->xAxis, _plot->yAxis);
    graph->setName(OriPetname::make());
    graph->setColorScale(_colorScale);

    const int countX = 100;
    const int countY = 50;
    double offsetX = _colorMapsCount * countX;
    double offsetY = _colorMapsCount * countY;
    _colorMapsCount++;

    auto data = graph->data();
    data->setSize(countX, countY);
    data->setRange({offsetX, offsetX + countX - 1.0}, {offsetY, offsetY + countY - 1.0});
    for (int y = 0; y < countY; y++)
    {
        auto line = QCPL::makeRandomSample(countX);
        for (int x = 0; x < countX; x++)
            data->setCell(x, y, line.y[x]);
    }
    _plot->replot();
}

void PlotWidget::resizeEvent(QResizeEvent *event)
{
    QWidget::resizeEvent(event);

    // Example of usage of text variables
    // Update texts if they use variable referring some
    // dynamically changed objects (the window size in this example)
    _plot->updateTexts();
}

void PlotWidget::savePlotFormat()
{
    auto fileName = QFileDialog::getSaveFileName(
        this, "Save Plot Format", recentFormatFile, "JSON files (*.json)\nAll files (*.*)");
    if (fileName.isEmpty())
        return;
    recentFormatFile = fileName;
    QString err = QCPL::saveFormatToFile(fileName, _plot.get());
    if (!err.isEmpty())
        Ori::Dlg::error(err);
}

void PlotWidget::loadPlotFormat()
{
    auto fileName = QFileDialog::getOpenFileName(
        this, "Load Plot Format", recentFormatFile, "JSON files (*.json)\nAll files (*.*)");
    if (fileName.isEmpty())
        return;
    recentFormatFile = fileName;

    QCPL::JsonReport report;
    auto err = QCPL::loadFormatFromFile(fileName, _plot.get(), &report);
    if (!err.isEmpty())
    {
        // The plot definitely has not been changed
        Ori::Dlg::error(err);
        return;
    }

    // The plot probably has not been changed, this can be clarified by examining the report
    _plot->replot();

    // In real app these messages could be shown in app log window, for example
    foreach (auto err, report)
        qDebug() << err.message;
}

void PlotWidget::pasteLegendFormat()
{
    auto err = QCPL::pasteLegendFormat(_plot->legend);
    if (err.isEmpty())
    {
        qDebug() << "Legend format pasted";
        _plot->replot();
    }
    else
        Ori::Dlg::error(err);
}

void PlotWidget::pasteTitleFormat()
{
    auto err = QCPL::pasteTitleFormat(_plot->title());
    if (err.isEmpty())
    {
        qDebug() << "Title format pasted";
        // This is a context menu command and it's done on visible element.
        // It's not expected that element gets hidden when its format pasted.
        // So the `QCPL::pasteTitleFormat()` doesn't change visibility
        // and there is not need to call `Plot::updateTitleVisibility()`
        _plot->replot();
    }
    else
        Ori::Dlg::error(err);
}

void PlotWidget::pasteAxisFormat(QCPAxis *axis)
{
    auto err = QCPL::pasteAxisFormat(axis);
    if (err.isEmpty())
    {
        qDebug() << "Axis format pasted";
        _plot->replot();
    }
    else
        Ori::Dlg::error(err);
}
