#include "wizardeqpment.h"
#include "ui/panelequipment/wizardpageeqpment.h"


#include <QLabel>
#include <QVBoxLayout>

WizardUiEquipment::WizardUiEquipment(QWidget *parent)
    : QWizard(parent)
{
    setWindowTitle( "设备-向导页面");

    // 添加页面
    WizardPageEquipment *page1 = new WizardPageEquipment();
    addPage(page1);

    WizardPageEquipmentConnect *page2 = new WizardPageEquipmentConnect;
    addPage(page2);

    //去掉帮助按钮
    setWindowFlags(windowFlags()&~Qt::WindowContextHelpButtonHint);

    //设置导航样式
    setWizardStyle( QWizard::ModernStyle );

    // 隐藏取消按钮
    setOptions(QWizard::NoCancelButton);


    // 设置上一步、下一步按钮
    // setButtonText(QWizard::BackButton, "上一步");
    // setButtonText(QWizard::NextButton, "下一步");
    // setButtonText(QWizard::FinishButton, "完成");

    int nWidth = 590;
    int nHeight = 500;
    if (parent != nullptr)
    {
        setGeometry(parent->x() + parent->width()/2 - nWidth/2,
                    parent->y() + parent->height()/2 - nHeight/2,
                    nWidth, nHeight);
    }
    else
    {
        resize(nWidth, nHeight);
    }
}
