#ifndef UIEQUIPMENTLIST_H
#define UIEQUIPMENTLIST_H

/******************************************************************************
  File Name     : panelequipment.h
  Version       : 1.0
  Author        : 
  Created       : 2024-04-16
  Last Modified :
  Description   : 左侧设备管理面板的设备列表
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QListWidget>
#include <QStyledItemDelegate>
#include <QWidget>


class UiEquipmentListItemBar : public QWidget
{
    Q_OBJECT
public:
    explicit UiEquipmentListItemBar(const QString& id, QWidget *parent = nullptr);

protected:
    QString m_id;

signals:
    void equipConnect(QString, bool);
    void equipDelete(QString);
};


class UiEquipmentListItemRect : public QWidget
{
    Q_OBJECT
public:
    explicit UiEquipmentListItemRect(const QString& title, const QString& icon, const QString& id, QWidget *parent = nullptr);

    UiEquipmentListItemBar  * m_toolbar;


signals:
};


class UiEquipmentListItem : public QWidget
{
    Q_OBJECT
public:
    explicit UiEquipmentListItem(const QString& title, const QString& icon, const QString& id, QWidget *parent = nullptr);

public:
    QString m_id;
    QString m_title;

signals:
};


class UiEquipmentList : public QListWidget
{
    Q_OBJECT
public:
    explicit UiEquipmentList(QWidget *parent = nullptr);

    void AddEquipment(const QString& title, const QString& icon, const QString& id);

    QListWidgetItem *findItemByID(const QString& id);
protected:
    void resizeEvent(QResizeEvent *event) override;

public slots:
    void connectEquip(const QString&, bool);
    void deleteEquip(const QString&);

signals:
};

#endif // UIEQUIPMENTLIST_H
