#include "qwiringsetting.h"
#include "widgets/switchbutton.h"

#include <app/settings.h>

#include <QHBoxLayout>
#include <QLabel>

QWiringSetting::QWiringSetting(QWidget *parent)
    : QWidget{parent}
{
    auto gridLayout = new QGridLayout(this);

    QVector<int> margins = YamlConfigUI::instance().getArray<int>("ui_porp_content.margins");
    if(margins.size() == 4)
    {
        gridLayout->setContentsMargins(margins[0], margins[1], margins[2], margins[3]);
    }

    auto vspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.vspacing");
    auto hspacing = YamlConfigUI::instance().getValue<int>("ui_porp_content.hspacing");

    gridLayout->setVerticalSpacing(vspacing);
    gridLayout->setHorizontalSpacing(hspacing);

    auto lineCell = new QLabel("单元");                    //单元

    auto hlayout = new QHBoxLayout();

    m_btn1 = new QPushButton("1");
    m_btn2 = new QPushButton("2");
    m_btn3 = new QPushButton("3");
    m_btn4 = new QPushButton("4");
    m_btn5 = new QPushButton("5");
    m_btn6 = new QPushButton("6");
    m_btn7 = new QPushButton("7");

    hlayout->addWidget(m_btn1);
    hlayout->addWidget(m_btn2);
    hlayout->addWidget(m_btn3);
    hlayout->addWidget(m_btn4);
    hlayout->addWidget(m_btn5);
    hlayout->addWidget(m_btn6);
    hlayout->addWidget(m_btn7);
    hlayout->addStretch(1);

    auto lbLineType = new QLabel("接线方式");                    //接线方式
    auto lbIsAloneSetting = new QLabel("是否单独配置");           //是否单独配置
    m_swichIsAloneSetting = new SwitchButton;

    gridLayout->addWidget(lineCell, 0, 0);
    gridLayout->addLayout(hlayout, 0, 1);


    gridLayout->addWidget(lbLineType, 1, 0);

    gridLayout->addWidget(lbIsAloneSetting, 2, 0);
    gridLayout->addWidget(m_swichIsAloneSetting, 2, 1);

    gridLayout->setColumnStretch(3, 1);
}
