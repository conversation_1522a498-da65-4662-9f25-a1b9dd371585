
#ifndef SLIDINGSTACKEDWIDGET_H
#define SLIDINGSTACKEDWIDGET_H

/******************************************************************************
  File Name     : animatedsplashscreen.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 带滑动动画的StackedWidget
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QStackedWidget>
#include <QEasingCurve>
#include <QParallelAnimationGroup>

class SlidingStackedWidget : public QStackedWidget
{

    Q_OBJECT
public:
    //! Animation direction
    enum t_direction
    {
        LEFT2RIGHT,
        RIGHT2LEFT,
        TOP2BOTTOM,
        BOTTOM2TOP,
        AUTOMATIC
    };
    SlidingStackedWidget(QWidget *parent);

public slots:
    void setSpeed(int speed);
    void setAnimation(enum QEasingCurve::Type animationtype);
    void setVerticalMode(bool vertical = true);
    void setWrap(bool wrap);
    bool slideInNext();
    bool slideInPrev();
    void slideInIdx(int idx, enum t_direction direction=AUTOMATIC);
    void slideInWgt(QWidget * widget, enum t_direction direction=AUTOMATIC);

signals:
    void animationFinished(void);

protected slots:
    void animationDoneSlot(void);

protected:
    int m_speed;
    enum QEasingCurve::Type m_animationtype;
    bool m_vertical;
    int m_now;
    int m_next;
    bool m_wrap;
    QPoint m_pnow;
    bool m_active;
};
#endif // SLIDINGSTACKEDWIDGET_H
