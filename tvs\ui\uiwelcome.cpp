#include "uiwelcome.h"
#include "ui/project/uirecentfiles.h"
#include "widgets/stdwidget.h"
#include "ui/project/uipjnew.h"
#include "widgets/uirichlist.h"

#include <QLabel>
#include <QListWidget>
#include <QPushButton>
#include <QSplitter>
#include <QVBoxLayout>





uiWelcome::uiWelcome(QWidget *parent)
    : QWidget{parent}
{
    // 创建垂直布局
    QVBoxLayout *mainLayout = new QVBoxLayout(this);

    // 创建标题
    auto *titleLabel = new LabelH2("欢迎使用，您可以执行如下操作：");
    mainLayout->addWidget(titleLabel);

    // 创建面板布局
    QHBoxLayout *panelLayout = new QHBoxLayout();
    mainLayout->addLayout(panelLayout, 1);

    // 创建左边的栅格布局
    auto uiNew = new UiPjNewGrid();
    panelLayout->addWidget(uiNew);

    // 设置分隔条
    auto splitter = new QPushButton();
    splitter->setObjectName("splitter");
    splitter->setFocusPolicy(Qt::NoFocus);
    splitter->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Expanding);
    // splitter->setMaximumWidth(1);
    // splitter->setMinimumHeight(500);
    // splitter->setSizes(QList<int>() << 200 << 400);
    panelLayout->addWidget(splitter, 0, Qt::AlignTop);


    // 最近打开文件列表
    auto recentFiles = new UiRecentFiles();
    panelLayout->addWidget(recentFiles, 1);
}
