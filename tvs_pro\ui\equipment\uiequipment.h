#ifndef UIEQUIPMENT_H
#define UIEQUIPMENT_H

/******************************************************************************
  File Name     : uidata.h
  Version       : 1.0
  Author        :
  Created       : 2024-04-16
  Last Modified :
  Description   : 设备查看主视图
  Function List :
  History       :
  1.Date        : 2024-04-16
    Author      :
    Modification: Created file

******************************************************************************/

#include <QWidget>
class UiEquipWave;
class UiData;
class UiStatus;
class QLabel;

class UiEquipment : public QWidget
{
    Q_OBJECT
public:
    explicit UiEquipment(const QString& id, QWidget *parent = nullptr);

    bool isUiDataVisiable() const;
    void setUiDataVisiable(bool visible);

protected:
    QString m_id;
    UiEquipWave * m_uiWave;
    UiData * m_uiData;
    UiStatus * m_uiStatus;
    QLabel* m_info;

signals:

    // QWidget interface
protected:
    virtual void resizeEvent(QResizeEvent *event) override;
};

#endif // UIEQUIPMENT_H
